#include "prodebpayment.h"
#include "jsonbuilder.h"
#include "webutils.h"
#include "qxtjson.h"
#include "globalutils.h"
#include <QSettings>
#include "../log4qt/ilogmsg.h"
#include <QCryptographicHash>
#include <QNetworkRequest>
#include <QHttpMultiPart>
#include <QHttpPart>
#include <QTimer>
#include "laneinfo.h"

ProDebPayment::ProDebPayment(QObject *parent) : QObject(parent) 
{ 
    m_networkManager = new QNetworkAccessManager(this);
    // 使用旧式的Qt 4.8.5 SIGNAL/SLOT连接语法
    connect(m_networkManager, SIGNAL(finished(QNetworkReply*)), 
            this, SLOT(onNetworkReplyFinished(QNetworkReply*)));
}

void ProDebPayment::SetOrgInfo(const QString &sStationId, const QString &sLaneId,
                           const QString &sStationName)
{
    m_sTollLaneId = sLaneId;
    m_sTollStationId = sStationId;
    m_sTollStationName = sStationName;
    m_sFileName = GetCurrentPath() + QString("lane.ini");
    setObjectName(QString("ProDebPayment"));
    LoadCfgFromFile();
}

bool ProDebPayment::RunOnce()
{
    return true;
}

// 省内追收名单补缴查询
bool ProDebPayment::QueryDebtInfo(const QString &sVlp, int nVlpc, DebtQueryResult &result)
{
    DebtRequest request = CreateBaseRequest(sVlp, nVlpc);
    
    CJsonBuilder builder;
    builder.AddKeyValue(QString("requestId"), request.requestId);
    builder.AddKeyValue(QString("vlp"), request.vlp);
    builder.AddKeyValue_Int(QString("vlpc"), request.vlpc);
    builder.AddKeyValue(QString("station"), request.station);
    builder.AddKeyValue(QString("laneid"), request.laneid);
    builder.AddKeyValue(QString("listno"), QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz"));
    
    QString sJson = QString("{%1}").arg(builder.CreateJsonStr().remove(0, 1));
    InfoLog(QString("省内追收名单补缴查询请求: %1").arg(sJson));
    
    QString sResponse;
    if (!SendHttpRequest("debdic", sJson, sResponse)) {
        return false;
    }
    
    return ParseQueryReturnMsg(sResponse, result, false);
}

// 省内追收名单补缴明细查询
bool ProDebPayment::QueryDebtDetail(const QString &sVlp, int nVlpc, DebtQueryResult &result)
{
    DebtRequest request = CreateBaseRequest(sVlp, nVlpc);
    
    CJsonBuilder builder;
    builder.AddKeyValue(QString("requestId"), request.requestId);
    builder.AddKeyValue(QString("vlp"), request.vlp);
    builder.AddKeyValue_Int(QString("vlpc"), request.vlpc);
    builder.AddKeyValue(QString("station"), request.station);
    builder.AddKeyValue(QString("laneid"), request.laneid);
    builder.AddKeyValue(QString("listno"), QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz"));
    
    QString sJson = QString("{%1}").arg(builder.CreateJsonStr().remove(0, 1));
    InfoLog(QString("省内追收名单补缴明细查询请求: %1").arg(sJson));
    
    QString sResponse;
    if (!SendHttpRequest("deblist", sJson, sResponse)) {
        return false;
    }
    
    return ParseQueryReturnMsg(sResponse, result, true);
}

// 车道省内追收名单补缴完成通知
bool ProDebPayment::NotifyDebtComplete(const QString &sVlp, int nVlpc, const QString &sOweFee, 
                                     const QString &sWasteId, const QString &sListno, 
                                     DebtQueryResult &result)
{
    DebtRequest request = CreateBaseRequest(sVlp, nVlpc, sListno);
    request.oweFee = sOweFee;
    request.wasteid = sWasteId;
    
    CJsonBuilder builder;
    builder.AddKeyValue(QString("requestId"), request.requestId);
    builder.AddKeyValue(QString("vlp"), request.vlp);
    builder.AddKeyValue_Int(QString("vlpc"), request.vlpc);
    builder.AddKeyValue(QString("oweFee"), request.oweFee);
    builder.AddKeyValue(QString("station"), request.station);
    builder.AddKeyValue(QString("wasteid"), request.wasteid);
    builder.AddKeyValue(QString("laneid"), request.laneid);
    builder.AddKeyValue(QString("listno"), request.listno);
    
    QString sJson = QString("{%1}").arg(builder.CreateJsonStr().remove(0, 1));
    InfoLog(QString("车道省内追收名单补缴完成通知请求: %1").arg(sJson));
    
    QString sResponse;
    if (!SendHttpRequest("debok", sJson, sResponse)) {
        return false;
    }
    
    return ParseNotifyReturnMsg(sResponse, result);
}

// 车道当趟补费确认接口
bool ProDebPayment::ConfirmCurrentDebt(const QString &sVlp, int nVlpc, const QString &sOweFee, 
                                     DebtQueryResult &result)
{
    DebtRequest request = CreateBaseRequest(sVlp, nVlpc);
    request.oweFee = sOweFee;
    
    CJsonBuilder builder;
    builder.AddKeyValue(QString("requestId"), request.requestId);
    builder.AddKeyValue(QString("vlp"), request.vlp);
    builder.AddKeyValue_Int(QString("vlpc"), request.vlpc);
    builder.AddKeyValue(QString("oweFee"), request.oweFee);
    builder.AddKeyValue(QString("station"), request.station);
    builder.AddKeyValue(QString("laneid"), request.laneid);
    
    QString sJson = QString("{%1}").arg(builder.CreateJsonStr().remove(0, 1));
    InfoLog(QString("车道当趟补费确认请求: %1").arg(sJson));
    
    QString sResponse;
    if (!SendHttpRequest("latest", sJson, sResponse)) {
        return false;
    }
    
    return ParseNotifyReturnMsg(sResponse, result);
}

// 异步省内追收名单补缴查询
void ProDebPayment::QueryDebtInfoAsync(const QString &sVlp, int nVlpc)
{
    DebtRequest request = CreateBaseRequest(sVlp, nVlpc);
    
    CJsonBuilder builder;
    builder.AddKeyValue(QString("requestId"), request.requestId);
    builder.AddKeyValue(QString("vlp"), request.vlp);
    builder.AddKeyValue_Int(QString("vlpc"), request.vlpc);
    builder.AddKeyValue(QString("station"), request.station);
    builder.AddKeyValue(QString("laneid"), request.laneid);
    builder.AddKeyValue(QString("listno"), QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz"));
    
    QString sJson = QString("{%1}").arg(builder.CreateJsonStr().remove(0, 1));
    InfoLog(QString("异步省内追收名单补缴查询请求: %1").arg(sJson));
    
    SendHttpRequestAsync("debdic", sJson, RT_QUERY_INFO);
}

// 异步省内追收名单补缴明细查询
void ProDebPayment::QueryDebtDetailAsync(const QString &sVlp, int nVlpc)
{
    DebtRequest request = CreateBaseRequest(sVlp, nVlpc);
    
    CJsonBuilder builder;
    builder.AddKeyValue(QString("requestId"), request.requestId);
    builder.AddKeyValue(QString("vlp"), request.vlp);
    builder.AddKeyValue_Int(QString("vlpc"), request.vlpc);
    builder.AddKeyValue(QString("station"), request.station);
    builder.AddKeyValue(QString("laneid"), request.laneid);
    builder.AddKeyValue(QString("listno"), QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz"));
    
    QString sJson = QString("{%1}").arg(builder.CreateJsonStr().remove(0, 1));
    InfoLog(QString("异步省内追收名单补缴明细查询请求: %1").arg(sJson));
    
    SendHttpRequestAsync("deblist", sJson, RT_QUERY_DETAIL);
}

// 异步车道省内追收名单补缴完成通知
void ProDebPayment::NotifyDebtCompleteAsync(const QString &sVlp, int nVlpc, const QString &sOweFee, 
                                          const QString &sWasteId, const QString &sListno)
{
    DebtRequest request = CreateBaseRequest(sVlp, nVlpc, sListno);
    request.oweFee = sOweFee;
    request.wasteid = sWasteId;
    
    CJsonBuilder builder;
    builder.AddKeyValue(QString("requestId"), request.requestId);
    builder.AddKeyValue(QString("vlp"), request.vlp);
    builder.AddKeyValue_Int(QString("vlpc"), request.vlpc);
    builder.AddKeyValue(QString("oweFee"), request.oweFee);
    builder.AddKeyValue(QString("station"), request.station);
    builder.AddKeyValue(QString("wasteid"), request.wasteid);
    builder.AddKeyValue(QString("laneid"), request.laneid);
    builder.AddKeyValue(QString("listno"), request.listno);
    
    QString sJson = QString("{%1}").arg(builder.CreateJsonStr().remove(0, 1));
    InfoLog(QString("异步车道省内追收名单补缴完成通知请求: %1").arg(sJson));
    
    SendHttpRequestAsync("debok", sJson, RT_NOTIFY_COMPLETE);
}

// 异步车道当趟补费确认接口
void ProDebPayment::ConfirmCurrentDebtAsync(const QString &sVlp, int nVlpc, const QString &sOweFee)
{
    DebtRequest request = CreateBaseRequest(sVlp, nVlpc);
    request.oweFee = sOweFee;
    
    CJsonBuilder builder;
    builder.AddKeyValue(QString("requestId"), request.requestId);
    builder.AddKeyValue(QString("vlp"), request.vlp);
    builder.AddKeyValue_Int(QString("vlpc"), request.vlpc);
    builder.AddKeyValue(QString("oweFee"), request.oweFee);
    builder.AddKeyValue(QString("station"), request.station);
    builder.AddKeyValue(QString("laneid"), request.laneid);
    
    QString sJson = QString("{%1}").arg(builder.CreateJsonStr().remove(0, 1));
    InfoLog(QString("异步车道当趟补费确认请求: %1").arg(sJson));
    
    SendHttpRequestAsync("latest", sJson, RT_CONFIRM_CURRENT);
}

// 异步HTTP请求方法
void ProDebPayment::SendHttpRequestAsync(const QString &sApiPath, const QString &sJson, int requestType)
{
    QString sUrl = m_sBaseUrl + "/getProDebDIc/" + sApiPath;
    QUrl url(sUrl);
    
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    
    // 保存请求类型到请求属性，以便在响应处理时识别
    // 使用Qt 4.8.5兼容的Attribute方式
    request.setAttribute(QNetworkRequest::Attribute(QNetworkRequest::User + 1), requestType);
    
    // 发送POST请求（异步）
    QByteArray postData = sJson.toUtf8();
    QNetworkReply *reply = m_networkManager->post(request, postData);
    // 关键点：不要在此处执行任何阻塞操作（如循环等待、processEvents），
    // 仅依赖 QNetworkAccessManager::finished 信号回调处理结果

    // 设置超时（10秒）
    QTimer::singleShot(10000, reply, SLOT(abort()));

    // 记录请求ID，用于后续识别
    InfoLog(QString("发送异步请求: %1, 请求类型: %2").arg(sApiPath).arg(requestType));
}

// 网络响应处理槽函数
void ProDebPayment::onNetworkReplyFinished(QNetworkReply *reply)
{
    reply->deleteLater();
    
    // 请求类型 - 使用Qt 4.8.5兼容的方式获取
    int requestType = reply->request().attribute(QNetworkRequest::Attribute(QNetworkRequest::User + 1)).toInt();
    
    // 处理可能的错误
    if (reply->error() != QNetworkReply::NoError) {
        QString errorType;
        switch (reply->error()) {
            case QNetworkReply::TimeoutError:
                errorType = "请求超时";
                break;
            case QNetworkReply::ConnectionRefusedError:
                errorType = "连接被拒绝";
                break;
            case QNetworkReply::HostNotFoundError:
                errorType = "主机未找到";
                break;
            default:
                errorType = reply->errorString();
                break;
        }
        
        ErrorLog(QString("补费接口网络请求错误 (类型:%1): %2")
                .arg(requestType)
                .arg(errorType));
        
        // 根据请求类型发送相应的失败信号
        DebtQueryResult emptyResult;
        
        switch (requestType) {
            case RT_QUERY_INFO:
                emit QueryDebtInfoFinished(false, emptyResult);
                break;
            case RT_QUERY_DETAIL:
                emit QueryDebtDetailFinished(false, emptyResult);
                break;
            case RT_NOTIFY_COMPLETE:
                emit NotifyDebtCompleteFinished(false, emptyResult);
                break;
            case RT_CONFIRM_CURRENT:
                emit ConfirmCurrentDebtFinished(false, emptyResult);
                break;
        }
        
        return;
    }
    
    // 读取响应数据
    QByteArray responseData = reply->readAll();
    QString sResponse = QString::fromUtf8(responseData);
    InfoLog(QString("异步补费接口返回: %1").arg(sResponse));
    
    // 解析响应并发送相应信号
    bool success = false;
    DebtQueryResult result;
    
    switch (requestType) {
        case RT_QUERY_INFO:
            success = ParseQueryReturnMsg(sResponse, result, false);
            emit QueryDebtInfoFinished(success, result);
            break;
        case RT_QUERY_DETAIL:
            success = ParseQueryReturnMsg(sResponse, result, true);
            emit QueryDebtDetailFinished(success, result);
            break;
        case RT_NOTIFY_COMPLETE:
        case RT_CONFIRM_CURRENT:
            success = ParseNotifyReturnMsg(sResponse, result);
            if (requestType == RT_NOTIFY_COMPLETE) {
                emit NotifyDebtCompleteFinished(success, result);
            } else {
                emit ConfirmCurrentDebtFinished(success, result);
            }
            break;
    }
}

// 创建基础请求参数
DebtRequest ProDebPayment::CreateBaseRequest(const QString &sVlp, int nVlpc, const QString &sListno)
{
    DebtRequest request;
    
    // 根据文档，requestId需要使用VLP+36001 MD5加密
    QString reqIdInput = sVlp + "36001";
    QByteArray md5 = QCryptographicHash::hash(reqIdInput.toUtf8(), QCryptographicHash::Md5).toHex();
    
    request.requestId = md5;
    request.vlp = sVlp;
    request.vlpc = nVlpc;
    request.station = m_sTollStationId;
    request.laneid = m_sTollLaneId;
    request.listno = sListno.isEmpty() ? QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz") : sListno;
    
    return request;
}

// 发送HTTP请求的通用方法
bool ProDebPayment::SendHttpRequest(const QString &sApiPath, const QString &sJson, QString &sResponse)
{
    QByteArray bData = sJson.toUtf8();
    QString sFileName = QString("ProDebPayment_%1_%2.json")
                            .arg(m_sTollLaneId)
                            .arg(QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz"));

    QString sUrl = m_sBaseUrl + "/getProDebDIc/" + sApiPath;
    
    // 记录请求开始时间
    QDateTime startTime = QDateTime::currentDateTime();
    InfoLog(QString("开始发送补费接口请求: %1").arg(sApiPath));
    
    DataSyncResponse retVal;
    WebUtils::UploadWaste(retVal, sFileName, bData, sUrl);
    sResponse = QString::fromUtf8(retVal.Content.constData(), retVal.Content.size());
    
    // 计算请求耗时
    qint64 elapsedMs = startTime.msecsTo(QDateTime::currentDateTime());
    InfoLog(QString("补费接口 %1 返回 (耗时:%2ms): %3").arg(sApiPath).arg(elapsedMs).arg(sResponse));
    
    if (retVal.HttpStatusCode != 200) {
        ErrorLog(QString("补费接口 %1 HTTP错误: %2").arg(sApiPath).arg(retVal.HttpStatusCode));
        return false;
    }
    
    return true;
}

// 解析查询返回消息
bool ProDebPayment::ParseQueryReturnMsg(const QString &sContent, DebtQueryResult &result, bool bWithDetail)
{
    QVariantMap retMap = QxtJSON::parse(sContent).toMap();
    if (retMap.isEmpty()) {
        WarnLog("查询返回数据为空");
        return false;
    }
    
    // 解析基本字段
    result.flag = retMap.value("flag").toString();
    result.listno = retMap.value("listno").toString();
    result.oweFee = retMap.value("oweFee").toString();
    result.evasionCount = retMap.value("evasionCount").toString();
    result.orderids = retMap.value("orderids").toString();
    
    DebugLog(QString("解析查询结果 - 标志:%1, 编号:%2, 欠费:%3, 条数:%4")
             .arg(result.flag)
             .arg(result.listno)
             .arg(result.oweFee)
             .arg(result.evasionCount));
    
    // 如果请求的是详细信息，还需解析debtlist
    if (bWithDetail && retMap.contains("debtlist")) {
        QVariantList debtList = retMap.value("debtlist").toList();
        foreach (QVariant varData, debtList) {
            QVariantMap debtMap = varData.toMap();
            DebtListItem item;
            
            item.passid = debtMap.value("passid").toString();
            item.orderid = debtMap.value("orderid").toString();
            item.enTollStationName = debtMap.value("enTollStationName").toString();
            item.entime = debtMap.value("entime").toString();
            item.extollStationName = debtMap.value("extollStationName").toString();
            item.extime = debtMap.value("extime").toString();
            item.recoveryType = debtMap.value("recoveryType").toString();
            item.mediaType = debtMap.value("mediaType").toString();
            item.mediaNo = debtMap.value("mediaNo").toString();
            item.payFee = debtMap.value("payFee").toString();
            item.fee = debtMap.value("fee").toString();
            item.oweFee = debtMap.value("oweFee").toString();
            item.calculationBasis = debtMap.value("calculationBasis").toString();
            item.realVehicleType = debtMap.value("realVehicleType").toString();
            item.realVehicleClass = debtMap.value("realVehicleClass").toString();
            item.realAxleCount = debtMap.value("realAxleCount").toString();
            
            // 解析证据
            if (debtMap.contains("audEvidence")) {
                QVariantList evidences = debtMap.value("audEvidence").toList();
                foreach (QVariant evidence, evidences) {
                    QVariantMap evidenceMap = evidence.toMap();
                    if (evidenceMap.contains("url")) {
                        item.evidenceUrls.append(evidenceMap.value("url").toString());
                    }
                }
            }
            
            result.debtlist.append(item);
        }
    }
    
    return result.flag == "1"; // 1-查询成功
}

// 解析通知返回消息
bool ProDebPayment::ParseNotifyReturnMsg(const QString &sContent, DebtQueryResult &result)
{
    QVariantMap retMap = QxtJSON::parse(sContent).toMap();
    if (retMap.isEmpty()) {
        WarnLog("通知返回数据为空");
        return false;
    }
    
    result.flag = retMap.value("flag").toString();
    result.listno = retMap.value("listno").toString();
    
    return result.flag == "1"; // 1-接收成功
}

// 加载配置文件
bool ProDebPayment::LoadCfgFromFile()
{
    // 使用laneinfo获取URL配置
    m_sBaseUrl = Ptr_Info->GetProDebPaymentUrl();
    if (m_sBaseUrl.isEmpty()) {
        ErrorLog("未配置省内补费接口URL");
        return false;
    }
    
    InfoLog(QString("省内补费接口配置加载成功 - URL: %1, 车道ID: %2, 收费站ID: %3")
            .arg(m_sBaseUrl)
            .arg(m_sTollLaneId)
            .arg(m_sTollStationId));
    
    return true;
}
