#include "vehplatefunc.h"

#include "globalutils.h"
#include "ilogmsg.h"
#include "lanetype.h"
#include <QStringList>
#include <vector>

//获取车牌号的长度(汉字按一个计算, 例如:鲁A12345 返回为7)
qint32 GetVehPlateLen(const char *szVehPlate)
{
    qint32 i = 0, j = 0, k = 0;

    while (szVehPlate[i]) {
        qint8 tt = szVehPlate[i];
        if (tt < 0) {
            j++;
            i++;
        } else
            k++;

        i++;
    }

    return j + k;
}

/*-----------------------------------------------------------------------------
比较两个车牌号是否逻辑上相同
比较规则：
        1.以两个车牌中较短的为基础,比较车牌后面的位数
        例如：
                车牌1：鲁A12345
                车牌2：12345

                那么只比较12345是否相同。

        2.*号可替代汉字和字母
        例如：
                车牌1：鲁AH888W
                车牌2：**888*

                那么认为两个车牌是相同的
-----------------------------------------------------------------------------*/
bool CompareVehPlate(const char *szVehPlate1, const char *szVehPlate2)
{
    qint32 nLen1, nLen2;
    char szTmpPlate1[12 + 1] = "", szTmpPlate2[12 + 1] = "";  // MAX_VEHPLATE_LEN=12
    qsnprintf(szTmpPlate1, sizeof(szTmpPlate1), "%s", szVehPlate1);
    qsnprintf(szTmpPlate2, sizeof(szTmpPlate2), "%s", szVehPlate2);

    nLen1 = GetVehPlateLen(szTmpPlate1);
    nLen2 = GetVehPlateLen(szTmpPlate2);

    //两个车牌中有一个以上为空时，认为不相等
    if ((nLen1 == 0) || (nLen2 == 0)) return false;

    const signed char *szShortVP, *szLongVP;
    //将较长的指针跳至跟较短指针对齐的位置
    // NOTE: 汉字应跳转2个字节
    if (nLen1 == nLen2) {
        szLongVP = (signed char *)szTmpPlate1;
        szShortVP = (signed char *)szTmpPlate2;
    } else {
        const signed char *szTmpLongVP;
        qint32 i = 0, nSkip = 0;
        if (nLen1 > nLen2) {
            szTmpLongVP = (signed char *)szTmpPlate1;
            szShortVP = (signed char *)szTmpPlate2;
        } else {
            szTmpLongVP = (signed char *)szTmpPlate2;
            szShortVP = (signed char *)szTmpPlate1;
        }

        //指针跳转偏移量
        qint32 nTmpSkip = abs(nLen1 - nLen2);

        for (; nTmpSkip > 0; nTmpSkip--) {
            if (szTmpLongVP[i] < 0) {
                i += 2;
            } else {
                i++;
            }
        }
        nSkip = i;
        szLongVP = szTmpLongVP + i;
    }

    qint32 i = 0, j = 0;
    //逐字节比较
    while (szLongVP[i] && szShortVP[j]) {
        //两个字节不同
        if (szLongVP[i] != szShortVP[j]) {
            if (((szLongVP[i] >= 48) && (szLongVP[i] <= 57)) ||
                ((szShortVP[j] >= 48) && (szShortVP[j] <= 57))) {
                //其中一个是数字
                return FALSE;
            } else {
                //两个都不是数字,忽略星号
                if ((szLongVP[i] != '*') && (szShortVP[j] != '*')) return FALSE;
            }
            //如果是汉字向前跳一个
            if (szLongVP[i] < 0) i++;
            if (szShortVP[j] < 0) j++;
        }
        i++;
        j++;
    }
    return true;
}

QString GetVehPlateColorName(quint8 nVehPlateColor)
{
    switch (nVehPlateColor) {
        case VP_COLOR_BLUE:
            return QObject::tr("蓝");
        case VP_COLOR_YELLOW:
            return QObject::tr("黄");
        case VP_COLOR_BLACK:
            return QObject::tr("黑");
        case VP_COLOR_WHITE:
            return QObject::tr("白");
        case VP_COLOR_LittleGREEN:
            return QObject::tr("渐绿");
        case VP_COLOR_GREEN:
            return QObject::tr("绿");
        case VP_COLOR_TEMP:
            return QObject::tr("临");
        case VP_COLOR_YELLOWGREEN:
            return QObject::tr("黄绿");
        case VP_COLOR_BLUEWHITE:
            return QObject::tr("蓝白");
        case VP_COLOR_RED:
            return QObject::tr("红");
        default:
            return QObject::tr("");
    }
}

void GetFullVehPlate(const char *szVehPlate, quint8 nColor, QString &strFullVehPlate)
{
    strFullVehPlate =
        QString("%1%2").arg(GetVehPlateColorName(nColor)).arg(GB2312toUnicode(szVehPlate));
}

void FullVehPlate2OldInputVehPlate(char *szOldInputVehPlate, const char *szFullVehPlate)
{
    qint32 i = 0;
    qint32 j = 0;
    while (szFullVehPlate[i]) {
        if ((szFullVehPlate[i] < '0') || (szFullVehPlate[i] > '9')) {
            szOldInputVehPlate[j] = '*';
            if ((qint8)szFullVehPlate[i] < 0) {
                i++;
            }
        } else {
            szOldInputVehPlate[j] = szFullVehPlate[i];
        }
        i++;
        j++;
    }
    szOldInputVehPlate[j] = '\0';
    if (j > 5) {
        for (qint32 index1 = 0; index1 < 5; index1++) {
            szOldInputVehPlate[index1] = szOldInputVehPlate[j - 5];
            j++;
        }
        szOldInputVehPlate[5] = '\0';
    }
}

/*
void RemoveSpCharFromPlate(const char *szSrc,char *szDest,qint32 nMaxVehPlateLen)
{
    while((*szSrc != '\0') && (nMaxVehPlateLen>0))
    {
        char sChar = *szSrc;
        if(('0'<=sChar && sChar<='9')
            || ('A'<=sChar && sChar<='Z'))
            //|| ('*'==sChar))
        {
            *szDest = sChar;
        }else if('a'<=sChar && sChar<='z')
        {
            *szDest = sChar-32;
        }else if((sChar)<0)
        {
            if(-80<(sChar))
            {
                *szDest++ =*szSrc++;
                *szDest  = *szSrc;
                nMaxVehPlateLen--;
            }else
            {
                szSrc+=2;
                continue;
            }
        }else{
            szSrc++;
            continue;
        }
        nMaxVehPlateLen--;
        szSrc++;
        szDest++;
    }
    return ;
}*/

void RemovePlateSpecChar(char *szDest, qint32 nDestSize, const char *szSrc,
                         qint32 nMaxVehPlateLen /*=MAX_VEHPLATE_LEN*/)
{
    if (nDestSize < 1 || nMaxVehPlateLen < 1) {
        return;
    }
    memset(szDest, 0, nDestSize);
    // const int nMaxLen=qMin(nDestSize-1,nMaxVehPlateLen-1);
    int nSourceLen = nMaxVehPlateLen - 1;
    int nDestLen = nDestSize - 1;
    int i = 0, j = 0;
    char szTmp[3] = "";
    while (szSrc[i]) {
        if (i > nSourceLen - 1) break;
        memset(szTmp, 0, sizeof szTmp);
        if (('0' <= szSrc[i] && szSrc[i] <= '9') || ('A' <= szSrc[i] && szSrc[i] <= 'Z')) {
            szTmp[0] = szSrc[i];
            ++i;
        } else if ('a' <= szSrc[i] && szSrc[i] <= 'z') {
            //小写字母转为大写
            szTmp[0] = szSrc[i] - 32;
            ++i;
        } else if (0 > szSrc[i]) {
            //去除中文符号
            if (i >= nSourceLen - 1) break;
            quint8 bHigh = quint8(szSrc[i]);
            quint8 bLow = quint8(szSrc[i + 1]);
            if ((0xb0 <= bHigh) && (bHigh <= 0xF7)) {
                if (0xa0 <= bLow) memcpy(szTmp, &szSrc[i], 2);
            }
            i += 2;
        } else {
            i++;
        }

        int n = strlen(szTmp);
        if (0 < n) {
            if (j + n <= nDestLen) {
                memcpy(&szDest[j], szTmp, n);
                j += n;
            } else {
                break;
            }
        }
    }
}

/*
void RemovePlateSpecChar(char *szDest, qint32 nDestSize, const char *szSrc, qint32 nMaxVehPlateLen)
{
        if (nDestSize<1 || nMaxVehPlateLen<1 || strlen(szSrc)<1)
        {
                return;
        }
        memset(szDest,0,nDestSize);
        const qint32 nMaxLen=qMin(nDestSize,nMaxVehPlateLen+1);
        qint32 i=0,j=0;
        char szTmp[3]="";
        while (szSrc[i])
    {
                memset(szTmp,0,sizeof(szTmp));
                //为兼容旧系统,'*'作为正常字符
        if (('0'<=szSrc[i] && szSrc[i]<='9')
            || ('A'<=szSrc[i] && szSrc[i]<='Z')
            || ('*'==szSrc[i]))
                {
            szTmp[0]=szSrc[i];
                }
        else if ('a'<=szSrc[i] && szSrc[i]<='z')
                {
                        //小写字母转为大写
            szTmp[0]=szSrc[i]-32;
                }
        else if (0>((qint8)szSrc[i]))
                {
                        //去除中文符号
            if (-80<=((qint8)szSrc[i]))
                        {
                                memcpy(szTmp,&szSrc[i],2);
                        }
                        else
                        {
                                i+=2;
                                continue;
                        }
                }

                qint32 n=strlen(szTmp);
                if (0<n)
                {
                        if (j<nMaxLen-n)
                        {
                                memcpy(&szDest[j],szTmp,n);
                                i+=n;
                                j+=n;
                        }
                        else
                        {
                                break;
                        }
                }
                else
                {
                        i++;
                }
        }
}*/

quint8 TransVehPlateColor(const QString &sColor)
{
    if (0 == sColor.compare("蓝")) {
        return VP_COLOR_BLUE;
    }
    if (0 == sColor.compare("黄")) {
        return VP_COLOR_YELLOW;
    }
    if (0 == sColor.compare("白")) {
        return VP_COLOR_WHITE;
    }
    if (0 == sColor.compare("黑")) {
        return VP_COLOR_BLACK;
    }

    if (0 == sColor.compare("绿")) {
        return VP_COLOR_GREEN;
    }

    if (0 == sColor.compare("蓝白")) {
        return VP_COLOR_BLUEWHITE;
    }
    if (0 == sColor.compare("黄绿")) {
        return VP_COLOR_YELLOWGREEN;
    }

    if (0 == sColor.compare("红")) return VP_COLOR_RED;

    return VP_COLOR_NONE;
}

// 将车牌识别结果按格式要求进行转换
bool ConvertGDWPlate(quint8 &nColor, QString &sPlate, const QString &sFullPlate)
{
    qint32 len = sFullPlate.length();
    if (len < 2) {
        // 全车牌中没有颜色
        nColor = VP_COLOR_NONE;
        sPlate = sFullPlate;
        return true;
    }
    QString sColor = sFullPlate.mid(0, 2);
    nColor = TransVehPlateColor(sColor);
    if (VP_COLOR_NONE == nColor) {
        // 如果前2个字符不是颜色，假定颜色未识别出
        sPlate = sFullPlate;
        if (0 == sPlate.compare("无车牌")) {
            // 高德威车牌识别设备在没有车牌号时会上送“无车牌”
            sPlate.clear();
        }
    } else {
        sPlate = sFullPlate.mid(2, sFullPlate.length() - 2);
    }
    return true;
}

int GetDefaultVehPlateColorForVehClass(int nVehClass)
{
    switch (nVehClass) {
        case VC_Car1:
            return VP_COLOR_BLUE;
        case VC_Car3:
        case VC_Car4:
        case VC_Truck2:
        case VC_Truck3:
        case VC_Truck4:
        case VC_Truck5:
            return VP_COLOR_YELLOW;
        default:
            return VP_COLOR_NONE;
    }
}

quint8 TransVehPlateColor(const char *sPlateColor, int nSize)
{
    if (nSize != 2) {
        return -1;
    }
    qint16 nColor = 0;
    memcpy(&nColor, sPlateColor, 2);
    nColor = qFromBigEndian(nColor);

    if (nColor >= 0 && nColor < 15) {
        return (quint8)nColor;
    }

    if (-1 == nColor) return VP_COLOR_NONE;

    char szColor[4];
    memset(szColor, 0, sizeof szColor);
    memcpy(szColor, sPlateColor, 2);
    QString sColor = GB2312toUnicode(szColor);

    if (("05" == sColor) || ("黄绿" == sColor)) {
        return VP_COLOR_YELLOWGREEN;
    }

    if (("06" == sColor) || ("蓝白" == sColor)) return VP_COLOR_BLUEWHITE;

    if (("00" == sColor) || ("蓝" == sColor)) {
        return VP_COLOR_BLUE;
    }

    if (("01" == sColor || ("黄") == sColor)) {
        return VP_COLOR_YELLOW;
    }

    if (("02" == sColor) || ("黑" == sColor)) {
        return VP_COLOR_BLACK;
    }

    if (("03" == sColor) || ("白" == sColor)) {
        return VP_COLOR_WHITE;
    }

    if (("04" == sColor) || ("绿" == sColor)) {
        return VP_COLOR_GREEN;
    }

    return VP_COLOR_NONE;
}

bool IsValidVehPlateColor(quint8 bColor)
{
    switch (bColor) {
        case VP_COLOR_BLUE:
        case VP_COLOR_YELLOW:
        case VP_COLOR_BLACK:
        case VP_COLOR_WHITE:
        case VP_COLOR_LittleGREEN:
        case VP_COLOR_YELLOWGREEN:
        case VP_COLOR_BLUEWHITE:
        case VP_COLOR_TEMP:
        case VP_COLOR_GREEN:
        case VP_COLOR_RED:
            return true;
            break;
        default:
            break;
    }
    return false;
}

QStringList prepareStrings(const QString &str1, const QString &str2)
{
    // 去除空格并补齐长度
    QString trimmedStr1 = str1.simplified();
    QString trimmedStr2 = str2.simplified();

    // 如果长度差大于2，直接返回失败
    if (qAbs(trimmedStr1.length() - trimmedStr2.length()) > 2) {
        return QStringList();
    }

    // 如果长度差为1，补齐长度
    if (trimmedStr1.length() < trimmedStr2.length()) {
        trimmedStr1 = trimmedStr1.leftJustified(trimmedStr2.length(), ' ');
    } else if (trimmedStr1.length() > trimmedStr2.length()) {
        trimmedStr2 = trimmedStr2.leftJustified(trimmedStr1.length(), ' ');
    }
    QStringList sList;
    sList.push_back(trimmedStr1);
    sList.push_back(trimmedStr2);
    return sList;
    // return QStringList{trimmedStr1, trimmedStr2};
}

/**
 * @brief
 * 比较车牌，如果长度相差2个字符，返回失败
 * 否则补齐后比较，如果相同，返回1，否则去掉前两个字符比较，如果相同返回2，如果超过一个字符不符，返回0，否则返回3
 * 该函数只用于通过车牌查询车型
 * @param
 * @return
 */
int compareVlp(const QString &str1, const QString &str2)
{
    QStringList preparedStrings = prepareStrings(str1, str2);
    if (preparedStrings.isEmpty()) {
        return 0;
    }

    QString preparedStr1 = preparedStrings[0];
    QString preparedStr2 = preparedStrings[1];

    // 第一次全量比较
    if (preparedStr1 == preparedStr2) {
        return 1;
    }

    // 第二次比较丢弃前两个字符后的字符串
    QString subStr1 = preparedStr1.mid(2);
    QString subStr2 = preparedStr2.mid(2);
    if (subStr1 == subStr2) {
        return 2;
    }

    // 第三次按位置比较字符
    int differences = 0;
    for (int i = 0; i < qMin(preparedStr1.length(), preparedStr2.length()); ++i) {
        if (preparedStr1[i] != preparedStr2[i]) {
            ++differences;
            if (differences > 1) {
                break;  // 超过一个不同字符，提前退出循环
            }
        }
    }

    if (differences <= 1) {
        return 3;
    }
    // 所有比较均失败
    return 0;
}

int levenshtein_distance(const std::string &s1, const std::string &s2)
{
    std::vector<std::vector<int> > dp(s1.size() + 1, std::vector<int>(s2.size() + 1));

    for (int i = 0; i <= s1.size(); i++) dp[i][0] = i;
    for (int j = 0; j <= s2.size(); j++) dp[0][j] = j;

    for (int i = 1; i <= s1.size(); i++) {
        for (int j = 1; j <= s2.size(); j++) {
            if (s1[i - 1] == s2[j - 1]) {
                dp[i][j] = dp[i - 1][j - 1];
            } else {
                dp[i][j] = std::min(std::min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]) + 1;
            }
        }
    }

    return dp[s1.size()][s2.size()];
}

bool isChineseChar(QChar ch)
{
    if (ch.unicode() >= 0x4E00 && ch.unicode() <= 0x9FA5) {
        return true;
    }
    return false;
}

int compareVlp_funnzzy(const QString &str1, const QString &str2)
{
    int nLen1 = str1.length();
    int nLen2 = str2.length();
    if (0 == nLen1 || 0 == nLen2) {
        //有一个为空返回6
        return 6;
    }

    QByteArray b1 = str1.toLocal8Bit();
    QByteArray b2 = str2.toLocal8Bit();

    QChar ch1 = str1.at(0);
    QChar ch2 = str2.at(0);
    bool bDifChinese = false;
    //如果首字符为汉字，且不相同则记录汉字不等
    if (isChineseChar(ch1) || isChineseChar(ch2)) {
        if (ch1 != ch2) {
            bDifChinese = true;
        }
    }

    std::string stdS1;
    stdS1.append(b1.data());
    std::string stdS2;
    stdS2.append(b2.data());

    int distance = levenshtein_distance(stdS1, stdS2);
    if (bDifChinese) {
        if (distance > 1) distance--;
    }
    return distance;
}

//绝大部分车牌（军车不行）
bool IsValidVehPlate(const QString &sPlate)
{
    if (sPlate.length() < 5) return false;
    QRegExp regExp;

    QString sPatter = QString(
        "^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF]"
        ")|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|(["
        "京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}["
        "A-HJ-NP-Z0-9挂学警港澳使领]))");
    regExp.setPattern(sPatter);
    return regExp.exactMatch(sPlate);
}
