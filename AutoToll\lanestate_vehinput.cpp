#include "lanestate_vehinput.h"

#include "WebService/qxtjson.h"
#include "authfm.h"
#include "cardfileconver_jiangxi.h"
#include "cardfileconverter.h"
#include "ccrediteblist.h"
#include "cemvehlist.h"
#include "cfarecalcunit.h"

#include "cgallroadfare.h"
#include "cgbcardblistinc.h"
#include "cgcardblacktable.h"
#include "cgcpccardgraytable.h"
#include "cgvehblacktable.h"
#include "cgvehgraytable.h"
#include "coldsysproc.h"
#include "cprepayblist.h"
#include "crcutil.h"
#include "ctransinfoshared.h"
#include "dlgmain.h"
#include "etclanectrl.h"
#include "fdandsoundevent.h"
#include "forminputaxistype.h"
#include "forminputbigtruck.h"
#include "forminputinvoiceno.h"
#include "forminputplate.h"
#include "formlogin.h"
#include "formreverse.h"
#include "formvehinfo.h"
#include "funcmenu.h"
#include "laneinfo.h"
#include "lanesoundplay.h"
#include "listdlg.h"
#include "messagebox.h"
#include "messagedialog.h"
#include "parafileold.h"
#include "rsudev.h"
#include "rsudev_gbef.h"
#include "stdlog.h"
#include "tollgantrymgr.h"
#include "transinfo.h"
#include "unlogindlg.h"
#include "unloginopverifyfm.h"
#include "update.h"
#include "vehplatefunc.h"
#include "vehweightinfo.h"
#include "remotemsgmgr.h"
#include "remotecontrolmgr.h"
#include "vehtypelibmgr.h"

#include "etcdelay.h"

#include "prodebpayment.h"

// 重取Tac允许的交易时间到当前时间的时间差
#define ReGetTacInterval 300
CLaneState_VehInput::CLaneState_VehInput(qint32 nStateId, QObject *Parent)
    : CAbstractState(nStateId, Parent)
{
    QObject::connect(
                this,
                SIGNAL(NotifySpEventVehMessageEvent(qint32, quint8, int, QString, int, QString, QString)),
                this, SLOT(OnSpEventVehMessageEvent(qint32, quint8, int, QString, int, QString, QString)));
    m_IsInputingVLP = false;

    QObject::connect(&m_vehStayOutTimer, SIGNAL(timeout()), this, SLOT(OnVehStayOutTimer()));
    
    // 初始化卡机定时器相关参数
    m_timerCardMgr.setSingleShot(true);
    m_nTimerCardMgrInterval2 = 300;
    m_nTimerCardMgrInterval2Flag = false;
    m_nDelayedOBUID = 0; // 初始化已延时的OBUID为0，表示没有车辆延时过
}

CLaneState_VehInput::~CLaneState_VehInput()
{
    m_vehStayOutTimer.stop();
    QObject::disconnect(&m_vehStayOutTimer, SIGNAL(timeout()), this, SLOT(OnVehStayOutTimer()));
}

QString CLaneState_VehInput::GetOpStateName(int nOpState)
{
    switch (nOpState) {
    case opState_None: {
        return QString("等待来车");
        break;
    }
    case opState_WaitUpCard: {
        return QString("等待出卡");
        break;
    }
    case opState_WaitPickCard: {
        return QString("等待取卡");
        break;
    }
    case opState_IsWritingCard: {
        return QString("正在写卡");
        break;
    }
    case opState_WaitVehLeave: {
        return QString("等待车辆离开");
        break;
    }
    case opState_Reverse: {
        return QString("废票处理");
        break;
    }
    case opState_InputVehInfo: {
        return QString("正在输入车辆信息");
        break;
    }
    default:
        return QString("");
    }
}

void CLaneState_VehInput::Enter()
{
    CAbstractState::Enter();
    {
        QMutexLocker locker(&m_OtherXMt);
        m_OtherXVehInfo.Clear();
    }
    if (!IfInputVehInfoFinished()) {
        GetMainDlg()->ShowPromptMsg("请输入车辆信息");
    } else {
        GetMainDlg()->ShowPromptMsg("请输入车辆信息");
    }

    CFareDisplayer_GB *pFD = CDeviceFactory::GetETCFareDisPlayer(DevIndex_Second);
    if (pFD) {
        pFD->ClearAll();
    }
    SetOpState(opState_None);
    if (Ptr_Info->IsExitLane()) {
        CVehInfo vehInfo;
        bool bCheckInput = !Ptr_Info->bHaveCardMgr();
        GetCurVehInfo(vehInfo, bCheckInput);
        /*
        if (vehInfo.IsEmpty()) {
            CCardReader::CancelCardDetection();
            return;
        }*/
        CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
        if (pCurTransInfo) {
            if (pCurTransInfo->hasWriteCard_Exit()) {
                CCardReader::CancelCardDetection();
                GetMainDlg()->ShowPromptMsg("已刷卡,请按确认键重新计费");
                return;
            } else {
                pCurTransInfo->ClearTransInfo();
            }
        }
        CCardReader::StartCardDetection(this);
    }
}

void CLaneState_VehInput::Leave() { CCardReader::CancelCardDetection(); }

/*
 * 车型输入状态机下按取消键
 * 1、如果，此时有入口信息，并且入口信息不是刷卡获得的，清除车辆入口信息。
 * 2、如果，此时为重新处理，取消重新处理业务。
 *
 */
void CLaneState_VehInput::VehClassInput_EscKeyProcess()
{
    FormVehInfo *pVehFrm = (FormVehInfo *)m_pStateUI;
    if (pVehFrm) {
        CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
        bool bClearEntry = true;
        if (pCurTransInfo) {
            if (pCurTransInfo->hasWriteCard_Exit()) {
                bClearEntry = false;
            }
        }
        pVehFrm->ClearAllVehInfo(bClearEntry);
    }
    CCardReader::CancelCardDetection();
    return;
}

void CLaneState_VehInput::FD_DisplayTransInfo(int nIndex, CTransInfo *pTransInfo, bool bRetrans)
{
    int nDevIndex = nIndex >= DevIndex_Second ? DevIndex_Second : DevIndex_First;
    CDeviceFactory::StopAlarm(nDevIndex);
    if (!pTransInfo) return;

    if (pTransInfo->bTransFailed()) return;

    QString sBalMoney, sTollMoney;
    if (pTransInfo->mediaType == MediaType_OBU) {
        if (pTransInfo->IccInfo.ProCardBasicInfo.bType == CARD_TYPE_TALLY_CARD) {
            sBalMoney = "记账卡";
        } else {
            sBalMoney =
                    QString("余额:%1").arg(pTransInfo->ConsumeInfo.dwBalanceAfter / 100.0, 0, 'f', 2);
        }
        quint32 dwLastMoney;
        dwLastMoney = pTransInfo->ConsumeInfo.dwMoney;

        sTollMoney = QString("%1").arg(dwLastMoney / 100.0, 0, 'f', 2);
        // QString sOBUsn;
        // GetMainDlg()->Emit_ETCShowOBUInfo(0, sOBUsn);
    }
    QString sVehClassName;
    QString sVlp = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate);
    QString sWarnMsg;
    quint8 bFeeBoard = pTransInfo->GetFeeBoard(sWarnMsg);
    if (2 == bFeeBoard && sWarnMsg.length() > 0) {
        sBalMoney = sWarnMsg;
    }
    if (bRetrans) {
        sBalMoney = QString("重新放行");
    }

    pTransInfo->feeBoardPlay = bFeeBoard;
    sVehClassName = GetVehClassName(pTransInfo->VehInfo.VehClass);
    QString sWeight;

    if (isTruck(pTransInfo->VehInfo.VehClass) && DevIndex_First != nIndex) {
        sWeight = QString("重量%1").arg(pTransInfo->m_dwToTalWeight);
    }
    CFareDisplayer_GB *pFD = CDeviceFactory::GetETCFareDisPlayer(nDevIndex);
    if (pFD)
        pFD->ShowFareETC(sVehClassName, sVlp, sTollMoney, sBalMoney, sWeight,
                         Ptr_Info->GetIsCLear());
}

bool CLaneState_VehInput::GetCurVehWeightInfo(int vehClass, CVehAxisInfo &vehAxisInfo,
                                              quint32 &dwTotalWeight, quint32 &dwLimitWeight,
                                              qint32 &nOverRate)
{
    VehWeightInfo *pVehWeightInfo = VehWeightInfo::GetVehWeightInfo();
    if (!pVehWeightInfo) return false;

    bool bZhuanXiang = isZhuangXiangTruck(vehClass);
    return pVehWeightInfo->GetFirstVehWeight(bZhuanXiang, vehAxisInfo, dwTotalWeight, dwLimitWeight,
                                             nOverRate);
}

bool CLaneState_VehInput::QryTransShare(int nCurMediaType, const QString &sPlate, QString &sError)
{
    QDateTime curTime = QDateTime::currentDateTime();
    TransInfoShared Value;
    bool bFlag = CTransInfoShared::GetTransInfoShared()->GetValue(sPlate, Value);
    DebugLog(QString("组播查询返回%1").arg(bFlag));

    if (bFlag) {
        if (2 == Value.nMediatype) {  //如果是cpc交易直接终止
            if (0 == Value.nStatus && Value.nLaneid < 100) {
                if (Value.Transtime.isValid() &&
                        Value.Transtime.secsTo(curTime) < Ptr_Info->GetMaxInterval()) {
                    sError = QString("%1[%2]在[%3]道已领卡,请核实")
                            .arg(sPlate)
                            .arg(Value.Transtime.toString("hh:mm"))
                            .arg(Value.nLaneid);
                    return true;
                } else
                    // sError = QString("已领取CPC卡,车道[%1]时间[%2]").arg(Value.nLaneid);
                    return false;
            }
        } else if (1 == Value.nMediatype) {
            bool bEntryTrans = Value.nLaneid < 100;
            bool bSameLaneType = false;
            if (bEntryTrans) {
                bSameLaneType = Ptr_Info->IsEntryLane();
            } else {
                bSameLaneType = Ptr_Info->IsExitLane();
            }
            if (!bSameLaneType) {
                return false;
            }
            if (1 == Value.nStatus) {
                if (Value.Transtime.isValid() &&
                        Value.Transtime.secsTo(QDateTime::currentDateTime()) < 300) {
                    if (nCurMediaType ==
                            Value.nMediatype) {  //如果是ETC发卡，则本车道ETC会在B4帧判断重复交易
                        if (Value.nLaneid != Ptr_Info->GetLaneId()) {
                            sError = QString("%1[%2]在[%3]道交易成功,请核实")
                                    .arg(sPlate)
                                    .arg(Value.Transtime.toString("hh:mm"))
                                    .arg(Value.nLaneid);
                            return true;
                        }
                    } else {
                        sError = QString("%1[%2]在[%3]道交易成功,请核实")
                                .arg(sPlate)
                                .arg(Value.Transtime.toString("hh:mm"))
                                .arg(Value.nLaneid);
                        return true;
                    }
                }
            }
        }
    }
    return false;
}

bool CLaneState_VehInput::GetVehInfoForCPC(bool bCheckInput, CTransInfo *pTransInfo,
                                           CVehInfo &vehInfo, CVehAxisInfo &vehAxisInfo,
                                           QString &sError, QString &sFDMsg)
{
    if (!pTransInfo) return false;

    GetCurVehInfo(vehInfo, bCheckInput);

    QString sVehPlate;
    int nVlpColor = vehInfo.nVehPlateColor;
    CVehClass auVehClass = VC_None;
    if (vehInfo.IsVLPEmpty()) {
        sError = QString("无车牌信息");
        sFDMsg = QString("无车牌信息\n转人工处理");
        return false;
    } else {
        sVehPlate = GB2312toUnicode(vehInfo.szVehPlate);
        auVehClass = CDeviceFactory::GetVCRDev()->GetVehClass(vehInfo.nVehPlateColor, sVehPlate);
        DebugLog(QString("取出车型识别结果:%1,%2").arg(sVehPlate).arg((int)auVehClass));
        vehInfo.AutoVehClass = auVehClass;
        CVehInfo_VehLib vehLib;
        bool bVehLib = false;
        if (Ptr_Info->bUseVehLib()) {
            bVehLib = CVehTypeLibMgr::GetVehTypeLibMgr()->GetVehResult(
                        sVehPlate, vehInfo.nVehPlateColor, vehLib);
            if (bVehLib) {
                if (vehLib.vehType == UVT_TRUCK || vehLib.vehType == UVT_J1 ||
                        vehLib.vehType == UVT_J2) {
                    DebugLog(QString("车型库返回车辆车种为%1,不予使用").arg(vehLib.vehType));
                } else {
                    vehInfo.qryVehClass = vehLib.vehClass;
                    vehInfo.nScore = vehLib.score;
                    vehInfo.qryVehType = vehLib.vehType;
                }
            }
        }
    }

    CVehAxisInfo *pVehAxisInfo = NULL;
    quint32 dwTotalWeight = 0, dwLimitWeight = 0;
    qint32 nOverRate = 0;
    bool bWeight =
            GetCurVehWeightInfo(vehInfo.VehClass, vehAxisInfo, dwTotalWeight, dwLimitWeight, nOverRate);

    DebugLog(QString("取车辆称重信息,TotalWeight%1,LimitWeight:%2,OverRate:%3")
             .arg(dwTotalWeight)
             .arg(dwLimitWeight)
             .arg(nOverRate));

    if (vehInfo.VehClass > VC_Truck && Ptr_Info->bCheckWeight()) {
        if (!bWeight) {
            sError = QString("货车无称重信息");
            sFDMsg = QString("货车无称重信息");
            return false;
        }
    }

    int nAxisNum = 0;
    if (bWeight) {
        nAxisNum = vehAxisInfo.GetConfirmedSingleAxisNum();
    }
    UpdateVehInfo_First(vehInfo, nAxisNum);

    if (vehInfo.VehClass == VC_None) {
        sError = QString("无识别车型信息");
        sFDMsg = QString("无车型信息\n转人工处理");
        return false;
    }

    if (vehInfo.VehClass > VC_Truck && vehInfo.GBVehType != UVT_BigTruck) {
        CBigVehInfo bigVehInfo;
        bool bBigVeh = HandleBigVehSelection(sVehPlate, nVlpColor, bigVehInfo);
        if (bBigVeh) {
            //提示信息
            QString sTitle = QString("大件车(尾牌:%1)").arg(bigVehInfo.trailer_vehicle_vlp);
            QString sMsg =
                    QString("单号:%1\n货物:%2").arg(bigVehInfo.cerNo).arg(bigVehInfo.goodsInfo);
            QString sFDMsg = QString("大件车等待确认");
            if (ShowInformation_Help(sTitle, sMsg,
                                     QString("按【确认】键为大件车,【ESC】键普通车发卡"), true,
                                     sFDMsg)) {
                vehInfo.GBVehType = UVT_BigTruck;
                SetInputVehType(vehInfo.GBVehType);
                SetCertNo(bigVehInfo.cerNo);
            }
        }
    }

    if (vehInfo.VehClass > VC_Truck && Ptr_Info->bCheckWeight()) {
        if (!bWeight) {
            sError = QString("货车无称重信息");
            sFDMsg = QString("货车无称重信息");
            return false;
        }

        if (vehInfo.GBVehType != UVT_BigTruck) {
            if (nOverRate > 50) {
                QString sTotal = QString("总重 %1").arg(dwTotalWeight);
                QString sOver = QString("超重 %1").arg(dwTotalWeight - dwLimitWeight);
                sError = QString("车辆超重%1千克").arg(dwTotalWeight - dwLimitWeight);
                if (Ptr_Info->IsEntryLane()) {
                    sFDMsg = QString("%1\n%2\n超限禁止通行").arg(sTotal).arg(sOver);
                    if (VC_Truck1 == vehInfo.VehClass &&
                            vehAxisInfo.GetConfirmedAxisGroup() == 11) {
                        if (dwTotalWeight < 18000) {
                            QString sMessage = QString("货1超限车,总重低于18吨");
                            QString sHelpMessage = QString("按[确认]键继续发卡,[取消]键停止发卡");
                            sFDMsg =
                                    QString(QString("%1\n%2\n等待人工确认").arg(sTotal).arg(sOver));
                            bool bRlt = ShowInformation_Help(QString("货1超限"), sMessage,
                                                             sHelpMessage, true, sFDMsg,
                                                             CSpEventMgr::SpEvent_Other, true);

                            if (!bRlt) return false;
                        }
                    } else {
                        return false;
                    }
                }
            }
        }
    }

    if (bWeight) {
        if (Ptr_Info->IsExitLane()) {
            int nAxisNum = vehAxisInfo.GetConfirmedSingleAxisNum();
            //出口轴数相符，以称重为准否则以入口为准
            if (CheckVCAndAxis(vehInfo, nAxisNum, false, sError, sFDMsg)) {
                pVehAxisInfo = &vehAxisInfo;
            } else {
                DebugLog(QString("出口称重轴数与车型不符(%1)").arg(sError));
            }
        } else
            pVehAxisInfo = &vehAxisInfo;
    }
    pTransInfo->SetVehInfo(&vehInfo, pVehAxisInfo);
    if (m_sAutoVehPlate.length() > 0) {
        if (Ptr_ETCCtrl->GetETCAutoRegInfoByPlate(m_sAutoVehPlate, pTransInfo->AutoRegInfo)) {
            DebugLog(QString("根据车牌获取到当前车辆自动识别信息:Plate:%1,id:%2")
                     .arg(pTransInfo->AutoRegInfo.sAutoVehPlate)
                     .arg(pTransInfo->AutoRegInfo.id));
        } else {
            DebugLog(QString("没找到%1,对应识别信息").arg(m_sAutoVehPlate));
        }
    }
    if (bWeight) {
        pTransInfo->m_nOverRate = nOverRate;
    } else
        pTransInfo->m_nOverRate = 0;
    return true;
}

bool CLaneState_VehInput::GetVehInfoForCPC_New(bool bCheckInput, CTransInfo *pTransInfo,
                                               CVehInfo &vehInfo, CVehAxisInfo &vehAxisInfo,
                                               QString &sError, QString &sFDMsg)
{
    if (!pTransInfo) return false;

    GetCurVehInfo(vehInfo, bCheckInput);

    //----
    VcrResult *pVcrResult = NULL;
    VcrResult vcrResult;
    bool bCheckRlt = CheckAndRegetVehInfo(vehInfo, vcrResult);
    if (bCheckRlt) {
        pVcrResult = &vcrResult;
    }
    if (vehInfo.IsVLPEmpty()) {
        sError = QString("无车牌信息");
        sFDMsg = QString("无车牌信息\n转人工处理");
        return false;
    }

    QString sVehPlate;
    int nVlpColor = vehInfo.nVehPlateColor;
    sVehPlate = GB2312toUnicode(vehInfo.szVehPlate);

    //更新车型库结果
    CVehInfo_VehLib vehLib;
    bool bVehLib = false;
    if (Ptr_Info->bUseVehLib()) {
        bVehLib = CVehTypeLibMgr::GetVehTypeLibMgr()->GetVehResult(sVehPlate,
                                                                   vehInfo.nVehPlateColor, vehLib);
        if (bVehLib) {
            if (vehLib.vehType == UVT_TRUCK || vehLib.vehType == UVT_J1 ||
                    vehLib.vehType == UVT_J2) {
                DebugLog(QString("车型库返回车辆车种为%1,不予使用").arg(vehLib.vehType));
            } else {
                vehInfo.qryVehClass = vehLib.vehClass;
                vehInfo.nScore = vehLib.score;
                vehInfo.qryVehType = vehLib.vehType;
            }
        }
    }

    CVehAxisInfo *pVehAxisInfo = NULL;
    quint32 dwTotalWeight = 0, dwLimitWeight = 0;
    qint32 nOverRate = 0;

    VehWeightInfo *pVehWeightInfo = VehWeightInfo::GetVehWeightInfo();
    if (pVehWeightInfo->IsEditing()) {
        sError = QString("正在编辑称重");
        sFDMsg = QString("正在处理称重信息请稍后");
        return false;
    }

    bool bWeight =
            GetCurVehWeightInfo(vehInfo.VehClass, vehAxisInfo, dwTotalWeight, dwLimitWeight, nOverRate);

    if (bWeight)
        DebugLog(QString("取车辆称重信息成功,TotalWeight%1,LimitWeight:%2,OverRate:%3")
                 .arg(dwTotalWeight)
                 .arg(dwLimitWeight)
                 .arg(nOverRate));
    else
        DebugLog(QString("无称重数据"));

    if (vehInfo.VehClass > VC_Truck && Ptr_Info->bCheckWeight()) {
        if (!bWeight) {
            sError = QString("货车无称重信息");
            sFDMsg = QString("货车无称重信息");
            return false;
        }
        if (Ptr_Info->IsEntryLane()) {
            if (vehAxisInfo.IsNullVeh()) {  //手工添加的空车也要设置称重信息
                sError = QString("空车请设置车辆称重信息");
                sFDMsg = QString("货车无称重信息");
                return false;
            }
        }
    }

    int nAxisNum = 0;
    if (bWeight) {
        nAxisNum = vehAxisInfo.GetConfirmedSingleAxisNum();
    }
    UpdateVehInfo_First(vehInfo, nAxisNum);

    if (vehInfo.VehClass == VC_None) {
        sError = QString("无识别车型信息");
        sFDMsg = QString("无车型信息\n转人工处理");
        return false;
    }
    pTransInfo->SetVcrResult(pVcrResult);

    if (vehInfo.VehClass > VC_Truck && vehInfo.GBVehType != UVT_BigTruck) {
        CBigVehInfo bigVehInfo;
        bool bBigVeh = HandleBigVehSelection(sVehPlate, nVlpColor, bigVehInfo);
        if (bBigVeh) {
            //提示信息
            QString sTitle = QString("大件车(尾牌:%1)").arg(bigVehInfo.trailer_vehicle_vlp);
            QString sMsg =
                    QString("单号:%1\n货物:%2").arg(bigVehInfo.cerNo).arg(bigVehInfo.goodsInfo);
            QString sFDMsg = QString("大件车等待确认");
            if (ShowInformation_Help(sTitle, sMsg,
                                     QString("按【确认】键为大件车,【ESC】键取消"), true,
                                     sFDMsg)) {
                vehInfo.GBVehType = UVT_BigTruck;
                SetInputVehType(vehInfo.GBVehType);
                SetCertNo(bigVehInfo.cerNo);
            }
        }
    }

    if (vehInfo.VehClass > VC_Truck && Ptr_Info->bCheckWeight()) {
        if (!bWeight) {
            sError = QString("货车无称重信息");
            sFDMsg = QString("货车无称重信息");
            return false;
        }

        if (vehInfo.GBVehType != UVT_BigTruck) {
            if (nOverRate > 50) {
                QString sTotal = QString("总重 %1").arg(dwTotalWeight);
                QString sOver = QString("超重 %1").arg(dwTotalWeight - dwLimitWeight);
                sError = QString("车辆超重%1千克").arg(dwTotalWeight - dwLimitWeight);
                if (Ptr_Info->IsEntryLane()) {
                    sFDMsg = QString("%1\n%2\n超限禁止驶入").arg(sTotal).arg(sOver);
#ifdef CHECKAXIS_11

                    if (VC_Truck1 == vehInfo.VehClass &&
                            vehAxisInfo.GetConfirmedAxisGroup() == 11) {
                        if (dwTotalWeight < 18000) {
                            QString sMessage = QString("货1超限车,总重低于18吨");
                            QString sHelpMessage = QString("按[确认]键继续发卡,[取消]键停止发卡");
                            sFDMsg =
                                    QString(QString("%1\n%2\n等待人工确认").arg(sTotal).arg(sOver));
                            bool bRlt = ShowInformation_Help(QString("货1超限"), sMessage,
                                                             sHelpMessage, true, sFDMsg,
                                                             CSpEventMgr::SpEvent_Other, true);

                            if (!bRlt) return false;
                        }
                    } else {
                        return false;
                    }
#else
                    return false;
#endif
                }
            }
        }
    }

    if (bWeight) {
        if (Ptr_Info->IsExitLane()) {
            int nAxisNum = vehAxisInfo.GetConfirmedSingleAxisNum();
            //出口轴数相符，以称重为准否则以入口为准（按轴计费和货6返回true，剩余车辆称重信息不重要）
            if (CheckVCAndAxis(vehInfo, nAxisNum, false, sError, sFDMsg)) {
                pVehAxisInfo = &vehAxisInfo;
            } else {
                DebugLog(QString("出口车辆称重轴数与车型不符(%1),以入口称重信息为准").arg(sError));
            }
        } else
            pVehAxisInfo = &vehAxisInfo;
    }
    pTransInfo->SetVehInfo(&vehInfo, pVehAxisInfo);
    if (m_sAutoVehPlate.length() > 0) {
        if (Ptr_ETCCtrl->GetETCAutoRegInfoByPlate(m_sAutoVehPlate, pTransInfo->AutoRegInfo)) {
            DebugLog(QString("根据车牌获取到当前车辆自动识别信息:Plate:%1,id:%2")
                     .arg(pTransInfo->AutoRegInfo.sAutoVehPlate)
                     .arg(pTransInfo->AutoRegInfo.id));
        } else {
            DebugLog(QString("没找到%1,对应识别信息").arg(m_sAutoVehPlate));
        }
    }
    if (pVehAxisInfo)
        pTransInfo->m_nOverRate = nOverRate;
    else
        pTransInfo->m_nOverRate = 0;
    return true;
}

/**
 * @brief 校验车辆信息，重新从车型识别设备取车辆识别结果。
 * 1、车牌为空
 * 1.1取队列首辆车。取队列首辆车，校验称重队列和车型识别队列数，如果不符，则不取首辆车
 * 1.2如果首辆车车牌为空,也不取
 * 2、车牌不为空，且车型识别为空
 * 2.1如果车牌为临时牌（人工输入），取队列首辆车，队列首辆车车牌必须为空，且输入的车型和识别的车型要一致
 * 2.2如果不为临牌车，模糊配置车牌，如果匹配到，则以车型识别车型更新自动识别结果,但不采用车型识别的车牌。
 * @param
 * @return 采用车型结果返回true 否则返回false
 */
bool CLaneState_VehInput::CheckAndRegetVehInfo(CVehInfo &vehInfo, VcrResult &vcrResult)
{
    QString sVehPlate;
    int nVehNumWeight = 0;
    CVehAxisInfo vehAxisInfo;
    int nAxlesWeight = 0;
    int nVehNumVcr = 0;

    if (Ptr_Info->bCheckWeight()) {
        VehWeightInfo *pVehWeightInfo = VehWeightInfo::GetVehWeightInfo();
        if (pVehWeightInfo) {
            nVehNumWeight = pVehWeightInfo->GetVehCount();
            if (pVehWeightInfo->GetFirstVeh(&vehAxisInfo)) {
                nAxlesWeight = vehAxisInfo.GetConfirmedSingleAxisNum();
            }
        }

        nVehNumVcr = CDeviceFactory::GetVCRDev()->GetVehCount();
        DebugLog(
                    QString("称重队列车辆数:%1,车型识别队列车辆数:%2").arg(nVehNumWeight).arg(nVehNumVcr));
    }

    if (vehInfo.IsVLPEmpty()) {
        // 新增：检查是否禁用从车型设备队列获取车牌信息（仅针对车牌为空的情况）
        if (Ptr_Info->bDisableVcrQueuePlate()) {
            DebugLog(QString("车牌为空，但配置禁用从车型设备队列获取车牌信息，跳过队列获取逻辑"));
            return false;
        }
        
        //无车牌信息，取小黄人首辆车信息,取称台数据比较车辆数量
        if (nVehNumWeight > 0) {
            if (nVehNumVcr != nVehNumWeight) {
//                DebugLog(QString("车牌为空,车型队列数:%1,称重队列数:%2,不符,不取首辆车")
//                         .arg(nVehNumVcr)
//                         .arg(nVehNumWeight));
                DebugLog(QString("车牌为空,车型队列数:%1,称重队列数:%2,不符,继续处理")
                         .arg(nVehNumVcr)
                         .arg(nVehNumWeight));
                // return false;
            }
        }
        bool bRlt = CDeviceFactory::GetVCRDev()->GetFistVcrResult(vcrResult);
        if (!bRlt) {
            DebugLog(QString("取车型识别首辆车信息,当前无车型识别数据"));
            return false;
        } else {
            //如果小黄人车牌为空，返回
            DebugLog(QString("车型识别首辆车,plate:%1_%2,vehclass:%3,axlenum:%4")
                     .arg(vcrResult.sPlate)
                     .arg(vcrResult.nColor)
                     .arg(vcrResult.vehclass)
                     .arg(vcrResult.nAxleCnt));

            if (vcrResult.bNoPlate()) {
                DebugLog(QString("首辆车车牌为空,不采用"));
                return false;
            }

            if (0 == vcrResult.bPlateValid) {
                DebugLog(QString("首辆车车牌%1,不可信").arg(vcrResult.sPlate));
                return false;
            }

            if (nAxlesWeight > 0) {
                if (nAxlesWeight != vcrResult.nAxleCnt) {
                    DebugLog(QString("首辆车称重轴数:%1,识别轴数:%2不符,不采用")
                             .arg(nAxlesWeight)
                             .arg(vcrResult.nAxleCnt));
                    return false;
                }
            }
            vehInfo.nAutoVehPlateColor = vcrResult.nColor;
            QByteArray bVehPlate = UnicodetoGB2312(vcrResult.sPlate);
            qsnprintf(vehInfo.szAutoVehPlate, sizeof vehInfo.szAutoVehPlate, "%s",
                      bVehPlate.constData());
            qsnprintf(vehInfo.szVehPlate, sizeof vehInfo.szVehPlate, "%s", bVehPlate.constData());
            vehInfo.nVehPlateColor = vcrResult.nColor;

            vehInfo.AutoVehClass = vcrResult.vehclass;
            if (vehInfo.VehClass == VC_None) {
                vehInfo.VehClass = vehInfo.AutoVehClass;
                vehInfo.nVehClassWay = VehClassWay_Auto;
            }
            DebugLog(QString("车牌识别为空,取车型识别首辆车信息,plate:%1,color:%2,vehclass:%3")
                     .arg(vcrResult.sPlate)
                     .arg(vcrResult.nColor)
                     .arg(vcrResult.vehclass));
            return true;
        }
    } else {
        sVehPlate = GB2312toUnicode(vehInfo.szVehPlate);
        if (vehInfo.AutoVehClass == VC_None) {
            //临牌，取首辆车
            int nLinPai = sVehPlate.indexOf(QString("临"));
            if (nLinPai >= 0 || vehInfo.nVehPlateColor == VP_COLOR_TEMP) {
                DebugLog(
                            QString("%1_%2临牌车,取首辆车车型").arg(sVehPlate).arg(vehInfo.nVehPlateColor));
                bool bRlt = CDeviceFactory::GetVCRDev()->GetFistVcrResult(vcrResult);
                if (bRlt) {
                    DebugLog(QString("车型识别首辆车,Plate:%1_%2,vehClass:%3,Axles:%4")
                             .arg(vcrResult.sPlate)
                             .arg(vcrResult.nColor)
                             .arg(vcrResult.vehclass)
                             .arg(vcrResult.nAxleCnt));

                    if ((vcrResult.bNoPlate()) ||
                            sVehPlate == vcrResult.sPlate) {  //首辆车无车牌因为匹配成功
                        bool bFirstCar = true;
                        if (vehInfo.VehClass != VC_None) {  //已经输入车型了
                            if (vehInfo.VehClass != vcrResult.vehclass) {
                                DebugLog(QString("临牌车输入车型%1，与首辆车车型%2不符,放弃首辆车")
                                         .arg(vehInfo.VehClass)
                                         .arg(vcrResult.vehclass));
                                bFirstCar = false;
                            }
                        }

                        /*此处只是为了增加识别率，校验可以宽松一些
                        if (nAxlesWeight > 0 && vcrResult.nAxleCnt > 0) {
                            if (nAxlesWeight != vcrResult.nAxleCnt) {
                                DebugLog(QString("临牌车称重轴数%1与车型轴数%2不符,放弃首辆车")
                                             .arg(nAxlesWeight)
                                             .arg(vcrResult.nAxleCnt));
                                bFirstCar = false;
                            }
                        }*/

                        if (bFirstCar) {
                            DebugLog(QString("临牌车取首辆车,更新自动识别结果"));
                            vehInfo.AutoVehClass = vcrResult.vehclass;
                            qsnprintf(vehInfo.szAutoVehPlate, sizeof vehInfo.szAutoVehPlate, "%s",
                                      vehInfo.szVehPlate);
                            vehInfo.nAutoVehPlateColor = vehInfo.nVehPlateColor;
                            vcrResult.sPlate = sVehPlate;
                            vcrResult.nColor = vehInfo.nVehPlateColor;
                            if (vehInfo.VehClass == VC_None) {
                                vehInfo.VehClass = vehInfo.AutoVehClass;
                                vehInfo.nVehClassWay = VehClassWay_Auto;
                            }
                            return true;
                        }
                    }
                }
            } else {  //非临牌车,模糊查找一遍
                bool bRlt = false;
                if (vehInfo.VehClass == VC_None) {  //既没有车型识别也没有输入的车型，再查一边车型
                    DebugLog(QString("根据车牌%1查找车型结果").arg(sVehPlate));
                    bRlt = CDeviceFactory::GetVCRDev()->GetVcrResult(vehInfo.nVehPlateColor,
                                                                     sVehPlate, vcrResult);
                    if (bRlt) {
                        vehInfo.AutoVehClass = vcrResult.vehclass;
                        if (vehInfo.VehClass == VC_None) {
                            vehInfo.VehClass = vehInfo.AutoVehClass;
                            vehInfo.nVehClassWay = VehClassWay_Auto;
                        }
                        QString sAutoPlate = GB2312toUnicode(vehInfo.szAutoVehPlate);
                        if (sAutoPlate.isEmpty() || sAutoPlate != sVehPlate) {
                            DebugLog(QString("车牌识别结果%1,实际车牌:%2,小黄人识别结果%3,"
                                             "替换车牌识别为小黄人")
                                     .arg(sAutoPlate)
                                     .arg(sVehPlate)
                                     .arg(vcrResult.sPlate));
                            vcrResult.sPlate = sVehPlate;
                            vcrResult.nColor = vehInfo.nVehPlateColor;
                            qsnprintf(vehInfo.szAutoVehPlate, sizeof vehInfo.szAutoVehPlate, "%s",
                                      vehInfo.szVehPlate);
                            vehInfo.nAutoVehPlateColor = vehInfo.nVehPlateColor;
                            return true;
                        } else
                            return false;
                    }
                }

                DebugLog(QString("根据车牌%1模糊查找车型结果").arg(sVehPlate));
                bRlt = CDeviceFactory::GetVCRDev()->GetVcrResult_Fuzzy(vehInfo.nVehPlateColor,
                                                                       sVehPlate, vcrResult);
                if (bRlt) {
                    DebugLog(QString("模糊匹配到识别结果,plate:%1,vehclass:%2")
                             .arg(vcrResult.sPlate)
                             .arg(vcrResult.vehclass));
                    if (vehInfo.nVehClassWay == VehClassway_Input && VC_None != vehInfo.VehClass) {
                        if (vehInfo.VehClass != vcrResult.vehclass) {
                            DebugLog(QString("模糊匹配的车型%1,与人工输入的车型:%2不符,丢弃")
                                     .arg(vcrResult.vehclass)
                                     .arg(vehInfo.VehClass));
                            return false;
                        }
                    }

                    //车型为空，肯定没有人工输入，且自动识别没匹配上，此时出口可以取模糊匹配的车型，入口为避免错误，可不取（因此参数为false）
                    if (VC_None == vehInfo.VehClass &&
                            Ptr_Info->bFuzztMatch()) {  //如果车型为空,取自动识别车型
                        DebugLog(QString("当前车型为空,取模糊匹配车型%1:%2")
                                 .arg(vcrResult.sPlate)
                                 .arg(vcrResult.vehclass));
                        vehInfo.VehClass = vcrResult.vehclass;
                        vehInfo.nVehClassWay = VehClassWay_Auto;
                        vehInfo.AutoVehClass = vcrResult.vehclass;
                    }

                    if (vehInfo.VehClass == VC_None) {
                        return false;
                    }
                    //如果人工输入或者设置了车型，
                    vehInfo.AutoVehClass = vcrResult.vehclass;
                    QString sAutoPlate = GB2312toUnicode(vehInfo.szAutoVehPlate);
                    if (sAutoPlate.isEmpty() ||
                            sAutoPlate !=
                            sVehPlate) {  //如果自动识别车牌为空，说明人工输入的车牌，此时为了增加识别率，以车型识别结果代替车牌识别结果
                        //这种情况需要没车牌识别，而且人工提前输入了车辆信息才可以
                        DebugLog(QString("车牌识别结果%1,实际车牌:%2,小黄人识别结果%3")
                                 .arg(sAutoPlate)
                                 .arg(sVehPlate)
                                 .arg(vcrResult.sPlate));
                        vcrResult.sPlate = sVehPlate;
                        vcrResult.nColor = vehInfo.nVehPlateColor;
                        qsnprintf(vehInfo.szAutoVehPlate, sizeof vehInfo.szAutoVehPlate, "%s",
                                  vehInfo.szVehPlate);
                        vehInfo.nAutoVehPlateColor = vehInfo.nVehPlateColor;
                        return true;
                    } else {
                        //输入的同识别相同，或者没输入只有识别
                        if ((vehInfo.nVehClassWay != VehClassway_Input) &&
                                (Ptr_Info->bReplaceByVcr())) {
                            //没有人工输入，以人工输入为准
                            if (vcrResult.bPlateValid) {  //车牌有效
                                QByteArray bVehPlate = UnicodetoGB2312(
                                            vcrResult.sPlate);  //同时更新车牌识别结果为首辆车结果
                                qsnprintf(vehInfo.szAutoVehPlate, sizeof vehInfo.szAutoVehPlate,
                                          "%s", bVehPlate.constData());
                                vehInfo.nAutoVehPlateColor = vcrResult.nColor;
                                qsnprintf(vehInfo.szVehPlate, sizeof vehInfo.szVehPlate, "%s",
                                          bVehPlate.constData());
                                vehInfo.nVehPlateColor = vcrResult.nColor;
                                return true;
                            }
                        }
                    }
                    return false;

                    /*
                    if (!vcrResult.bNoPlate()) {  //更新车牌识别结果为小黄人结果
                        QByteArray bVehPlate =
                            UnicodetoGB2312(vcrResult.sPlate);  //同时更新车牌识别结果为首辆车结果
                        qsnprintf(vehInfo.szAutoVehPlate, sizeof vehInfo.szAutoVehPlate, "%s",
                                  bVehPlate.constData());
                        vehInfo.nAutoVehPlateColor = vcrResult.nColor;
                        return true;
                    }*/
                }
            }
        }
    }
    return false;
}

void CLaneState_VehInput::ShowLog(const QString &sLog) { GetMainDlg()->ShowLog(sLog); }

void CLaneState_VehInput::DebugEmvehInfo(const CEmVehInfo &emVehInfo)
{
    QString sLog;
    QDateTime startTime, endTime;
    UnixTime2QDateTime_GB(emVehInfo.startTime, startTime);
    UnixTime2QDateTime_GB(emVehInfo.endTime, endTime);

    QString sStartTime = startTime.toString("yyyy-MM-dd hh:mm:ss");
    QString sEndTime = endTime.toString("yyyy-MM-dd hh:mm:ss");

    sLog = QString(
                "紧急车[%1:%2],卡号:%3,名单类型:%4,处理类型:%5,"
                "校验类型:%6,车辆状态:%7,查询类型:%8,折扣类型:%9,折扣率:%10,起始时间:%11,"
                "结束时间:%12 ")
            .arg(emVehInfo.vlp)
            .arg(emVehInfo.vlpc)
            .arg(emVehInfo.cardId)
            .arg(emVehInfo.appointType)
            .arg(emVehInfo.handleType)
            .arg(emVehInfo.checkType)
            .arg(emVehInfo.vehicleSign)
            .arg(emVehInfo.vehicleIdentityType)
            .arg(emVehInfo.discountType)
            .arg(emVehInfo.discount)
            .arg(sStartTime)
            .arg(sEndTime);
    DebugLog(sLog);
}

bool CLaneState_VehInput::CheckOBUVehInfo(int nIndex, CTransInfo *pCurTransInfo, bool bJinJi,
                                          int &nErrorCode, QString &sError, QString &sFDMsg)
{
    if (bJinJi) return true;

    //混合车道出口
    if (DevIndex_Second == nIndex) {
        return true;
    }

    COBUVehInfo *pOBUVehInfo = &pCurTransInfo->OBUVehInfo;
    if (pOBUVehInfo->bUserType == 0x1B) {
        nErrorCode = CSpEventMgr::SpEvent_TruckStopPass;
        sError = QString("牵引车请前行");
        sFDMsg = sError;
        return false;
    }

    if (pOBUVehInfo->bUserType == 0x18 || pOBUVehInfo->bUserType == 0x1C) {
        nErrorCode = CSpEventMgr::SpEvent_TruckStopPass;
        sError = QString("集装箱车请前行");
        sFDMsg = sError;
        return false;
    }

    QString sPlate = GB2312toUnicode(pOBUVehInfo->szVehPlate);
    //双天线货车全部后天线处理
    bool bYJVeh = CCardFileConverter::IsYJCard(sPlate, pOBUVehInfo->bUserType);
    if ((pOBUVehInfo->bVehClass >= VC_Truck1) && (!bYJVeh)) {
        nErrorCode = CSpEventMgr::SpEvent_TruckStopPass;
        sError = QString("货车请前行");
        sFDMsg = sError;
        return false;
    }
    return true;
}

bool CLaneState_VehInput::CheckLastOBUPause(quint32 dwOBUId)
{
    QMutexLocker locker(&m_lastOBUMt);
    if (dwOBUId != m_lastOBUInfo.dwOBUId) {
        m_lastOBUInfo.ClearLastOBUInfo();
        return false;
    }
    if (0 == m_lastOBUInfo.nPauseTime) return false;

    qint64 nCurTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    if (nCurTime > m_lastOBUInfo.nPauseTime) {
        if (nCurTime - m_lastOBUInfo.nPauseTime > 1000) {
            m_lastOBUInfo.ClearLastOBUInfo();
            return false;
        }
    }
    return true;
}

void CLaneState_VehInput::PauseOBU(quint32 dwOBUId)
{
#ifdef Lane_Test
    return;
#endif
    QMutexLocker locker(&m_lastOBUMt);
    if (0 == dwOBUId) {
        m_lastOBUInfo.ClearLastOBUInfo();
        return;
    }
    m_lastOBUInfo.dwOBUId = dwOBUId;
    m_lastOBUInfo.nPauseTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
}

bool CLaneState_VehInput::CheckInputVCAndAxisNum(int &nAxisNum, QString &sError)
{
    CVehInfo inputVehInfo;
    GetCurVehInfo(inputVehInfo, false);
    //没识别或输入车型，不进行比较

    QString sPlate;
    int nAxisNumVC = 0;
    QString sVCName;
    if (!inputVehInfo.IsEmpty()) {
        sPlate = GB2312toUnicode(inputVehInfo.szVehPlate);
        nAxisNumVC = GetVehAxisNumByVC(inputVehInfo.VehClass);
        sVCName = GetVehClassName(inputVehInfo.VehClass);
    } else {
        DebugLog(QString("校验车型、轴数信息,车辆信息为空"));
    }

    CVehAxisInfo vehAxisInfo;
    quint32 dwTotalWeight = 0, dwLimitWeight = 0;
    qint32 nOverRate = 0;
    bool bHaveWeightInfo = GetCurVehWeightInfo(inputVehInfo.VehClass, vehAxisInfo, dwTotalWeight,
                                               dwLimitWeight, nOverRate);
    if (!bHaveWeightInfo) {
        if (Ptr_Info->bCheckWeight()) {
            if (sPlate.length() > 0)
                sError = QString("%1,无称重信息").arg(sPlate);
            else
                sError = QString("当前车辆无称重信息");
            return false;
        } else
            return true;
    }

    nAxisNum = vehAxisInfo.GetConfirmedSingleAxisNum();
    DebugLog(QString("校验车型轴数:%1,当前车辆轴数:%2").arg(nAxisNumVC).arg(nAxisNum));

    if (inputVehInfo.IsEmpty()) return true;
    if (inputVehInfo.GBVehType != UVT_BigTruck) {
        if (nAxisNumVC != nAxisNum) {
            if (inputVehInfo.VehClass == VC_Truck6 && (nAxisNum > nAxisNumVC)) {
                DebugLog(QString("货6异型轴,轴数:%1").arg(nAxisNumVC));
                return true;
            }
            sError = QString("车型%1与称重轴数%2轴不符").arg(sVCName).arg(nAxisNum);
            DebugLog(QString("识别车型与轴数不符,%1，车牌%2，轴数%3")
                     .arg(sVCName)
                     .arg(sPlate)
                     .arg(nAxisNum));
            return false;
        }
    }
    return true;
}

bool CLaneState_VehInput::CheckAndSetOpState(quint8 bOpState, QString &sDesc)
{
    QMutexLocker locker(&m_opStateMt);
    if (m_OpState > opState_None) {
        sDesc = GetOpStateName(m_OpState);
        return false;
    }
    if (m_OpState != bOpState) {
        m_OpState = bOpState;
        DebugLog(QString("设置车道状态:%1").arg(GetOpStateName(m_OpState)));
    }
    return true;
}

void CLaneState_VehInput::SetOpState(quint8 bOpState)
{
    QMutexLocker locker(&m_opStateMt);
    m_OpState = bOpState;
    DebugLog(QString("设置车道状态:%1").arg(GetOpStateName(m_OpState)));
}

bool CLaneState_VehInput::CheckVehEntryInfo(int nIndex, CTransInfo *pTransInfo,
                                            const CVehEntryInfo &vehEntryInfo, QString &sError,
                                            QString &sFDMsg)
{
    if (DevIndex_First == nIndex) {
        return true;
    }

    if (pTransInfo->VehInfo.GBVehType == UVT_TRUCK || pTransInfo->VehInfo.GBVehType == UVT_J1 ||
            pTransInfo->VehInfo.GBVehType == UVT_J2) {
        //需要比较入出口轴数
        if (!pTransInfo->m_vehAxisInfo.IsNullVeh()) {
            int nAxleNum = pTransInfo->m_vehAxisInfo.GetConfirmedSingleAxisNum();
            DebugLog(QString("开始比较出入口轴数,出口:%1,入口:%2")
                     .arg(nAxleNum)
                     .arg(vehEntryInfo.VehicalAxles));
            if (vehEntryInfo.VehicalAxles > 0) {
                if (nAxleNum != vehEntryInfo.VehicalAxles) {
                    QString sPlate = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate);
                    sError = QString("货车[%1],入口轴数[%2]与出口[%3]不符")
                            .arg(sPlate)
                            .arg(vehEntryInfo.VehicalAxles)
                            .arg(nAxleNum);
                    sFDMsg.clear();
                    DebugLog(sError);
                    return false;
                }
            }
        } else {
            DebugLog(QString("车辆为空车,不比较轴数"));
        }
    }
    return true;
}

/**
 * @brief
 * @param feeclass - 输入 当前计费方式 输出 最终计费方式
 * @return true- 按当前返回的计费方式计费 false-表示超过最大计费金额，需要转在线计费
 */
bool CLaneState_VehInput::CheckFeeClass_Etc(CTransInfo *pTransInfo, quint32 nTransFee,
                                            CFeeClass &feeClass, bool bUReturn, bool IsLocalEntry,
                                            bool bFree, QString &sMsg)
{
    DebugLog("校验出口最终计费方式");
    if (Ptr_Info->bNewUJ()) {
        return CheckFeeClass_Last(pTransInfo, nTransFee, feeClass, bUReturn, IsLocalEntry, bFree,
                                  sMsg);
    }
    int overCent = 0;
    int nRlt = pTransInfo->CompareMinFee(nTransFee, feeClass, overCent, IsLocalEntry, bFree, sMsg);
    if (nRlt <= 0) pTransInfo->nMinPerCent = overCent;

    if (nRlt < 0) {
        if (!bUReturn) feeClass = FeeClass_Min;  // U型按介质计费
        return true;
    } else if (0 == nRlt) {
        return true;
    }

    if (!IsLocalEntry) {
        feeClass = FeeClass_MinistryCenter;
        return false;
    }

    bool bLessMaxFee = false;
    quint32 nMaxFee = 0;
    bool bETC = feeClass == FeeClass_OBU;
    SpParaTable *pTable = (SpParaTable *)CParamFileMgr::GetParamFile(cfSpPara);
    if (pTable) {
        double nBaseFee = 0;
        if (pTable->QryMaxFee((int)pTransInfo->VehInfo.VehClass, nMaxFee, nBaseFee)) {
            CFlagListTable *pFlagTable = (CFlagListTable *)CParamFileMgr::GetParamFile(cfFlagList);
            if (pFlagTable) {
                QString sPlate = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate);
                QString sCardId = QString::fromAscii(pTransInfo->cpcIccInfo.cpcBasicInfo.sCardID);
                QString sOBUId;
                if (bETC) {
                    sCardId = QString::fromAscii(pTransInfo->IccInfo.ProCardBasicInfo.szCardNo);
                    sOBUId = QString::fromAscii(pTransInfo->OBUBaseInfo.szContractSerialNumber);
                }
                QString sEntryTime =
                        pTransInfo->vehEntryInfo.EnTime.toString("yyyy-MM-dd hh:mm:ss");
                CFlagListInfo flagListInfo;
                DebugLog(
                            QString("查询调头点,vlp:%1,vlpc:%2,cardId:%3,obuid:%4,entryTime:%5,flagver:%6")
                            .arg(sPlate)
                            .arg(pTransInfo->VehInfo.nVehPlateColor)
                            .arg(sCardId)
                            .arg(sOBUId)
                            .arg(sEntryTime)
                            .arg(pFlagTable->GetVersion()));
                bool bInFlag = pFlagTable->QryFlagInfo(
                            sPlate, (int)pTransInfo->VehInfo.nVehPlateColor, sCardId, sOBUId,
                            pTransInfo->vehEntryInfo.EnTime, flagListInfo);
                if (bInFlag) {
                    QString sFlagTime = QDateTime::fromTime_t(flagListInfo.transTime)
                            .toString("yyyy-MM-dd hh:mm:ss");
                    DebugLog(QString("车辆经过调头点[%1],OBUId:%2,CardId:%3,transTime:%4,TransFee:%"
                                     "5,MaxFee:%6")
                             .arg(flagListInfo.UturnPoint)
                             .arg(flagListInfo.ObuId)
                             .arg(flagListInfo.sCardId)
                             .arg(sFlagTime)
                             .arg(nTransFee)
                             .arg(nMaxFee));

                    if (nTransFee > nMaxFee) {
                        sMsg = QString("[%1]调头,通行费[%2]超过上限金额[%3]")
                                .arg(flagListInfo.UturnPoint)
                                .arg(nTransFee)
                                .arg(nMaxFee);
                        feeClass = FeeClass_ProvCenter;
                        DebugLog(sMsg);
                    } else {
                        DebugLog(
                                    QString("调头点[%1],时间:%2,交易金额:%3,最大金额:%4,按卡内金额计费")
                                    .arg(flagListInfo.UturnPoint)
                                    .arg(sFlagTime)
                                    .arg(nTransFee)
                                    .arg(nMaxFee));
                        bLessMaxFee = true;
                        pTransInfo->m_nCalcMaxFee = nMaxFee;
                    }
                } else {
                    DebugLog("车辆未经过调头点");
                    //判断是否两天以内
                    int nSeconds =
                            pTransInfo->vehEntryInfo.EnTime.secsTo(QDateTime::currentDateTime());
                    if (nSeconds >= 0 && nSeconds <= 2 * 24 * 3600) {
                        //两天之内
                        CMinFeePerCent minFeePercent;
                        bool bQryRlt = pTable->QryMinFeePercent(
                                    IsLocalEntry, pTransInfo->VehInfo.VehClass, minFeePercent);
                        if (bQryRlt) {
                            bool bETC = FeeClass_OBU == feeClass;
                            qint32 nLowPercent = 100, nHighPercent = 200;
                            if (bETC) {
                                nLowPercent = minFeePercent.LowPercent_ETC;
                                nHighPercent = minFeePercent.HightPercent_ETC;
                            } else {
                                nLowPercent = minFeePercent.LowPercent_CPC;
                                nHighPercent = minFeePercent.HightPercent_CPC;
                            }

                            QString sError;
                            qint32 nOverPercent = 0;
                            bool bComRlt = pTransInfo->CompareMinFeeByParam(
                                        nTransFee, bETC, nLowPercent, nHighPercent, nOverPercent, bFree,
                                        sError);
                            if (bComRlt <= 0) {
                                bLessMaxFee = true;
                                pTransInfo->m_nFeePercent = nHighPercent;
                            } else {
                                if (nTransFee < nMaxFee) {
                                    QString sVehPlate;
                                    bool bPolic = pTransInfo->bSpecialVeh(sVehPlate);
                                    if (feeClass == FeeClass_OBU && bPolic) {
                                        DebugLog(QString("赣O警车%1,超出最小费额,"
                                                         "但未超出省内最大金额,按实际介质收费")
                                                 .arg(sVehPlate));
                                        bLessMaxFee = true;
                                        pTransInfo->m_nFeePercent = nHighPercent;
                                        pTransInfo->m_nCalcMaxFee = nMaxFee;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } else {
            DebugLog(QString("查询最大费额失败"));
        }
    }

    if (!bLessMaxFee) {
        feeClass = FeeClass_ProvCenter;
    }
    return bLessMaxFee;
}

/**
 * @brief
 * @param
 * @return true -此时按介质计费或最小费额 false-在线计费
 */
bool CLaneState_VehInput::CheckFeeClass_Last(CTransInfo *pTransInfo, quint32 nTransFee,
                                             CFeeClass &feeClass, bool bUReturn, bool IsLocalEntry,
                                             bool bFree, QString &sMsg)
{
    DebugLog("NewUJ校验出口最终计费方式");
    // bool bCheckFeeResult = false;
    int overCent = 0;

    //两天之内
    CMinFeePerCent minFeePercent;
    bool bETC = FeeClass_OBU == feeClass;

    SpParaTable *pTable = (SpParaTable *)CParamFileMgr::GetParamFile(cfSpPara);
    pTable->QryMinFeePercent(IsLocalEntry, pTransInfo->VehInfo.VehClass, minFeePercent);

    qint32 nLowPercent = 100, nHighPercent = 200;
    if (bETC) {
        nLowPercent = minFeePercent.LowPercent_ETC;
        nHighPercent = minFeePercent.HightPercent_ETC;
    } else {
        nLowPercent = minFeePercent.LowPercent_CPC;
        nHighPercent = minFeePercent.HightPercent_CPC;
    }

    int nRlt = pTransInfo->CompareMinFeeByParam(nTransFee, bETC, nLowPercent, nHighPercent,
                                                overCent, bFree, sMsg);
    if (nRlt <= 0) pTransInfo->nMinPerCent = overCent;

    if (nRlt < 0) {  // U型且半小时以内按介质，否则etc最小，cpc转在线
        if (!bUReturn) {
            feeClass = FeeClass_Min;
        } else {
            QDateTime curTime = QDateTime::currentDateTime();
            qint32 nSeconds = pTransInfo->vehEntryInfo.EnTime.secsTo(curTime);
            if (nSeconds > 60 * 30) {
                if (bETC) {
                    feeClass = FeeClass_Min;
                } else {
                    if (IsLocalEntry)
                        feeClass = FeeClass_ProvCenter;
                    else
                        feeClass = FeeClass_MinistryCenter;
                }
            } else {
                //半小时以内,则按介质计费
            }
        }
        return true;
    } else if (0 == nRlt) {
        return true;
    } else {
    }

    bool bLessMaxFee = false;
    quint32 nMaxFee = 0;
    double nBaseFee = 0;
    if (!pTable->QryMaxFee((int)pTransInfo->VehInfo.VehClass, nMaxFee, nBaseFee)) {
        DebugLog(QString("省内最大费额查询失败"));
        if (IsLocalEntry)
            feeClass = FeeClass_MinistryCenter;
        else
            feeClass = FeeClass_ProvCenter;
        return false;
    }

    //

    CFlagListInfo flagListInfo;
    bool bInFlag = false;
    if (IsLocalEntry) {
        bInFlag = QueryFlagInfo(pTransInfo, bETC, flagListInfo);
    }

    if (bInFlag) {  //省内，在路径内，超过上限返回在线，否则按介质计费
        if (nTransFee > nMaxFee) {
            sMsg = QString("[%1]调头,通行费[%2]超过上限金额[%3]")
                    .arg(flagListInfo.UturnPoint)
                    .arg(nTransFee)
                    .arg(nMaxFee);
            feeClass = FeeClass_MinistryCenter;
            DebugLog(sMsg);
        } else {
            DebugLog(QString("调头点[%1],时间:%2,交易金额:%3未超出省内最大金额:%4,按介质金额计费")
                     .arg(flagListInfo.UturnPoint)
                     .arg(flagListInfo.transTime)
                     .arg(nTransFee)
                     .arg(nMaxFee));
            bLessMaxFee = true;
            pTransInfo->m_nCalcMaxFee = nMaxFee;  //按传入计费类型计费
            return true;
        }
    } else {  //不在调头点或者超过上限
        if (nTransFee > nMaxFee) {
            sMsg = QString("通行费[%1]超过收费上限金额[%2]").arg(nTransFee).arg(nMaxFee);
            feeClass = FeeClass_MinistryCenter;
            DebugLog(sMsg);
        } else {
            if (IsLocalEntry) {
                //本省不在参数内
                DebugLog(
                            QString("省内通行,未经过调头点,通行费[%1]未超过省内收费上限[%2]金额,在线计费")
                            .arg(nTransFee)
                            .arg(nMaxFee));
                if (bETC) {
                    QString sVehPlate;
                    if (pTransInfo->bSpecialVeh(sVehPlate)) {
                        DebugLog(
                                    QString("赣O警车:%1超出最小费额上限,但未超出省内收费上限按介质金额计费")
                                    .arg(sVehPlate));
                        pTransInfo->m_nCalcMaxFee = nMaxFee;
                        return true;
                    }
                }
                feeClass = FeeClass_ProvCenter;
            } else {
                //跨省的小于，则按介质计费
                DebugLog(QString("跨省通行,通行费%1,未超过收费上限%2,在线计费")
                         .arg(nTransFee)
                         .arg(nMaxFee));
                // bLessMaxFee = true;
                // pTransInfo->m_nCalcMaxFee = nMaxFee;
                // sMsg = QString("通行费[%1]超过收费上限金额").arg(nTransFee).arg(nMaxFee);
                feeClass = FeeClass_MinistryCenter;
            }
        }
    }

    return false;
}
bool CLaneState_VehInput::QueryFlagInfo(CTransInfo *pTransInfo, bool bETC,
                                        CFlagListInfo &flagListInfo)
{
    if (!pTransInfo) return false;
    CFlagListTable *pFlagTable = (CFlagListTable *)CParamFileMgr::GetParamFile(cfFlagList);
    if (!pFlagTable) return false;

    QString sPlate = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate);

    QString sCardId = QString::fromAscii(pTransInfo->cpcIccInfo.cpcBasicInfo.sCardID);
    if (bETC) sCardId = QString::fromAscii(pTransInfo->IccInfo.ProCardBasicInfo.szCardNo);
    QString sOBUId = QString::fromAscii(pTransInfo->OBUBaseInfo.szContractSerialNumber);
    QString sEntryTime = pTransInfo->vehEntryInfo.EnTime.toString("yyyy-MM-dd hh:mm:ss");
    DebugLog(QString("查询调头点,vlp:%1,vlpc:%2,cardId:%3,obuid:%4,entryTime:%5,flagver:%6")
             .arg(sPlate)
             .arg(pTransInfo->VehInfo.nVehPlateColor)
             .arg(sCardId)
             .arg(sOBUId)
             .arg(sEntryTime)
             .arg(pFlagTable->GetVersion()));
    bool bInFlag = pFlagTable->QryFlagInfo(sPlate, (int)pTransInfo->VehInfo.nVehPlateColor, sCardId,
                                           sOBUId, pTransInfo->vehEntryInfo.EnTime, flagListInfo);
    if (bInFlag) {
        QString sFlagTime =
                QDateTime::fromTime_t(flagListInfo.transTime).toString("yyyy-MM-dd hh:mm:ss");
        DebugLog(QString("车辆经过调头点[%1],OBUId:%2,CardId:%3,transTime:%4")
                 .arg(flagListInfo.UturnPoint)
                 .arg(flagListInfo.ObuId)
                 .arg(flagListInfo.sCardId)
                 .arg(sFlagTime));
        return true;
    } else {  //在线查询flag
        bool bRlt = CFareCalcUnit::QryFlag_OnLine(sPlate, (int)pTransInfo->VehInfo.nVehPlateColor,
                                                  sOBUId, sCardId, flagListInfo);
        if (bRlt) {
            //
            DebugLog(QString("在线查询调头点返回,UPort：[%1],OBUId:%2,CardId:%3,transTime:%4")
                     .arg(flagListInfo.UturnPoint)
                     .arg(flagListInfo.ObuId)
                     .arg(flagListInfo.sCardId)
                     .arg(flagListInfo.transTime));

            QDateTime flagTime, curTime;
            curTime = QDateTime::currentDateTime();
            if (flagListInfo.transTime > 0) {
                flagTime = QDateTime::fromTime_t(flagListInfo.transTime);
                int nSeconds = flagTime.secsTo(curTime);

                if (flagTime > pTransInfo->vehEntryInfo.EnTime && nSeconds < 48 * 3600) {
                    QString sFlagTime = flagTime.toString("yyyy-MM-dd hh:mm:ss");
                    DebugLog(QString("在线查询,车辆经过调头点[%1],OBUId:%2,CardId:%3,transTime:%4")
                             .arg(flagListInfo.UturnPoint)
                             .arg(flagListInfo.ObuId)
                             .arg(flagListInfo.sCardId)
                             .arg(sFlagTime));
                    return true;
                }
            } else {
            }
        }
        return false;
    }
}

bool CLaneState_VehInput::ShowInformation_Help(const QString &sTitle, const QString &sMsg,
                                               const QString &sHelp, bool bAlarm,
                                               const QString &sFDMsg, int nEventId,
                                               bool allowRemoteCtrl)
{
    if (sFDMsg.length() > 0) {
        CDeviceFactory::FareDisplay_ShowHelpMsg(DevIndex_Second, sFDMsg);
    }
    if (bAlarm) {
        CDeviceFactory::StartAlarm(DevIndex_Second, 0);
    }

    RemoteMsgMgr::GetSingleInst()->SendLaneReportEventReq(1, nEventId, sMsg);

    bool bRlt = CMessageBox::Information_Help(sTitle, sMsg, sHelp, CMessageBox::Style_OkCancel,
                                             NULL, allowRemoteCtrl);

    if (bAlarm) {
        CDeviceFactory::StopAlarm(DevIndex_Second);
    }

    return bRlt;
}

void CLaneState_VehInput::AddToPermitList(const QString &sVehPlate, const COperInfo &operInfo)
{
    CVehPassPermitInfo vehPermitInfo;
    vehPermitInfo.vehPlate = sVehPlate;
    vehPermitInfo.occurTime = QDateTime::currentDateTime();
    vehPermitInfo.operInfo = operInfo;

    QMutexLocker locker(&m_permitMt);
    m_PermitVehList.push_back(vehPermitInfo);
    while (m_PermitVehList.size() > 2) {
        m_PermitVehList.pop_front();
    }
    return;
}

bool CLaneState_VehInput::CheckVehInPermitList(const QString &sVehPlate,
                                               CVehPassPermitInfo &permitInfo)
{
    if (sVehPlate.isEmpty()) return false;
    QDateTime curTime = QDateTime::currentDateTime();
    QMutexLocker locker(&m_permitMt);
    foreach (CVehPassPermitInfo veh, m_PermitVehList) {
        if (veh.vehPlate == sVehPlate) {
            if (qAbs(veh.occurTime.secsTo(curTime)) < 300) {
                DebugLog(
                            QString("%1在许可名单内,时间%2").arg(veh.occurTime.toString("yyyyMMddhhmmss")));
                permitInfo = veh;
                return true;
            }
        }
    }
    return false;
}

bool CLaneState_VehInput::CheckAndInitForDelay(int nIndex, quint32 OBUID, CTransInfo *pTransInfo,
                                               int nDevType)
{
    if (DevIndex_First == nIndex) return false;

    CRsuDev_GB_EF *pRsuDev = (CRsuDev_GB_EF *)CDeviceFactory::GetRsuDev(nIndex);
    QString sNo;
    QDate curDate = QDate::currentDate();
    QString sExpiredTime;
    if (!Ptr_Info->bAutoDelay()) return false;

    if (DevChannel_ESAM == nDevType) {
        // obu
        if (!Ptr_Info->GetCheckOBUExpired()) return false;

        QString sIssuer = GB2312toUnicode(pTransInfo->OBUBaseInfo.ContractProvider, 4);
        if (sIssuer != QString("江西")) return false;
        sExpiredTime = QString::fromAscii(pTransInfo->OBUBaseInfo.szContractExpiredDate, 8);
        sNo = QString::fromAscii(pTransInfo->OBUBaseInfo.szContractSerialNumber);
        DebugLog(QString("obu 到期时间%1").arg(sExpiredTime));
    } else if (DevChannel_ETCCard == nDevType) {
        if (pTransInfo->IccInfo.ProCardBasicInfo.wNetWorkId != 3601) {
            return false;
        }
        sExpiredTime = QString::fromAscii(pTransInfo->IccInfo.ProCardBasicInfo.szExpireTime, 8);
        sNo = QString::fromAscii(pTransInfo->IccInfo.ProCardBasicInfo.szCardNo);
        DebugLog(QString("etc card 到期时间%1").arg(sExpiredTime));
    } else {
        return false;
    }
    QString S2024 = QString("20240101");
    if (sExpiredTime < S2024) {
        return false;
    }

    int nDelayType = 0;
    QDate lastDate = curDate;
    QString sLastDate = lastDate.toString("yyyyMMdd");
    if (sLastDate <= sExpiredTime) {
        lastDate = lastDate.addMonths(6);
        sLastDate = lastDate.toString("yyyyMMdd");
        if (sLastDate <= sExpiredTime) {
            return false;
        } else {
            nDelayType = ETCDelay::ETC_AddDate;  //延期
        }
    } else {
        nDelayType = ETCDelay::ETC_NewDate;  //续期
    }

    DebugLog(QString("etc delay, devtype:%1,delayType:%2").arg(nDevType).arg(nDelayType));
    bool bRlt = pRsuDev->InitForDelay(OBUID, nDevType);
    if (bRlt) {
        if (DevChannel_ETCCard == nDevType)
            ETCDelay::GetSingleStance()->CardDelay_Begin(
                        OBUID, pTransInfo->IccInfo.ProCardBasicInfo, nDelayType);
        else
            ETCDelay::GetSingleStance()->OBUDelay_Begin(OBUID, pTransInfo->OBUBaseInfo, nDelayType);

        QString sDevName = DevChannel_ETCCard == nDevType ? QString("ETC卡") : QString("电子标签");
        QString sDelayTypeName =
                DevChannel_ETCCard == nDelayType ? QString("延期") : QString("续期");
        DisplayTransError_ETC(nIndex, 0,
                              QString("%1 %2 处理,等待结果").arg(sDevName).arg(sDelayTypeName),
                              QString(""));
    }

    return bRlt;
}

void CLaneState_VehInput::StartVehStayOutTimer()
{
    //
    QMutexLocker locker(&m_vehStayOutMx);
    if (m_vehStayOutTimer.isActive()) m_vehStayOutTimer.stop();
    m_vehStayOutTimer.start(30000);
}

void CLaneState_VehInput::StopVehStayOutTimer()
{
    QMutexLocker locker(&m_vehStayOutMx);
    if (m_vehStayOutTimer.isActive()) m_vehStayOutTimer.stop();
}

void CLaneState_VehInput::FuncMenuProcess()
{
    // 初始化参数
    QList<CListData> lstMenuItem;
    lstMenuItem.push_back(CListData(0, "紧急抬杆", "紧急抬杆", 0, true, false));
    lstMenuItem.push_back(CListData(1, "参数查询", "参数查询", 0, true, false));
    lstMenuItem.push_back(CListData(2, "重连桌面读卡器", "重连桌面读卡器", 0, true, false));
    lstMenuItem.push_back(CListData(3, "重连卡机读卡器", "重连卡机读卡器", 0, true, false));
    if (Ptr_Info->IsExitLane()) {
        //出口，并且有卡机, 启用或禁用自助收费
        if (Ptr_Info->bCardMgrEnabled()) {
            lstMenuItem.push_back(
                        CListData(4, "切换人工收费", "切换人工收费", 0, Ptr_Info->IsExitLane(), false));
        } else {
            lstMenuItem.push_back(
                        CListData(4, "切换自助收费", "切换自助收费", 0, Ptr_Info->IsExitLane(), false));
        }

    } else {
        lstMenuItem.push_back(CListData(4, "禁用自动发卡", "禁用自动发卡", 0, false, false));
    }

    lstMenuItem.push_back(CListData(5, "重新初始化天线", "重新初始化天线", 0, true, false));

    if(Ptr_Info->IsExitLane())
    {
        CWtSysDev_ZC *pDev = CDeviceFactory::GetWeightDev();
        if (pDev) {
            if (pDev->bUploadWeightAgain())
                lstMenuItem.push_back(CListData(6, "人工称重", "人工称重", 0, true, false));
        }
    }

    if (Ptr_Info->IsExitLane() && Ptr_Info->bHaveCardMgr()) {
        lstMenuItem.push_back(CListData(7, "卡机退卡", "卡机退卡", 0, true, false));
    }

    if (Ptr_Info->bHaveFrontDev()) {
        if (Ptr_ETCCtrl->IsFrontRsuWork()) {
            lstMenuItem.push_back(CListData(8, "停用前天线", "停用前天线", 0, true, false));
        } else {
            lstMenuItem.push_back(CListData(8, "启用前天线", "启用前天线", 0, true, false));
        }
    } else {
        lstMenuItem.push_back(CListData(8, "停用前天线", "停用前天线", 0, false, false));
    }




    //    lstMenuItem.push_back(CListData(1, "", "", 0,  true, false));
    CListDlg dlg("功能菜单", "请按【数字】键选择", true, true, GetMainDlg());
    dlg.ShowList(lstMenuItem, 0);

    Ptr_RemoteCtrl->MenuSelectBegin("功能菜单", lstMenuItem, true, &dlg, SLOT(OnMenuSelected(int)));
    int nResult = dlg.doModalShow();
    Ptr_RemoteCtrl->MenuSelectEnd(&dlg, SLOT(OnMenuSelected(int)));

    switch (nResult) {
    case 1:  //紧急车，抬杆放行
        Ptr_ETCCtrl->SetVehLeadOut();
        GetMainDlg()->ShowLog("操作紧急抬杆业务");
        break;
    case 2:
        GetMainDlg()->CheckAndDownLoadParam();
        break;
    case 3: {
        CCardReader *pCardReader = CDeviceFactory::GetCardReader(0);
        CCardReader::CancelCardDetection(true);
        if (pCardReader) {
            pCardReader->CloseDev();
            if (!pCardReader->StartDev()) {
                GetMainDlg()->ShowLog(QString("桌面读写器重连失败"));
            } else {
                GetMainDlg()->ShowLog(QString("桌面读写器开始授权"));
                pCardReader->PsamAuthInit();
            }
        }
        break;
    }
    case 4: {
        for (int i = 1; i <= 2; ++i) {
            CCardReader *pCardReader = CDeviceFactory::GetCardReader(i);
            if (pCardReader) {
                QString sDevName = 1 == i ? QString("上读写器") : QString("下读写器");
                pCardReader->CloseDev();
                if (!pCardReader->StartDev()) {
                    GetMainDlg()->ShowLog(QString("%1,重连失败").arg(sDevName));
                } else {
                    GetMainDlg()->ShowLog(QString("%1,开始授权处理").arg(sDevName));
                    pCardReader->PsamAuthInit();
                }
            }
        }
        break;
    }
    case 6:
        if(1) {
            GetMainDlg()->ShowRsuSelectionDialog();

        }
        break;
    case 5: {
        Ptr_Info->MakeCardMgrEnabled(!Ptr_Info->bCardMgrEnabled());
        GetMainDlg()->RefreshDevStatus();
        GetMainDlg()->RefreshLaneInfo();
        break;
    }
    case 9: {
        if (Ptr_Info->IsExitLane()){
            bool bWork = Ptr_ETCCtrl->IsFrontRsuWork();
            if (bWork) {
                DebugLog(QString("停用前天线"));
            } else {
                DebugLog(QString("启用前天线"));
            }
            Ptr_ETCCtrl->SetFrontRsuState(!bWork);
            break;
        }
    }
    case 8: {
        if (Ptr_Info->IsExitLane() && Ptr_Info->bHaveCardMgr()) {
            CPaymentMachine *pCardMgr = CDeviceFactory::GetPayMentMgr();
            if (pCardMgr) {
                pCardMgr->BackCard(1);
                DebugLog(QString("上工位退卡"));
                SleeperThread::msleep(100);
                pCardMgr->BackCard(2);
                DebugLog(QString("下工位退卡"));
            }
        }
        break;
    }
    case 7: {
        if (Ptr_Info->IsExitLane()){
            CWtSysDev_ZC *pWeightDev = CDeviceFactory::GetWeightDev();
            if (pWeightDev) {
                pWeightDev->UploadVehWeightAgain();
            }
            break;
        }
        else
        {
            bool bWork = Ptr_ETCCtrl->IsFrontRsuWork();
            if (bWork) {
                DebugLog(QString("停用前天线"));
            } else {
                DebugLog(QString("启用前天线"));
            }
            Ptr_ETCCtrl->SetFrontRsuState(!bWork);
            break;
        }
    }
    }
}

int CLaneState_VehInput::DoSimulateMenu()
{
    // 初始化参数
    QList<CListData> lstMenuItem;
    int nResult = 1;
    lstMenuItem.push_back(CListData(0, "强制落杆", "强制落杆", 0, true, false));
    lstMenuItem.push_back(CListData(1, "超长车", "超长车", 0, true, false));
    if (Ptr_Info->bHaveFrontDev()) {
        bool bEnable = Ptr_Info->bHaveFrontDev();
        lstMenuItem.push_back(CListData(2, "增加车辆", "添加一辆待处理车辆", 0, bEnable, false));
        lstMenuItem.push_back(CListData(3, "删除异常车", "删除首辆异常车", 0, bEnable, false));
    }

    CListDlg dlg("模拟菜单", "请按【数字】键选择", true, true, GetMainDlg());
    dlg.ShowList(lstMenuItem, 0);
    nResult = dlg.doModalShow();

    if (0 == nResult) {
        return 0;
    }

    switch (nResult) {
    case 1: {
        QString sTitle = QString("提示");
        QString sMsg = QString("确认落杆?");
        QString sHelp;
        if (!CMessageBox::Information_Help(sTitle, sMsg, sHelp, CMessageBox::Style_OkCancel,
                                           NULL, true))
            return 0;
        DebugLog("模拟落杆");
        Ptr_ETCCtrl->SimulateDownBar();
        GetMainDlg()->RefreshPassVehQueue();
        GetMainDlg()->RefreshTradeVehQueue();
        SetOpState(opState_None);
        QString stime = QTime::currentTime().toString("hhmmss");
        StdInfoLog(LogKey::OneKey_Other, LogKey::Other_ManualOperator, stime, QString(""),
                   QString("模拟落杆"));

        break;
    }
    case 2: {
        QString sTitle = QString("提示");
        QString sMsg = QString("确认长车处理?");
        QString sHelp;
        if (!CMessageBox::Information_Help(sTitle, sMsg, sHelp, CMessageBox::Style_OkCancel,
                                           NULL, true))
            return 0;
        DebugLog("超长车处理");
        Ptr_ETCCtrl->DoLongVeh();

        break;
    }
    case 3: {
        CTransInfo *pTransInfo = new CTransInfo();
        Ptr_ETCCtrl->AddTransVehToList(1, pTransInfo, true);
        DebugLog(QString("增加异常车,落杆"));
        Ptr_ETCCtrl->SetRefusePass();
        break;
    }
    case 4: {
        CTransInfo transInfo;
        if (Ptr_ETCCtrl->bAbnormalVehOfTheFirstVehInQueue(transInfo)) {
            CTransInfo *pTransInfo = Ptr_ETCCtrl->RemoveTransInfoFromQue(true, true);
            if (pTransInfo) {
                delete pTransInfo;
                if (Ptr_ETCCtrl->bAllowllContinuePass(false)) {
                    Ptr_ETCCtrl->SetAllowPass(true);
                }
            }
        }

        break;
    }
    }
    return nResult;
}

bool CLaneState_VehInput::HolidayFreeMenu(int nVehClass, bool &bManNum8)
{
    bManNum8 = false;
    CHolidayFreeTable *pHolidayTable =
            (CHolidayFreeTable *)CParamFileMgr::GetParamFile(cfHolidayFree);
    if (!pHolidayTable) {
        DebugLog(QString("无节假日免费名单"));
        return false;
    }

    QString sError;
    bool bHolidayFree = pHolidayTable->IsHolidayFree((CVehClass)nVehClass, sError);

    if (!bHolidayFree) return false;
    if (bHolidayFree) {
        DebugLog(QString("节假日免费车辆,车型：%1").arg(nVehClass));
    }

    // 初始化参数
    QString sFDMsg = QString("等待确认座位数");
    CDeviceFactory::FareDisplay_ShowHelpMsg(DevIndex_Second, sFDMsg);
    this->ShowMessage(QString("请选择车辆类型"));
    QList<CListData> lstMenuItem;
    lstMenuItem.push_back(CListData(0, "7座以下(含7座)", "7座及以下车辆", 0, true, false));
    lstMenuItem.push_back(CListData(1, "7座以上车辆", "7座以上车辆", 0, true, false));

    CListDlg dlg("节假日免费", "请按【数字】键选择", true, false, GetMainDlg());
    dlg.ShowList(lstMenuItem, 0);

    Ptr_RemoteCtrl->MenuSelectBegin("节假日免费", lstMenuItem, true, &dlg,
                                    SLOT(OnMenuSelected(int)));
    int nResult = dlg.doModalShow();
    Ptr_RemoteCtrl->MenuSelectEnd(&dlg, SLOT(OnMenuSelected(int)));
    if (2 == nResult) {
        DebugLog(QString("节假日免费先择7座以上车辆"));
        bManNum8 = true;
    }
    return 1 == nResult;
}
//输入车牌
bool CLaneState_VehInput::InputPlate()
{
    DebugLog(QString("进入InputPlate函数"));
    m_IsInputingVLP = true;
    VP_COLOR nVlpColor = VP_COLOR_NONE;
    QString sPlate;
    if (!m_bInputPlate) {
        //还未输入过任何车牌,采用识别车牌为初始值,进行确认
        nVlpColor = (VP_COLOR)m_nAutoPlateColor;
        sPlate = m_sAutoVehPlate;
    } else {
        //如果输入过车牌号, 取输入过的车牌
        nVlpColor = (VP_COLOR)m_nInputPlateColor;
        sPlate = m_sInputVehPlate;
    }

    FormInputPlate dlgInput(GetMainDlg());
    if (dlgInput.EditPlate(sPlate, nVlpColor)) {
        dlgInput.GetInputResult(sPlate, nVlpColor);
        SetInputVehPlate(sPlate, nVlpColor);
        // 人工修改车牌后，查询车型库
        if (Ptr_Info->bUseVehLib() && !sPlate.isEmpty()) {
            // 调用异步查询函数获取车辆信息
            CVehTypeLibMgr::GetVehTypeLibMgr()->AsyncGetVehResult(sPlate, nVlpColor);
            DebugLog(QString("人工修改车牌后查询车型库：车牌=%1, 颜色=%2").arg(sPlate).arg(nVlpColor));
        }
        m_IsInputingVLP = false;
        DebugLog(QString("准备执行MoveMgrHead函数"));
        MoveMgrHead();
        return true;
    } else {
        m_IsInputingVLP = false;
        DebugLog(QString("准备执行MoveMgrHead函数"));
        MoveMgrHead();
        return false;
    }
    MoveMgrHead();
}

//设置输入车型
void CLaneState_VehInput::SetInputVehClass(CVehClass vehClass)
{
    CAbstractState::SetInputVehClass(vehClass);
    RefreshVehClassShow();
    //选择了车型后，需要刷新一下计重的显示(限载跟车型相关)
    GetMainDlg()->NotifyWeightDataChange();
}

//设置输入车种
void CLaneState_VehInput::SetInputVehType(CUnionVehType vehType)
{
    CAbstractState::SetInputVehType(vehType);
    RefreshVehTypeShow();
    //通知远程控制，车种数据发生变化, 车种是单独输入的，所以在设置方法里配置
    Ptr_RemoteCtrl->RefreshVehInput();
}

//设置输入车牌
void CLaneState_VehInput::SetInputVehPlate(const QString &sVehPlate, int nVLPColor)
{
    CAbstractState::SetInputVehPlate(sVehPlate, nVLPColor);
    RefreshVehPlateShow();
}

//设置输入轴型
void CLaneState_VehInput::SetInputAxleType(const QString &axleType)
{
    CAbstractState::SetInputAxleType(axleType);
    RefreshAxleTypeShow();
}
//清除输入
void CLaneState_VehInput::ResetInput()
{
    CAbstractState::ClearInputVeh();
    RefreshVehClassShow();
    RefreshVehTypeShow();
    RefreshVehPlateShow();
    RefreshAxleTypeShow();

    //变车型后，需要刷新一下计重的显示(限载跟车型相关)
    GetMainDlg()->NotifyWeightDataChange();
}

//更新车型显示
void CLaneState_VehInput::RefreshVehClassShow()
{
    FormVehInfo *pFrmInput = (FormVehInfo *)m_pStateUI;
    pFrmInput->ShowVehClass(m_InputVehClass);
}

//更新车牌显示
void CLaneState_VehInput::RefreshVehPlateShow()
{
    FormVehInfo *pFrmInput = (FormVehInfo *)m_pStateUI;
    pFrmInput->ShowVehPlate((VP_COLOR)m_nInputPlateColor, m_sInputVehPlate);
}

//更新轴型显示
void CLaneState_VehInput::RefreshAxleTypeShow()
{
    FormVehInfo *pFrmInput = (FormVehInfo *)m_pStateUI;
    pFrmInput->ShowAxleType(m_sInputAxleType);
}

//更新车种显示
void CLaneState_VehInput::RefreshVehTypeShow()
{
    FormVehInfo *pFrmInput = (FormVehInfo *)m_pStateUI;
    pFrmInput->ShowVehType(m_InputVehType);
}

int CLaneState_VehInput::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    StopVehStayOutTimer();
    int nRlt = 0;
    if (mtcKeyEvent->isVehClassKey()) {
        ProcessKeyEvent_VehClassInput(mtcKeyEvent);
        nRlt = 1;
        mtcKeyEvent->setKeyType(KC_VehClass);
    } else if (mtcKeyEvent->isVehTypeKey()) {
        ProcessKeyEvent_VehTypeInput(mtcKeyEvent);
        nRlt = 1;
        mtcKeyEvent->setKeyType(KC_Func);
    }

    if (nRlt != 0) {
        return nRlt;
    }

    QString sError;
    if (mtcKeyEvent->isFuncKey()) {
        int nFuncKey = mtcKeyEvent->func();
        mtcKeyEvent->setKeyType(KC_Func);
        switch (nFuncKey) {
        case KeySimulate: {
            if (1 == DoSimulateMenu()) {
                QString stime = QTime::currentTime().toString("hhmmss");
                StdInfoLog(LogKey::OneKey_Other, LogKey::Other_ManualOperator, stime,
                           QString(""), QString("模拟落杆"));
                //判断锁杆状态
                if (CDeviceFactory::GetIOCard()->IsLockBar()) {
                    CDeviceFactory::GetIOCard()->UnLockBar();
                }
            }
            break;
        }

        case KeyCanopyLight: {
            bool bIsGreen = CDeviceFactory::GetIOCard()->GetDOPortStatus(DO_CanopyLightGreen);
            if (bIsGreen)
                ShowErrorMessage(QString("雨棚红灯开"));
            else
                ShowErrorMessage(QString("雨棚绿灯开"));
            CDeviceFactory::GetIOCard()->SetCanopyLight(!bIsGreen);

            break;
        }
        case KeyShift: {
            if (Ptr_ETCCtrl->HasTransOkVehInLane()) {
                DebugLog(QString("车道内有已交易车辆，无法下班"));
                ShowMessage("车道内有已交易车辆,无法下班");
                return 1;
            }
            time_t curTime = QDateTime::currentDateTime().toTime_t();
            if (Ptr_Info->bHaveFrontDev()) {
                CTransInfo *pTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_First);
                if (pTransInfo->transState == CTransInfo::Ts_WaitOpResult) {
                    if (curTime > pTransInfo->transStateTime) {
                        if (curTime - pTransInfo->transStateTime < 10) {
                            DebugLog(QString("前天线正在交易无法下班"));
                            ShowMessage("前天线正在交易,无法下班");
                            return 1;
                        }
                    }
                }
            }
            CAbstractState::SetPause(true);

            /*
                if (Ptr_ETCCtrl->GetCurTransInfo(0, 0)->transState ==
                CTransInfo::Ts_WaitOpResult) { ShowErrorMessage("车道内有正在交易车辆,请稍后");
                    return 1;
                }*/

            QString sMsg = "是否确认下班?";
            if (CMessageBox::Information(sMsg)) {
                Ptr_ETCCtrl->UnLogin();
                // if(!Update::readyToUpdate())
                CETCLaneCtrl::GetETCLaneCtrl()->ChangeToUnLoginState();
            } else {
                CAbstractState::SetPause(false);
            }
            return 1;
        }
        case KeyLock: {
            bool isLockBar = CDeviceFactory::GetIOCard()->IsLockBar();
            QString sMsg;
            if (isLockBar) {
                sMsg = "是否取消锁杆?";
            } else {
                sMsg = "是否确认锁杆?";
            }
            if (CMessageBox::Information(sMsg)) {
                if (isLockBar) {
                    CDeviceFactory::GetIOCard()->UnLockBar();
                } else {
                    CDeviceFactory::GetIOCard()->LockBar();
                }
            }
            break;
        }
        case KeyFunc: {
            FuncMenuProcess();
            return 1;
        }
        case KeyDel: {
            if(Ptr_Info && Ptr_Info->GetCardMgrType()!=31)
                Capture();
            //更改车牌
            //if(Ptr_Info && Ptr_Info->GetCardMgrType()==31)
            //    DebugLog(QString("机械臂设备准备进入修改车牌"));
            InputPlate();
            //通知远控车辆录入信息变化
            Ptr_RemoteCtrl->RefreshVehInput();
            //此时车辆信息有可能输入完成，检查是否打开读写器
            CheckCardOperation();
            break;
        }
        case KeyChangeAxis: {
            //                COperInfo authOper;
            //                FormLogin dlgAuth(GetMainDlg());
            //                if (dlgAuth.auth(authOper)) {

            //                }

            //改轴,修改计重信息
            CFuncMenu::GetFunMenu()->ShowMenu_ChangeAxle();
            //由改轴后，重新填写界面输入轴组信息
            if (VehWeightInfo::GetVehWeightInfo()->GetVehCount() > 0) {
                CVehAxisInfo firstAxisInfo;
                VehWeightInfo::GetVehWeightInfo()->GetFirstVeh(&firstAxisInfo);
                SetInputAxleType(QString::number(firstAxisInfo.GetConfirmedAxisGroup()));
            }
            //通知远控车辆录入信息变化
            Ptr_RemoteCtrl->RefreshVehInput();
            break;
        }
        case KeyMotorcade:  //车队
        {
            if (Ptr_ETCCtrl->GetAllVehCountInQue() > 0) {
                ShowErrorMessage("车道内有未离开车辆,无法操作车队");
                return 1;
            }

            /*
                if (Ptr_ETCCtrl->GetCurTransInfo(0, 0)->transState ==
                CTransInfo::Ts_WaitOpResult) {
                    ShowErrorMessage("车道内有正在交易车辆,无法操作车队");
                    return 1;
                }*/
            Ptr_ETCCtrl->ChangeToMotorcade();
            break;
        }
        case KeyEsc: {  //取消键
            if (m_InputVehType == UVT_Normal) {
                //清除所有输入内容
                if (m_InputVehClass == VC_None) {
                    if (Ptr_Info->IsExitLane() && Ptr_Info->bHaveCardMgr()) {
                        CTransInfo *pcurTransInfo =
                                Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
                        if (pcurTransInfo) {
                            if (!pcurTransInfo->hasWriteCard_Exit()) {
                                CDeviceFactory::GetPayMentMgr()->BackAllCard();
                            }
                        }
                    }
                }
                ResetInput();
                //此时关闭读卡
                CCardReader::CancelCardDetection();
            } else {
                //如果车种不是普通车, 复位成普通车
                SetInputVehType(UVT_Normal);
            }
            //通知远控车辆录入信息变化
            Ptr_RemoteCtrl->RefreshVehInput();
            break;
        }

        case KeyForceShift:  // L 队列减车
        {
            break;
        }

        case KeyRight:  //右箭头，查看抓拍图像
        {
            //            QString sError =  QString("鲁A123456[%1]在[%2]道交易成功,请核实")
            //                    .arg(QDateTime::currentDateTime().toString("hh:mm"))
            //                    .arg(102);
            //            CWasteInfo wasteInfo;
            //            wasteInfo.bType = 9;
            //            wasteInfo.dtWasteTime = QDateTime::currentDateTime();
            //            wasteInfo.sDesc = sError;
            //            GetMainDlg()->Emit_ETCShowTransInfo(0, wasteInfo);
            //            GetMainDlg()->Emit_ETCShowPrompt(0, sError);

                QString sPlate;
                int nColor = 0;
                GetAutoVehPlate(sPlate, nColor);
                GetMainDlg()->ShowImages(nColor, sPlate);
                return 1;
                break;
            }
            case KeyGan: {
                ProcessKeyEvent_GanTongKa();
                break;
            }
            case KeyOther:  //其它免征车，选车种
            {
                if (Ptr_Info->IsEntryLane()) {
                    CVehInfo vehInfo;
                    GetCurVehInfo(vehInfo, true);
                    if (vehInfo.IsEmpty()) {
                        ShowErrorMessage(QString("请输入车辆信息"));
                        break;
                    }
                    CUnionVehType otherVehType = UVT_Normal;
                    int nRlt = CFuncMenu::GetFunMenu()->ShowMenu_OtherFree(otherVehType, false);
                    if (0 == nRlt) {
                        break;
                    }
                    DoOhterFreeVeh(otherVehType);
                } else {
                    CVehInfo vehInfo;
                    GetCurVehInfo(vehInfo, true);
                    if (vehInfo.IsEmpty()) {
                        ShowErrorMessage(QString("请输入车辆信息"));
                        break;
                    }

                    if (isCar(vehInfo.VehClass)) {
                        break;
                    }
                    CUnionVehType otherVehType = UVT_Normal;
                    int nRlt =
                        CFuncMenu::GetFunMenu()->ShowMenu_OtherFree(otherVehType, true, true);
                    if (0 == nRlt) break;
                    DoOhterFreeVeh(otherVehType);
                }
                break;
            }
            case KeyBigTruck: {
                //判断是否已经输入了车型
                CVehInfo vehInfo;
                GetCurVehInfo(vehInfo);
                QString sPlate = GB2312toUnicode(vehInfo.szVehPlate);
                if (vehInfo.VehClass == VC_None) {
                    GetMainDlg()->ShowPromptMsg("请输入车型!", true);
                } else if (!isTruck(vehInfo.VehClass)) {
                    GetMainDlg()->ShowPromptMsg("请输入货车车型!", true);
                } else {
                    //大件运输,先查名单，如果在名单内，直接显示单号
                    QString sTickNo = "";
                                    CBigVehInfo bigVehInfo;
                if (HandleBigVehSelection(sPlate, vehInfo.nVehPlateColor, bigVehInfo)) {
                    vehInfo.GBVehType = UVT_BigTruck;
                    sTickNo = bigVehInfo.cerNo;
                }
                FormInputBigTruck dlgInputBigTruck(GetMainDlg());
                if (dlgInputBigTruck.InputTickNo(sTickNo)) {
                    //输入了大件运输证号  sTickNo;
                    DebugLog(QString("大件运输证号:").arg(sTickNo));
                    SetInputVehType(UVT_BigTruck);
                    SetCertNo(sTickNo);
                }
            }
            break;
        }
        case KeyReprint: {
            ProcessKeyReprint();
            break;
        }
        default:
            mtcKeyEvent->setKeyType(KC_NONE);
            break;
        }
    }
    return 0;
}

/**
 * @brief
 * @param
 * @return 返回true 后续etclanectrl会继续处理。false则中断处理
 */
bool CLaneState_VehInput::ProcessDIEvent(qint32 nDI, bool bStatus, QString &sError)
{
    Q_UNUSED(sError)
    QString sLastLoopStatus = Ptr_ETCCtrl->GetCurLoopStatus();
    qint64 curTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    if (DI_LoopFront == nDI) {
        CTransInfo *pVehTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_First, 0);
        CTransInfo *pLastTransInfo = Ptr_ETCCtrl->GetLastTransInfo(DevIndex_First);
        if (!pVehTransInfo) return true;

        if (bStatus) {
            /*
            if (sLastLoopStatus == QString("11")) {
                //前队列为空，并且当前车辆不是等待车--该函数处理一定要在ETCLanCtrl之前
                if (0 == Ptr_ETCCtrl->GetVehCount_FrontQue()) {
                    if (0 == pVehTransInfo->dwOBUID && (!pLastTransInfo->bWaitOpResult())) {
                        DisplayTransError_ETC(DevIndex_First, CSpEventMgr::SpEvent_NoOBU,
                                              QString(""), QString(""));
                        DebugLog("无交易判断无电子标签");
                    } else if (qAbs(curTime - pVehTransInfo->m_BeginTime) > 120000) {
                        DisplayTransError_ETC(DevIndex_First, CSpEventMgr::SpEvent_NoOBU,
                                              QString(""), QString(""));
                        DebugLog("超时判断无电子标签");
                    } else {
                    }
                }
            }*/
        } else {
        }
        return true;
    } else if (DI_LoopBack == nDI) {
        if (bStatus) {
            // CIODevStatus ioStatus;
            // CDeviceFactory::GetIOCard()->GetDIPortStatus(DI_LoopExist, ioStatus);
            if (1) {
                if (Ptr_Info->bHaveCardMgr()) {
                    DebugLog("收回卡头");
                    if (Ptr_Info->IsEntryLane()) {
                        // CDeviceFactory::GetCardMachine()->MoveCardHead(false);
                    } else {
                        /*
                        CDeviceFactory::GetPayMentMgr()->RecycleTicket();
                        SleeperThread::msleep(50);
                        CDeviceFactory::GetPayMentMgr()->SetVehState(false);
                        */
                    }
                }
            }
        }
        return true;
    } else if (DI_LoopExist == nDI) {
        if (bStatus) {
            StartVehStayOutTimer();
        } else
            StopVehStayOutTimer();
    }
    return false;
}

void CLaneState_VehInput::DisplayTransError_ETC(int nIndex, quint32 nErrorCode,
                                                const QString &sError, const QString &sFDMsg)
{
    QString sMsg = sError;
    bool bAlarm = false;
    bool bStop = false;
    QString sFareDispMsg;
    if (nErrorCode > 0) {
        CSpEvent spEvent = CSpEventMgr::GetSpEvent(nErrorCode, Ptr_Info->IsExitLane());
        if (spEvent.nSpId > CSpEventMgr::SpEvent_None &&
                (spEvent.nSpId != CSpEventMgr::SpEvent_Ignore)) {
            sFareDispMsg = spEvent.FareDisplayInfo;
            if (sFareDispMsg.length() == 0) {
                sFareDispMsg = sError;
            }
            bAlarm = spEvent.bAlarm;
            bStop = spEvent.bStop;
        }
        if (sError.length() == 0) sMsg = spEvent.DisplayInfo;
    }

    if (sFDMsg.length() > 0) {
        sFareDispMsg = sFDMsg;
    }

    if (sFareDispMsg.length() > 0) {
        CFareDisplayer_GB *pFD = CDeviceFactory::GetETCFareDisPlayer(nIndex);
        DebugLog(QString("费显%1显示信息:%2").arg(nIndex).arg(sFareDispMsg));
        if (pFD) {
            pFD->ShowError(sFareDispMsg, bStop);
        }
    }
    /*
    if (bAlarm && 0 != nIndex) {
        CDeviceFactory::StartAlarm(nIndex, 10000);
    }*/

    if (sMsg.length() > 0) {
        // ShowLog(sMsg);
        GetMainDlg()->Emit_ETCShowPrompt(nIndex, sMsg);
    }
    return;
}

bool CLaneState_VehInput::ProcessRsuEvent_New(int nIndex, quint32 OBUID, int nEvent,
                                              int &nErrorCode, QString &sError)
{
    bool bRlt = false;
    QString str = QTime::currentTime().toString("hhmmsszzz");
    CRsuDev *pDev = CDeviceFactory::GetRsuDev(nIndex);
    QString sFDMsg;
    if (nEvent >= CRsuDev::RsuEvent_OBUBaseInfo && nEvent <= CRsuDev::RsuEvent_IccInfo) {
        QString sOpStateName;
        if (!CheckAndSetOpState(opState_None, sOpStateName)) {
            GetMainDlg()->Emit_ETCShowPrompt(nIndex, QString("%1,ETC暂停交易").arg(sOpStateName));
            pDev->StopDeal(OBUID);
            return false;
        }

        if (CheckRsuPause()) {
            GetMainDlg()->Emit_ETCShowPrompt(nIndex, QString("%1,ETC暂停交易").arg(sOpStateName));
            pDev->StopDeal(OBUID, true);
            DebugLog(QString("天线暂停交易"));
            return false;
        }
    }
    switch (nEvent) {
    case CRsuDev::RsuEvent_RsuState: {
        const CRsuBaseInfo_Jx *pRsuBaseInfo = pDev->GetRsuBaseInfo();
        for (int i = 0; i < pRsuBaseInfo->bPsamNum; ++i) {
            QString sPsam = Raw2HexStr(pRsuBaseInfo->PsamInfo[i].TerminateCode, 6);
            ShowLog(QString("天线:%1,终端机[%2]编号:%3").arg(nIndex).arg(i).arg(sPsam));
        }
        return true;
    }

    case CRsuDev::RsuEvent_OnPower: {
        ShowLog(QString("天线[%1]上电").arg(nIndex));
        return true;
        break;
    }
    case CRsuDev::RsuEvent_OBUBaseInfo: {
        if (nIndex == DevIndex_Second && CheckLastOBUPause(OBUID)) {
            GetMainDlg()->Emit_ETCShowPrompt(nIndex, QString("天线暂停交易"));
            pDev->StopDeal(OBUID);
            return false;
        }
        bRlt = ProcesssOBUBaseInfo_Light(nIndex, OBUID, nErrorCode, sError, sFDMsg);
        CTransInfo *pVehTranInfo = Ptr_ETCCtrl->GetCurTransInfo(nIndex, OBUID);
        ErrorLog(QString("ProcessOBUBaseInfo:%1").arg(sError));
        if (bRlt) {
            pDev->ContinueDeal(OBUID);
            GetMainDlg()->Emit_ETCShowPrompt(nIndex, sError);
            ShowLog(sError);
        } else {
            if (nIndex == DevIndex_Second) {
                PauseOBU(OBUID);
            }
            pDev->StopDeal(OBUID);
            //                pVehTranInfo->ClearTransInfo();
            DisplayTransError_ETC(nIndex, nErrorCode, sError, sFDMsg);

            if (CSpEventMgr::SpEvent_Ignore == nErrorCode || (!pVehTranInfo)) {
                //忽略掉交易后天线干扰导致的连续天线数据
                break;
            }
            CSpEvent spEvent = CSpEventMgr::GetSpEvent(nErrorCode, Ptr_Info->IsExitLane());

            if (spEvent.nFaileCause > 0) {
                StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_TradeStatus, str,
                           QString::fromAscii(pVehTranInfo->OBUBaseInfo.szTime),
                           QString::number(2));
                StdErrorLog(LogKey::OneKey_TranResult, LogKey::TranResult_LogicalFailCause, str,
                            QString::fromAscii(pVehTranInfo->OBUBaseInfo.szTime),
                            QString::number(spEvent.nFaileCause));
            } else {
                StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_TradeStatus, str,
                           QString::fromAscii(pVehTranInfo->OBUBaseInfo.szTime),
                           QString::number(3));
            }

            pVehTranInfo->SetTransState(CTransInfo::Ts_WaitTransBegin);
        }
        break;
    }
    case CRsuDev::RsuEvent_OBUVehInfo: {
        bool bRepeat = false;
        bRlt = ProcessOBUVehInfo_Light(nIndex, OBUID, nErrorCode, sError, sFDMsg, bRepeat);
        ErrorLog(QString("ProcessOBUVehInfo:%1").arg(sError));

        if (bRlt) {
            pDev->ContinueDeal(OBUID);
            if (sError.length() > 0) this->ShowMessage(sError);
        } else {
            if (nIndex == DevIndex_Second) {
                PauseOBU(OBUID);
            }
            pDev->StopDeal(OBUID, bRepeat);
            CTransInfo *pVehTranInfo = Ptr_ETCCtrl->GetCurTransInfo(nIndex, OBUID);
            //              if (pVehTranInfo) pVehTranInfo->ClearTransInfo();
            DisplayTransError_ETC(nIndex, nErrorCode, sError, sFDMsg);
            CSpEvent spEvent = CSpEventMgr::GetSpEvent(nErrorCode, Ptr_Info->IsExitLane());
            if (!pVehTranInfo) break;
            if (spEvent.nFaileCause > 0) {
                StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_TradeStatus, str,
                           QString::fromAscii(pVehTranInfo->OBUBaseInfo.szTime),
                           QString::number(2));
                StdErrorLog(LogKey::OneKey_TranResult, LogKey::TranResult_LogicalFailCause, str,
                            QString::fromAscii(pVehTranInfo->OBUBaseInfo.szTime),
                            QString::number(spEvent.nFaileCause));
            } else {
                StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_TradeStatus, str,
                           QString::fromAscii(pVehTranInfo->OBUBaseInfo.szTime),
                           QString::number(3));
            }
            if (!bRepeat) pVehTranInfo->SetTransState(CTransInfo::Ts_WaitTransBegin);
        }
        break;
    }
    case CRsuDev::RsuEvent_IccInfo: {
        bool bStop = false;
        if (Ptr_Info->IsEntryLane()) {
            bRlt = ProcessIccInfo_Entry(nIndex, OBUID, nErrorCode, sError, sFDMsg, bStop);
        } else {
            bRlt = ProcessIccInfo_Exit(nIndex, OBUID, nErrorCode, sError, bStop);
        }

        CTransInfo *pTranInfo = Ptr_ETCCtrl->GetCurTransInfo(nIndex, OBUID);
        if (!bRlt) {
            if (nIndex == DevIndex_Second) {
                PauseOBU(OBUID);
            }
            CSpEvent spEvent = CSpEventMgr::GetSpEvent(nErrorCode, Ptr_Info->IsExitLane());
            if (pTranInfo) {
                if (spEvent.nFaileCause > 0) {
                    StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_TradeStatus, str,
                               QString::fromAscii(pTranInfo->OBUBaseInfo.szTime),
                               QString::number(2));
                    StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_LogicalFailCause,
                               str, QString::fromAscii(pTranInfo->OBUBaseInfo.szTime),
                               QString::number(spEvent.nFaileCause));
                } else {
                    StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_TradeStatus, str,
                               QString::fromAscii(pTranInfo->OBUBaseInfo.szTime),
                               QString::number(3));
                }
            }
        }
        ErrorLog(QString("ProcessIccInfo:%1, %2").arg(sError).arg(bRlt));

        if (!bRlt) {
            if (bStop) {
                if (Ptr_Info->IsEntryLane() && nIndex != DevIndex_First) {
                    if (nErrorCode == CSpEventMgr::SpEvent_OutTimeOBU &&
                            !pTranInfo->m_bWhiteVeh) {
                        if (this->CheckAndInitForDelay(nIndex, OBUID, pTranInfo,
                                                       DevChannel_ESAM))
                            return true;
                    }
                    if (nErrorCode == CSpEventMgr::SpEvent_OutTimeCard &&
                            !pTranInfo->m_bWhiteVeh) {
                        if (this->CheckAndInitForDelay(nIndex, OBUID, pTranInfo,
                                                       DevChannel_ETCCard))
                            return true;
                    }
                }
                pDev->StopDeal(OBUID);
                if (pTranInfo) {
                    if (pTranInfo->m_bWhiteVeh) {
                        //直接放行
                        this->CompleteTransForWhiteVeh(true);
                        OnTransFinished(nIndex, true, nErrorCode, sError);
                        return true;
                    } else {
                        pTranInfo->SetTransState(CTransInfo::Ts_WaitTransBegin);
                    }
                }
            } else {
                if (pTranInfo) {
                    if (pTranInfo->transState == CTransInfo::Ts_IsReadingIcc) {
                        pTranInfo->SetTransState(CTransInfo::Ts_WaitIccInfo);
                    }
                }
            }
            DisplayTransError_ETC(nIndex, nErrorCode, sError, sFDMsg);
        } else {
            if (sError.length() > 0) {
                GetMainDlg()->Emit_ETCShowPrompt(nIndex, sError);
                ShowLog(sError);
            }
        }
        break;
    }
    case CRsuDev::RsuEvent_TransResult: {
        if (Ptr_Info->IsEntryLane()) {
            bRlt = ProcessB5FrameInfo_Entry(nIndex, OBUID, nErrorCode, sError);

            if (bRlt && Ptr_Info->bHaveCardMgr()) {
                if (nIndex == DevIndex_Second) {
                    CDeviceFactory::GetCardMachine()->MoveCardHead(false);
                    SleeperThread::msleep(80);
                    CDeviceFactory::GetCardMachine()->PlaySnd(CCardMgr::SndId_Thank);
                }
            }
        } else {
            bRlt = ProcessB5FrameInfo_Exit(nIndex, OBUID, nErrorCode, sError);
            if (bRlt && Ptr_Info->bHaveCardMgr()) {
                if (DevIndex_Second == nIndex) {
                    DebugLog("ETC 交易完毕收回卡机头");
                    CDeviceFactory::GetPayMentMgr()->SetVehState(false);
                }
            }
        }
        OnTransFinished(nIndex, bRlt, nErrorCode, sError);
        break;
    }
    case CRsuDev::RsuEvent_B7: {
        bRlt = this->ProcessB7FrameInfo(nIndex, OBUID, nErrorCode, sError);
        break;
    }
    case CRsuDev::RsuEvent_TransParent: {
        if (0 != nErrorCode) {
            DebugLog(QString("E2 返回status:%1").arg(nErrorCode));
            return false;
        }
        bRlt = ProcessE2FrameInfo(nIndex, OBUID, nErrorCode, sError);
        break;
    }
    default:
        break;
    }
    return bRlt;
}

bool CLaneState_VehInput::OnTransFinished(int nIndex, bool bResult, int nErrorCode,
                                          const QString &sError, bool bReTrans,
                                          CTransInfo *pTransInfo)
{
    CTransInfo *pLastTransInfo = pTransInfo;
    if (!pLastTransInfo) {
        pLastTransInfo = Ptr_ETCCtrl->GetLastTransInfo(nIndex);
    }
    
    // 【新增】交易完成时重置卡机定时器延时状态，为下次交易做准备
    if (DevIndex_Second == nIndex) {
        if (m_nTimerCardMgrInterval2Flag) {
            m_nTimerCardMgrInterval2Flag = false;
            DebugLog(QString("交易完成：重置后天线卡机定时器延时标志，天线%1").arg(nIndex));
        }
        if (m_nDelayedOBUID != 0) {
            DebugLog(QString("交易完成：清除已延时的OBUID记录，上次延时OBUID:%1").arg(m_nDelayedOBUID));
            m_nDelayedOBUID = 0; // 清除已延时的OBUID记录，为下次交易做准备
        }
    }
    
    if (bResult) {
        //以下控制费显显示
        if (pLastTransInfo->bTransFailed()) return true;
        FD_DisplayTransInfo(nIndex, pLastTransInfo, bReTrans);
        QString sVlp = GB2312toUnicode(pLastTransInfo->VehInfo.szVehPlate);
        ShowLog(QString("%1,%2").arg(sVlp).arg(sError));
        GetMainDlg()->Emit_ETCShowPrompt(nIndex, QString("%1交易完成放行").arg(sVlp));
        bool bClearTransInfo = nIndex == DevIndex_Second || nIndex == DevIndex_Manual;
        GetMainDlg()->Emit_ClearVehInfoFrm(bClearTransInfo);
    } else {
        QString sFDMsg;
        DisplayTransError_ETC(nIndex, nErrorCode, sError, sFDMsg);
    }
    return true;
}

bool CLaneState_VehInput::OnTransFinished_Manual(bool bResult, int nErrorCode,
                                                 const QString &sError, CTransInfo *pTransInfo)
{
    CTransInfo *pLastTransInfo = pTransInfo;
    if (!pLastTransInfo) {
        pLastTransInfo = Ptr_ETCCtrl->GetLastTransInfo(DevIndex_Manual);
    }

    if (bResult) {
        ClearVehInfo(true);
        GetMainDlg()->ShowPromptMsg(QString("等待来车"));
        FD_DisplayTransInfo(DevIndex_Second, pLastTransInfo, false);

    } else {
        QString sFDMsg;
        bool bAlarm = false;
        DisplayTransError_CPC(DevIndex_Second, sError, sFDMsg, bAlarm);
    }
    return true;
}

bool CLaneState_VehInput::ProcessOBUVehInfo_Light(int nIndex, quint32 OBUID, int &nErrorCode,
                                                  QString &sError, QString &sFDMsg, bool &bRepeat)
{
    bRepeat = false;
    nErrorCode = 0;
    sError.clear();
    DebugLog(QString("天线%1处理B3[%2]帧....").arg(nIndex).arg(OBUID));
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(nIndex, OBUID);
    if (!pCurTransInfo) {
        DebugLog(QString("当前车辆为空,nIndex:%1").arg(nIndex));
        sError = QString("后天线交易未检测到车辆");
        nErrorCode = CSpEventMgr::SpEvent_Other;
        return false;
    }

    CRsuDev *pRsuDev = CDeviceFactory::GetRsuDev(nIndex);
    const COBUVehInfo *pOBUVehInfo = pRsuDev->GetOBUVehInfo();
    COBUVehInfo obuVehInfo = *pOBUVehInfo;
    pOBUVehInfo = &obuVehInfo;

    CStdLog::StdLogVehInfo_OBUVehInfo(QString::fromAscii(pCurTransInfo->OBUBaseInfo.szTime),
                                      pOBUVehInfo);

    nErrorCode = 0;

    pCurTransInfo->curFrameId = 0xB3;
    pCurTransInfo->curFrameTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    if (0 != pOBUVehInfo->ErrorCode) {
        nErrorCode = CSpEventMgr::SpEvent_FrameError;
        pCurTransInfo->nErrorCode = pOBUVehInfo->ErrorCode;
        sError = QString("收到B3 帧数据错误,错误码[%1]").arg(pOBUVehInfo->ErrorCode);
        return false;
    }

    if (CTransInfo::Ts_WaitOBUVehInfo != pCurTransInfo->transState) {
        QString sState = pCurTransInfo->GetTransStateStr();
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sError = QString("收到B3 %1,不处理").arg(sState);
        return false;
    }

    if ((0 == OBUID) || (OBUID != pCurTransInfo->dwOBUID)) {
        ErrorLog(QString("车辆信息帧，OBU 不符,OBUID1 %1 OBUID2 %2 ")
                 .arg(OBUID)
                 .arg(pCurTransInfo->dwOBUID));
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sError = "B3帧标签ID错误，不予处理";
        return false;
    }

    pCurTransInfo->SetOBUVehInfo(pOBUVehInfo, NULL);

    //

    QString sVehPlate = GB2312toUnicode(pOBUVehInfo->szVehPlate);
    QString sLog = QString("OBU 车辆信息,车型:%1 车牌：%2 车牌颜色:%3 用户类型: %4")
            .arg(pOBUVehInfo->bVehClass)
            .arg(sVehPlate)
            .arg(pOBUVehInfo->nPlateColor)
            .arg(pOBUVehInfo->bUserType);

    DebugLog(sLog);

    ShowLog(
                QString("B3 车型:%1,用户类型:%2").arg(pOBUVehInfo->bVehClass).arg(pOBUVehInfo->bUserType));
    ShowLog(QString("B3 车牌号:%1,车牌颜色:%2")
            .arg(QString::fromLocal8Bit(pOBUVehInfo->szVehPlate))
            .arg(pOBUVehInfo->nPlateColor));

    QString sVehClassName = GetVehClassName((CVehClass)pOBUVehInfo->bVehClass);

    if (QryTransShare(MediaType_OBU, sVehPlate, sError)) {
        //在交易区显示提示信息
        CWasteInfo wasteInfo;
        wasteInfo.bType = 9;
        wasteInfo.sDesc = sError;
        GetMainDlg()->Emit_ETCShowTransInfo(nIndex, wasteInfo);
        return false;
    }

    char szProvider[8];
    memset(szProvider, 0, sizeof szProvider);
    memcpy(szProvider, pCurTransInfo->OBUBaseInfo.ContractProvider, 4);
    QString sOBUProvider = GB2312toUnicode(szProvider);

    CVehInfo VehInfo;
    VehInfo.Clear();
    VehInfo.PVehClass = (CVehClass)pOBUVehInfo->bVehClass;
    VehInfo.VehClass = (CVehClass)pOBUVehInfo->bVehClass;
    VehInfo.AutoVehClass = VehInfo.VehClass;
    VehInfo.GBVehType = GetGBVehTypeByUserType(pCurTransInfo->OBUVehInfo.bUserType);
    VehInfo.nVehClassWay = VehClassWay_OBU;

    VehInfo.nVehPlateColor = pOBUVehInfo->nPlateColor;
    if (9 == VehInfo.nVehPlateColor) {
        VehInfo.nVehPlateColor = 0;
    }
    memcpy(VehInfo.szVehPlate, pOBUVehInfo->szVehPlate, 12);

    if (QString("军车") == sOBUProvider) {
        VehInfo.GBVehType = UVT_Army;
    }

    pCurTransInfo->SetVehInfo(&VehInfo, NULL);
    if (Ptr_Info->IsEntryLane())
        RemoteMsgMgr::GetSingleInst()->SendETCTransMsg_Entry(pCurTransInfo, 2);
    else
        RemoteMsgMgr::GetSingleInst()->SendETCTransMsg_Exit(pCurTransInfo, 2);

    if (0 == sVehClassName.length()) {
        nErrorCode = CSpEventMgr::SpEvent_OBUVehClassErr;
        sError = QString("OBU车辆未知车型[%1]").arg(pOBUVehInfo->bVehClass);
        return false;
    }

    QString sPlate = GB2312toUnicode(pOBUVehInfo->szVehPlate);
    //出口因为疫苗车，该部分判断放在b4帧处理
    bool bYJVeh = CCardFileConverter::IsYJCard(sPlate, pOBUVehInfo->bUserType);
    if (Ptr_Info->IsEntryLane()) {
        /*
        if (pOBUVehInfo->bVehClass >= VC_Car2 && pOBUVehInfo->bVehClass <= VC_Car4) {
            QDateTime curTime = QDateTime::currentDateTime();
            if (curTime.time().hour() >= 2 && curTime.time().hour() <= 4) {
                nErrorCode = CSpEventMgr::SPEvent_StopPassByTime;
                sError = QString("客2以上车辆限时通行");
                return false;
            }
        }*/

        QDateTime curTime = QDateTime::currentDateTime();
        bool bPermit = Ptr_Info->CheckVehPassPermit(VehInfo.VehClass, curTime);
        if (!bPermit) {
            CVehPassPermitInfo permitInfo;
            if (!CheckVehInPermitList(sPlate, permitInfo)) {
                QString sVehName = GetVehClassName(VehInfo.VehClass);
                DebugLog(QString("%1,%2 限时通行").arg(sVehName).arg(sPlate));
                sError = QString("<%1,%2>夜间禁止通行").arg(sPlate).arg(sVehName);
                int nOpType = 0;
                QString sDetail;
                int nAxisNum = 0;
                bool OpRlt = false;
                if (CheckOtherXVehInfo(XEvent_VehPassPermit, VehInfo, nOpType, OpRlt, NULL,
                                       nAxisNum, sDetail)) {
                    if (Operate_WaitConfirm == nOpType) {
                        sError = QString("请按[赣通卡]键继续发卡");
                        bRepeat = true;
                        return false;
                    } else if (Operate_Pause == nOpType) {
                        sError = QString("客车夜间禁止行驶,等待确认");
                        bRepeat = true;
                        return false;
                    } else {
                    }
                } else {
                    nErrorCode = CSpEventMgr::SPEvent_StopPassByTime;
                    return false;
                }
            } else {
                pCurTransInfo->m_bPassPermit = true;
                pCurTransInfo->m_operInfo = permitInfo.operInfo;
                pCurTransInfo->m_AuthTime = permitInfo.occurTime;
            }
        }
    }

    // MTC后天线应该取称重信息

    CVehAxisInfo vehAxisInfo;
    int nAxisType = 0;
    bool bHaveWeightInfo = false;
    CVehAxisInfo *pVehAxisInfo = NULL;
    QString sCertNo;
    if ((Ptr_Info->IsMTCLane()) && (DevIndex_Second == nIndex)) {
        quint32 dwTotalWeight = 0, dwLimitWeight = 0;
        qint32 nOverRate = 0;
        int nAxisNum = 0;
        bHaveWeightInfo = GetCurVehWeightInfo(VehInfo.VehClass, vehAxisInfo, dwTotalWeight,
                                              dwLimitWeight, nOverRate);
        if (!bHaveWeightInfo) {
            if (VehInfo.VehClass > VC_Truck && (Ptr_Info->bCheckWeight())) {
                sError = QString("货车没有称重信息");
                sFDMsg = QString("无称重信息\n%1").arg(sPlate);
                return false;
            } else {
                DebugLog(QString("车辆%1:%2,无称重信息").arg(VehInfo.VehClass).arg(sPlate));
            }
        } else {
            if (VehInfo.VehClass > VC_Truck) {
                nAxisNum = vehAxisInfo.GetConfirmedSingleAxisNum();
                pVehAxisInfo = &vehAxisInfo;
                nAxisType = vehAxisInfo.GetConfirmedAxisGroup();
                if (!CheckVCAndAxis(VehInfo, nAxisNum, bYJVeh, sError, sFDMsg)) {
                    if (Ptr_Info->IsEntryLane()) {
                        return false;
                    } else {  //出口称重不匹配，以入口为准
                        pVehAxisInfo = NULL;
                        DebugLog(QString("etc出口称重车型%1与轴数%2不匹配,以入口为准")
                                 .arg(VehInfo.VehClass)
                                 .arg(nAxisNum));
                    }
                }
            }
        }

        CVehInfo inputVehInfo;
        GetCurVehInfo(inputVehInfo, false);
        QString sInputPlate = GB2312toUnicode(inputVehInfo.szVehPlate);
        if (sInputPlate.length() > 0 && sVehPlate != sInputPlate) {
            sError = QString("标签车牌[%1]与识别车牌不符[%2]").arg(sVehPlate).arg(sInputPlate);
            sFDMsg = QString("标签车牌\n%1\n与车辆车牌不符").arg(sVehPlate);

            VcrResult vcrResult;
            bool bRlt = CDeviceFactory::GetVCRDev()->GetFistVcrResult(vcrResult);
            if (bRlt) {
                if (vcrResult.sPlate == sVehPlate) {
                    DebugLog(QString("%1,但与车型识别队列首辆车车牌一致,缺省认为首辆车,vehclass:%2")
                             .arg(sError)
                             .arg(vcrResult.vehclass));
                    if (vcrResult.vehclass == VC_None) {
                        return false;
                    }
                    pCurTransInfo->SetVcrResult(&vcrResult);
                    VehInfo.AutoVehClass = vcrResult.vehclass;
                    QByteArray bPlate = UnicodetoGB2312(vcrResult.sPlate);
                    qsnprintf(VehInfo.szAutoVehPlate, sizeof VehInfo.szAutoVehPlate, "%s",
                              bPlate.constData());
                    VehInfo.nAutoVehPlateColor = vcrResult.nColor;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        }

        /*
        if (Ptr_Info->IsEntryLane()) {
            if (VehInfo.VehClass > VC_Truck && bHaveWeightInfo) {
                nAxisNum = vehAxisInfo.GetConfirmedSingleAxisNum();
                if (!CheckVCAndAxis(VehInfo, nAxisNum, bYJVeh, sError, sFDMsg)) {
                    return false;
                }
            }
        }*/

        if (bHaveWeightInfo) {
            DebugLog(
                        QString("ETC "
                                "交易取车辆称重信息,TotalWeight:%1,dwLimitWeight:%2,OverRate:%3,轴数:%4")
                        .arg(dwTotalWeight)
                        .arg(dwLimitWeight)
                        .arg(nOverRate)
                        .arg(nAxisNum));
        }
        if (Ptr_Info->IsEntryLane()) {
            int nOpType = 0;

            CBigVehInfo bigVehInfo;
            CBigVehInfo *pBigVehInfo = NULL;
            QString sDetail;
            if (VehInfo.VehClass > VC_Truck) {
                if (HandleBigVehSelection(sPlate, VehInfo.nVehPlateColor, bigVehInfo)) {
                    VehInfo.GBVehType = UVT_BigTruck;
                    pBigVehInfo = &bigVehInfo;
                }
            }
            if (nOverRate > 50 && VehInfo.VehClass > VC_Truck) {
                //货车超限,直接发送费显。总重 超重+提示

                sError = QString("货车超限禁止通行");
                quint32 dwOverWeight = dwTotalWeight - dwLimitWeight;
                sFDMsg = QString("总重%1\n超重%2\n超限车辆禁止驶入")
                        .arg(dwTotalWeight)
                        .arg(dwOverWeight);

                quint32 nAxisType = vehAxisInfo.GetConfirmedAxisGroup();
                bool bAllowPass = false;
                if (bYJVeh || VehInfo.GBVehType == UVT_Army) {
                    bAllowPass = true;
                }
#ifdef CHECKAXIS_11
                if ((!bAllowPass) && (11 == nAxisType)) {
                    if (dwTotalWeight < 18000) {  //此时提醒由人工确认是否继续发卡
                        bool OpRlt = false;
                        QString sDetail;
                        if (CheckOtherXVehInfo(XEvent_Truck1Over, VehInfo, nOpType, OpRlt, NULL,
                                               nAxisNum, sDetail, nAxisType)) {
                            if (Operate_WaitConfirm == nOpType) {
                                sError = QString("请按[赣通卡]键继续发卡");
                                bRepeat = true;
                                return false;
                            } else if (Operate_Pause == nOpType) {
                                sError = QString("货1超限等待确认");
                                bRepeat = true;
                                return false;
                            } else {
                                bAllowPass = true;
                            }
                        }
                    }
                }
#endif
                if (!bAllowPass) {
                    if (VehInfo.GBVehType == UVT_BigTruck) {
                        bool OpRlt = false;
                        if (CheckOtherXVehInfo(XEvent_ConfirmAxis, VehInfo, nOpType, OpRlt,
                                               pBigVehInfo, nAxisNum, sDetail, nAxisType)) {
                            if (Operate_Continue != nOpType) {
                                if (Operate_WaitConfirm == nOpType) {
                                    sError = QString("请按赣通卡键继续发卡");
                                } else if (Operate_Pause == nOpType) {
                                    sError = QString("等待进行车辆信息确认");
                                    sFDMsg = QString("%1\n大件运输车\n等待确认").arg(sPlate);
                                    RemoteMsgMgr::GetSingleInst()->SendLaneReportEventReq(1, CSpEventMgr::SpEvent_Truck_OBU,
                                        sFDMsg);
                                }
                                bRepeat = true;
                                return false;
                            } else {
                                if (OpRlt) {
                                    sCertNo = bigVehInfo.cerNo;
                                } else {
                                    VehInfo.GBVehType = UVT_Normal;
                                }
                            }
                        }
                    }
                    if (UVT_BigTruck != VehInfo.GBVehType) {
                        return false;
                    }
                }
            } else {
                QString sDetail;
                bool opRlt = false;
                if (VehInfo.bNeedConfirmAxis()) {
                    if (CheckOtherXVehInfo(XEvent_ConfirmAxis, VehInfo, nOpType, opRlt, pBigVehInfo,
                                           nAxisNum, sDetail, nAxisType)) {
                        if (Operate_Continue != nOpType) {
                            if (Operate_WaitConfirm == nOpType) {
                                sError = QString("请按赣通卡键继续发卡");

                            } else if (Operate_Pause == nOpType) {
                                QString sVehName = GetUnionVehTypeName(VehInfo.GBVehType);
                                sError = QString("%1请确认车辆轴数").arg(sVehName);
                                sFDMsg = QString("%1\n%2\n等待确认").arg(sPlate).arg(sVehName);
                                RemoteMsgMgr::GetSingleInst()->SendLaneReportEventReq(1, CSpEventMgr::SpEvent_Truck_OBU,
                                    sFDMsg);
                            }
                            bRepeat = true;
                            return false;
                        } else {
                            if (VehInfo.GBVehType == UVT_BigTruck) {
                                CVehInfo tmpVehInfo;
                                GetCurVehInfo(tmpVehInfo, true);
                                if (!qstrcmp(tmpVehInfo.szVehPlate, VehInfo.szVehPlate) &&
                                        tmpVehInfo.nVehPlateColor == VehInfo.nVehPlateColor) {
                                    VehInfo.GBVehType = tmpVehInfo.GBVehType;
                                    if (pBigVehInfo && (VehInfo.GBVehType == UVT_BigTruck))
                                        sCertNo = pBigVehInfo->cerNo;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    pCurTransInfo->SetVehInfo(&VehInfo, pVehAxisInfo);
    pCurTransInfo->m_sCertNo = sCertNo;

    GetMainDlg()->Emit_ETCShowVehInfo(nIndex, VehInfo);
    pCurTransInfo->SetTransState(CTransInfo::Ts_WaitIccInfo);
    sError = QString("等待卡片信息...");

    bool bwhiteListVeh = CParamFileMgr::IsWhiteListVeh(sPlate);
    pCurTransInfo->SetWhiteListVeh(bwhiteListVeh);

    if (bwhiteListVeh) {
        return true;
    }

    //判断拆卸
    if (pCurTransInfo->OBUBaseInfo.OBUState.bDisassembled) {
        nErrorCode = CSpEventMgr::SpEvent_DissambleOBU;
        sError = QString("电子标签拆卸");

        if (!Ptr_Info->bAllowDisOBU()) return false;
    }

    // 如果配置使用车型库，则在B3帧获取到车牌号和颜色后调用车型库查询
    if (Ptr_Info->bUseVehLib() && !sVehPlate.isEmpty()) {
        // 异步查询车型库
        CVehTypeLibMgr::GetVehTypeLibMgr()->AsyncGetVehResult(sVehPlate, pOBUVehInfo->nPlateColor);
        DebugLog(QString("B3帧获取车牌后查询车型库，车牌：%1，颜色：%2").arg(sVehPlate).arg(pOBUVehInfo->nPlateColor));
    }

    ShowLog(
        QString("B3 车型:%1,用户类型:%2").arg(pOBUVehInfo->bVehClass).arg(pOBUVehInfo->bUserType));
    ShowLog(QString("B3 车牌号:%1,车牌颜色:%2")
                .arg(QString::fromLocal8Bit(pOBUVehInfo->szVehPlate))
                .arg(pOBUVehInfo->nPlateColor));

    return true;
}

bool CLaneState_VehInput::ProcesssOBUBaseInfo_Light(int nIndex, quint32 OBUID, int &nErrorCode,
                                                    QString &sError, QString &sFDMsg)
{
    DebugLog(QString("天线%1处理B2[%2]帧....").arg(nIndex).arg(OBUID));
    
    // 【新增】新交易开始时，如果是不同车辆，清除之前的延时状态，确保状态干净
    // 适用于入口和出口车道
    if (DevIndex_Second == nIndex && m_nDelayedOBUID != 0 && m_nDelayedOBUID != OBUID) {
        DebugLog(QString("B2帧处理：检测到新车辆(OBUID:%1)，清除之前车辆(OBUID:%2)的延时状态")
                 .arg(OBUID).arg(m_nDelayedOBUID));
        m_nTimerCardMgrInterval2Flag = false;
        m_nDelayedOBUID = 0;
    }
    
    CRsuDev *pRsuDev = CDeviceFactory::GetRsuDev(nIndex);
    const COBUBaseInfo *pOBUBaseInfo = pRsuDev->GetOBUBaseInfo();
    COBUBaseInfo obuBaseInfo = *pOBUBaseInfo;
    pOBUBaseInfo = &obuBaseInfo;
    CStdLog::StdLogOBUInfo_BaseInfo(OBUID, pOBUBaseInfo);

    nErrorCode = 0;
    sError.clear();
    sFDMsg.clear();
    if (pOBUBaseInfo->bCPC) {
        nErrorCode = CSpEventMgr::SpEvent_Ignore;
        if (Ptr_Info->IsETCLane())
            sError = QString("CPC卡请转人工处理");
        else
            sError = QString("天线检测到CPC卡暂不处理");
        return false;
    }

    if (Ptr_Info->bHaveFrontDev()) {
        if (nIndex == DevIndex_Second) {
            //后天线检测到标签,判断这个标签是否前天线已经交易完，并且车队首车为未交易车辆
        }
    }

    CTransInfo transInfo;
    bool bRlt = Ptr_ETCCtrl->CheckVehhasTransByOBUId(OBUID, 3, transInfo);
    if (bRlt) {
        if (transInfo.bTransOk()) {
            if (transInfo.m_nRsuIndex == nIndex)  //同一天线检测到
                return false;
        }
    }

    if (!Ptr_ETCCtrl->bHasWaitingTransVeh(nIndex)) {
        sError.clear();  //
        DebugLog(QString("后天线没检测到等待交易车辆"));
        sError = QString("没有检测到等待交易车辆");
        nErrorCode = CSpEventMgr::SpEvent_Ignore;
        return false;
    }
    CTransInfo *pVehTranInfo = Ptr_ETCCtrl->GetCurTransInfo(nIndex, OBUID);

    if (!pVehTranInfo) {
        sError = QString("没有检测到等待交易车辆");
        ErrorLog(QString("天线1%没有检测到等待交易车辆").arg(nIndex));
        nErrorCode = CSpEventMgr::SpEvent_Ignore;
        return false;
    }

    pVehTranInfo->m_nRsuIndex = nIndex;
    pVehTranInfo->curFrameId = 0xB2;
    pVehTranInfo->curFrameTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    if (pOBUBaseInfo->ErrorCode != 0) {
        nErrorCode = CSpEventMgr::SpEvent_FrameError;
        pVehTranInfo->nErrorCode = nErrorCode;
        sError = QString("B2帧数据错误,Error:%1").arg(pOBUBaseInfo->ErrorCode);
        return false;
    }
    /*
    qint32 nPosY = 0;
    qint32 nPosX = 0;
    if (!pRsuDev->CheckVehPos(OBUID, nPosX, nPosY)) {
        sError = QString("OBU位置[x:%1,y:%2]超出天线范围").arg(nPosX).arg(nPosY);
        nErrorCode = CSpEventMgr::SpEvent_Ignore;
        pVehTranInfo->nErrorCode = nErrorCode;
        return false;
    }*/

    if (!CheckAllowETCTrans(nIndex, sError, sFDMsg)) {
        nErrorCode = CSpEventMgr::SpEvent_Ignore;
        return false;
    }

    pVehTranInfo->SetTransState(CTransInfo::Ts_IsProcessOBUBaseInfo);

    if (0 == pOBUBaseInfo->ErrorCode) {
        CTransInfo transInfo;
        bool bHasTrans = Ptr_ETCCtrl->CheckVehhasTransByOBUId(OBUID, 300, transInfo);
        if (bHasTrans && Ptr_Info->bCheckSameVeh()) {
            //如果该车已经交易成功，并且不在队列里了，则认为是重新交易车辆，继续处理。否则暂不交易
            if (transInfo.bTransOk()) {  //只处理交易成功车辆
                if (Ptr_ETCCtrl->CheckVehInVehQue(&transInfo)) {
                    if (Ptr_Info->bHaveFrontDev() && DevIndex_Second == nIndex) {
                        if (DevIndex_First == transInfo.m_nRsuIndex) {
                            QString sOBUId = QString::fromLocal8Bit(
                                        transInfo.OBUBaseInfo.szContractSerialNumber);
                            QString sPlate = GB2312toUnicode(transInfo.VehInfo.szVehPlate);
                            DebugLog(QString("后天线检测到前天线交易车辆,obu:%1,vehplate:%2")
                                     .arg(sOBUId)
                                     .arg(sPlate));
                            CheckAndRepassFrontETCVeh(transInfo);
                        }
                    }
                    nErrorCode = CSpEventMgr::SpEvent_Ignore;
                    return false;
                } else {
                    // B4帧继续处理
                    DebugLog(QString("重复交易车辆,等待后续处理...."));
                }
            } else {
                DebugLog("重复交易车辆,上次交易失败");
            }
        }
    }

    ShowLog(
                QString("B2 OBUSN:%1").arg(QString::fromLocal8Bit(pOBUBaseInfo->szContractSerialNumber)));
    ShowLog(QString("B2 发行方:%1,版本号:%2")
            .arg(QString::fromLocal8Bit(pOBUBaseInfo->ContractProvider, 4))
            .arg(pOBUBaseInfo->ContractVersion, 2, 16, QLatin1Char('0')));
    ShowLog(QString("B2 启用时间:%1,到期时间:%2")
            .arg(QString::fromLocal8Bit(pOBUBaseInfo->szContractSignedDate))
            .arg(QString::fromLocal8Bit(pOBUBaseInfo->szContractExpiredDate)));

    if (DevIndex_First == nIndex) {
        //对于非倒车车辆，需要判断前车干扰
        int nRet = Ptr_ETCCtrl->HaveDisturbCar(nIndex, OBUID, NULL, sError);
        if (nRet > 0) {
            nErrorCode = CSpEventMgr::SpEvent_DisturbCar;
            return false;
        }

        /*
        if (!Ptr_ETCCtrl->CheckReservedVeh(OBUID)) {
            int nRet = Ptr_ETCCtrl->HaveDisturbCar(OBUID, NULL, sError);
            if (nRet > 0) {
                nErrorCode = CSpEventMgr::SpEvent_DisturbCar;
                return false;
            }
        } else {
            //如果是倒车车辆，如果此时还有异常车在前面，则不能交易。
            quint32 dwTmpOBUId = 0;
            if (Ptr_ETCCtrl->bHaseAbnormalVehInQueue(sError, dwTmpOBUId)) {
                nErrorCode = CSpEventMgr::SpEvent_DisturbCar;
                return false;
            } else {
                DebugLog(QString("倒车车辆,继续交易"));
            }
        }*/

        if (Ptr_ETCCtrl->GetVehCount_FrontQue() >= 1) {
            nErrorCode = CSpEventMgr::SpEvent_Ignore;
            // sError = QString("前车尚未离开");
            return false;
        }
    }

    pVehTranInfo->ClearTransInfo();
    pVehTranInfo->SetOBUBaseInfo(OBUID, pOBUBaseInfo);

    if (pOBUBaseInfo->OBUState.bLowPower) {
        DebugLog(QString("标签电量过低"));
    }

    pVehTranInfo->SetTransState(CTransInfo::Ts_WaitOBUVehInfo);
    sError = QString("等待OBU车辆信息");
    GetMainDlg()->Emit_ETCShowOBUInfo(nIndex,
                                      QString::fromAscii(pOBUBaseInfo->szContractSerialNumber));
    return true;
}

void CLaneState_VehInput::FillCardTollInfo(CCardTollInfo &cardTollInfo, const QDateTime &transTime,
                                           CTransInfo *pTransInfo)
{
    cardTollInfo.clear();
    // cardTollInfo.wNetworkId = ORG_NETWORKID_GB;
    cardTollInfo.sNetworkHex = ORG_NETWORKID_HEX;

    cardTollInfo.dwStaId = pTransInfo->m_curGantryInfo.nStationId;
    cardTollInfo.bLaneId = pTransInfo->m_curGantryInfo.bLaneId;
    cardTollInfo.sEnStationHex = pTransInfo->m_curGantryInfo.sStationHex.right(4);
    cardTollInfo.sEnLaneHex = pTransInfo->m_curGantryInfo.sLaneHex.right(2);

    // 通行状态，0-出口收回；1-封闭式入口发出；3-封闭式ETC入口发出；5-便携式入口发出；7-手持入口发出；9-无人值守入口发出
    cardTollInfo.bPassStatus = Ptr_Info->GetLaneType();

    cardTollInfo.bMaintenance = 0;
    cardTollInfo.szPassTime = transTime;
    cardTollInfo.dwPassTime = transTime.toTime_t();

    cardTollInfo.bVehClass = (quint8)pTransInfo->VehInfo.VehClass;
    //写卡时，应该写入正常车种
    cardTollInfo.bVehTollType = (quint8)pTransInfo->VehInfo.GetGBVehTypeForTrans();

    // cpc特有
    cardTollInfo.dwOpId = Ptr_ETCCtrl->GetOperInfo().dwOper;
    cardTollInfo.bShiftId = Ptr_ETCCtrl->GetShiftMgr()->m_ShiftParam.wShift;
    cardTollInfo.bRsuMode = 0x01;
    cardTollInfo.dwLimitWeight = 0xffffffff;

    memcpy(cardTollInfo.szVehPlate, pTransInfo->VehInfo.szVehPlate, sizeof cardTollInfo.szVehPlate);
    cardTollInfo.bVehPlateColor = (quint8)pTransInfo->VehInfo.nVehPlateColor;

    cardTollInfo.sLastGantryHex = pTransInfo->m_curGantryInfo.sGantryHex;
    cardTollInfo.dwDoorFramePassTime = QDateTime2UnixTime_GB(transTime);

    cardTollInfo.dwToltalWeight =
            pTransInfo->m_dwToTalWeight;  // pTransInfo->m_vehAxisInfo.GetRawTotalFavWeight();
    cardTollInfo.dwLimitWeight =
            pTransInfo->m_dwWeightLimit;  // pTransInfo->m_vehAxisInfo.GetRawWeightStandard();
    cardTollInfo.VehicalAxles = pTransInfo->GetVehAxisNum();
    if (0 == cardTollInfo.VehicalAxles) {
        cardTollInfo.VehicalAxles = GetVehAxisNumByVC(pTransInfo->VehInfo.VehClass);
    }

    DebugLog(QString("FillCardTollInfo,TotalWeight:%1,LimitWeight:%2,axleNum:%3")
             .arg(cardTollInfo.dwToltalWeight)
             .arg(cardTollInfo.dwLimitWeight)
             .arg(cardTollInfo.VehicalAxles));

    /*
    //入口货车重量，如果obu内重量,<4500以obu为准 否则填 1500
    if(isTruck( pTransInfo->VehInfo.VehClass) || isYJTruck(pTransInfo->VehInfo.VehClass)){
        if(pTransInfo->IccInfo.nRawCardType==TYPE_PRO){
            cardTollInfo.dwToltalWeight =pTransInfo->OBUVehInfo.dwTotalWeight; //0xffffffff;
            if(cardTollInfo.dwToltalWeight>TotalWeight_Limit){
                cardTollInfo.dwToltalWeight = TotalWeight_Default;
            }
            cardTollInfo.dwLimitWeight = pTransInfo->OBUVehInfo.dwTotalWeight;
        }else if(pTransInfo->IccInfo.nRawCardType==TYPE_CPC){
            //从计重中取
        }
    }else
    {
        //客车默认填写0xFF
        cardTollInfo.dwToltalWeight  = 0xffffff;//obuVehInfo.dwTotalWeight;
        cardTollInfo.VehicalAxles    = 0x02;//obuVehInfo.bVehAxles;
        cardTollInfo.dwLimitWeight = 0xffffffff;
    }*/

    cardTollInfo.bVehState = pTransInfo->m_bVehState;
    if (cardTollInfo.bVehState == 0xff) {
        cardTollInfo.bVehState = GetVehStateByVehType(pTransInfo->VehInfo.GBVehType);
    }
    cardTollInfo.dwTotalFee = 0;  //后续计算出金额是填写
    cardTollInfo.dwTotalFee_Before = 0;
    return;
}

bool CLaneState_VehInput::ProcessIccInfo_Entry(int nIndex, quint32 OBUID, int &nErrorCode,
                                               QString &sMsg, QString &sFDMsg, bool &bStop)
{
    DebugLog(QString("天线%1处理B4[%2]帧....").arg(nIndex).arg(OBUID));
    bStop = true;
    nErrorCode = 0;
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(nIndex, OBUID);
    if (!pCurTransInfo) {
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sMsg = QString("后天天线未检测到待交易车辆");
        return false;
    }

    if (!CheckAllowETCTrans(nIndex, sMsg, sFDMsg)) {
        nErrorCode = CSpEventMgr::SpEvent_Ignore;
        return false;
    }

    if (CTransInfo::Ts_WaitIccInfo != pCurTransInfo->transState) {
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sMsg = QString(
                    QString("%1 状态下接收到卡片信息，不予处理").arg(pCurTransInfo->GetTransStateStr()));
        return true;
    }
    pCurTransInfo->SetTransState(CTransInfo::Ts_IsReadingIcc);
    CRsuDev_GB_EF *pRsuCtrl = (CRsuDev_GB_EF *)CDeviceFactory::GetRsuDev(nIndex);
    const CRsuIccInfo *pIccInfo = pRsuCtrl->GetRsuIccInfo();
    CRsuIccInfo iccInfo = *pIccInfo;
    pIccInfo = &iccInfo;

    nErrorCode = 0;
    sMsg.clear();

    if (OBUID != pCurTransInfo->OBUVehInfo.dwOBUID) {
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sMsg = QString("B4帧OBUID 不符,原来:%1 当前:%2")
                .arg(pCurTransInfo->OBUVehInfo.dwOBUID)
                .arg(OBUID);
        return true;
    }

    CStdLog::StdLogIccInfo_ProCardBasicInfo(QString::fromAscii(pCurTransInfo->OBUBaseInfo.szTime),
                                            &pIccInfo->ProCardBasicInfo, pIccInfo->dwBalance);

    QString sProCardId = QString::fromLocal8Bit(pIccInfo->ProCardBasicInfo.szCardNo);
    QString sEndTime = QString::fromLocal8Bit(pIccInfo->ProCardBasicInfo.szExpireTime);
    QString sNetWorId = QString::fromLocal8Bit(pIccInfo->ProCardBasicInfo.szNetworkId);
    DebugLog(QString("Profile15,network:%1,cardId:%2,endtime:%3")
             .arg(sNetWorId)
             .arg(sProCardId)
             .arg(sEndTime));

    CTransInfo hasTransInfo;
    bool bHasTrans = Ptr_ETCCtrl->CheckVehhasTransByOBUId(OBUID, 300, hasTransInfo);
    if (bHasTrans) {
        if (hasTransInfo.bTransOk() || hasTransInfo.m_bWhiteVeh) {
            if (Ptr_ETCCtrl->CheckVehInVehQue(&hasTransInfo)) {
                nErrorCode = CSpEventMgr::SpEvent_Other;
                sMsg = sMsg;
                return true;
            } else {
                //交易成功车辆没在队列里，要根据卡内信息判断是否已经发卡，如果已经发卡则继续放行。
                if (Ptr_Info->bCheckSameVeh() && CheckCardHasTrans(pIccInfo->CardTollInfo, true)) {
                    //如果已经写卡，则生成一条已经交易完成且保存的流水。
                    hasTransInfo.SetSaveResult(true);
                    Ptr_ETCCtrl->ReCompleteTrans_HaveTrans(nIndex, &hasTransInfo);
                    QString sError = QString("重新放行");
                    OnTransFinished(nIndex, true, 0, sError, true, &hasTransInfo);
                    return true;

                } else {
                    DebugLog("重复交易车辆,卡内信息改变，重新处理");
                }
            }
        }
    }

    QString sLastGantryHex;
    COpenGantryInfo openGantryInfo;

    QString sVehPlate = GB2312toUnicode(pCurTransInfo->VehInfo.szVehPlate);

    bool bHasOpenGantry = GetOpenGantyInfo_New(pCurTransInfo->VehInfo.VehClass, sVehPlate,
                                               pCurTransInfo->VehInfo.nVehPlateColor,
                                               &pIccInfo->CardTollInfo, openGantryInfo, true);
    if (bHasOpenGantry) {
        sLastGantryHex = openGantryInfo.sGantryHex;
    }

    //取当前门架信息
    CVirGantryInfo curGantryInfo;
    bool bQryResult = CTollGantryMgr::GetTollGantryMgr()->GetCurGantryInfo(
                sLastGantryHex, curGantryInfo, bHasOpenGantry);
    if (!bQryResult) {
        DebugLog(QString("门架%1不在本站代发卡门架列表内").arg(sLastGantryHex));
        sLastGantryHex.clear();
        bQryResult = CTollGantryMgr::GetTollGantryMgr()->GetCurGantryInfo(
                    sLastGantryHex, curGantryInfo, bHasOpenGantry);
        if (!bQryResult) {
            sMsg = QString("取当前门架信息失败");
            return false;
        }
    } else {
        if (bHasOpenGantry) {
            time_t dwCurTime = QDateTime::currentDateTime().toTime_t();
            if (dwCurTime > openGantryInfo.uEntryTime) {
                if (dwCurTime - openGantryInfo.uEntryTime > curGantryInfo.nMaxTime) {
                    DebugLog(QString("开放式门架时间超过时间阈值:%1,取当前实体门架信息")
                             .arg(curGantryInfo.nMaxTime));
                    sLastGantryHex.clear();
                    if (!CTollGantryMgr::GetTollGantryMgr()->GetCurGantryInfo(
                                sLastGantryHex, curGantryInfo, bHasOpenGantry)) {
                        sMsg = QString("取当前门架失败");
                        return false;
                    }
                }
            }
        }
    }
    pCurTransInfo->SetVirGantryInfo(curGantryInfo, bHasOpenGantry, openGantryInfo.sVlpId);

    // 1、初始化入口过站信息
    CCardTollInfo cardTollInfo;
    QDateTime TransTime = QDateTime::currentDateTime();
    if (bHasOpenGantry) {
        QDateTime openGantryTime;
        if (openGantryInfo.uEntryTime > 0)
            if (UnixTime2QDateTime_GB(openGantryInfo.uEntryTime, openGantryTime)) {
                TransTime = openGantryTime;
            }
    }
    CPro0019Raw_NewGB raw0019;
    FillCardTollInfo(cardTollInfo, TransTime, pCurTransInfo);
    CCardFileConverter::CardTollInfo2Pro0019Raw_NewGB(&raw0019, &cardTollInfo);
    DebugLog(QString("sLastGantryHex:%1, stationName:%2 nStationId:%3,当前站信息:%4,交易时间:%5 ")
             .arg(sLastGantryHex)
             .arg(pCurTransInfo->m_curGantryInfo.sStationName)
             .arg(pCurTransInfo->m_curGantryInfo.nStationId)
             .arg(Ptr_Info->GetStationID())
             .arg(TransTime.toString("yyyy-MM-dd hh:mm:ss")));

    CVehEntryInfo entryInfo;
    GetEntryInforEntry(TransTime, cardTollInfo, pCurTransInfo, entryInfo);
    pCurTransInfo->SetVehEntryInfo(entryInfo, TransTime);
    pCurTransInfo->SetIccInfo(pIccInfo);

    if (0 != pIccInfo->EF04RdStatus) {
        nErrorCode = CSpEventMgr::SPEVENT_EF04_ReadFaild;
        sMsg = QString("读标签EF04文件失败");
        DebugLog(QString("读标签EF04文件失败"));
        return false;
    }
    // 2、判断是否插卡
    pCurTransInfo->curFrameTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    pCurTransInfo->curFrameId = 0xB4;
    if (pIccInfo->ErrorCode > 0) {
        //没插卡，写EF04
        if (RsuError_B4_NoCard == pIccInfo->ErrorCode) {
            nErrorCode = CSpEventMgr::SpEvent_NotInsertCard;
            sMsg = QString("未插卡");
            sFDMsg = QString("未插卡\n%1").arg(sVehPlate);

            // light 避免多次写
            if (bHasTrans && hasTransInfo.etcTransType == TransType_EnNoCard) return false;
            bStop = false;
            pCurTransInfo->SetGBSpEvent(GBSP_NOPassCard, true);
            pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_OBUNoCard);
            this->WriteCardAndEfo04File(nIndex, OBUID, TransType_EnNoCard, 0, TransTime, &raw0019,
                                        NULL, NULL);
            return false;
        } else if (0x73 == pIccInfo->ErrorCode) {
            nErrorCode = CSpEventMgr::SPEVENT_EF04_ReadFaild;
            sMsg = QString("读标签EF04文件失败");
            return false;
        }
        nErrorCode = CSpEventMgr::SpEvent_FrameError;
        sMsg = QString("B4帧数据错误,错误码[%1]").arg(pIccInfo->ErrorCode);
        pCurTransInfo->nErrorCode = pIccInfo->ErrorCode;

        return false;
    }

    // 3、保存卡片信息并显示

    GetMainDlg()->Emit_ETCShowCardInfo(nIndex, pIccInfo->ProCardBasicInfo.bType,
                                       QString::fromAscii(pIccInfo->ProCardBasicInfo.szCardNo),
                                       pIccInfo->dwBalance);

    // 【新增】在后天线处理B4帧时设置卡机定时器延时标志（入口车道专用）
    // 多重安全检查，确保只延时一次：
    // 1. 只对后天线(DevIndex_Second)生效
    // 2. 确保当前为入口车道（避免在出口车道误触发）
    // 3. 只对卡机类型31生效
    // 4. 延时标志未设置过
    // 5. 该OBUID未执行过延时（防止同一车辆多次B4帧重复延时）
    if (DevIndex_Second == nIndex && Ptr_Info->IsEntryLane() && 
        Ptr_Info->GetCardMgrType() == 31 && !m_nTimerCardMgrInterval2Flag && 
        m_nDelayedOBUID != OBUID) {
        m_nTimerCardMgrInterval2Flag = true;
        m_nDelayedOBUID = OBUID; // 记录已延时的OBUID
        DebugLog(QString("B4帧处理：设置后天线卡机定时器延时标志，天线%1，OBUID:%2").arg(nIndex).arg(OBUID));
    } else if (DevIndex_Second == nIndex && m_nDelayedOBUID == OBUID) {
        DebugLog(QString("B4帧处理：该车辆(OBUID:%1)已执行过延时，跳过重复延时").arg(OBUID));
    }

    QString sLog = QString("天线%1收到B4 %2,网络号:%3,卡号:%4,余额:%5")
            .arg(nIndex)
            .arg(CCardFileConverter::GetCardTypeName(pIccInfo->ProCardBasicInfo.bType))
            .arg(QString::fromAscii(pIccInfo->ProCardBasicInfo.szNetworkId))
            .arg(QString::fromLocal8Bit(pIccInfo->ProCardBasicInfo.szCardNo))
            .arg(pIccInfo->dwBalance);
    ShowLog(sLog);

    //只用来判断是否为调头车，避免误写
    if (!CheckCardTollInfo(pIccInfo->CardTollInfo, true, nErrorCode, sMsg)) {
        return false;
    }

    // 4、卡片基本校验
    bool bYJVeh = CCardFileConverter::IsYJCard(pIccInfo->ProCardBasicInfo);
    if (CCardFileConverter::IsArmyCard(pIccInfo->ProCardBasicInfo.szNetworkId)) {
        pCurTransInfo->VehInfo.GBVehType = UVT_Army;
    } else {
        if (bYJVeh) {
            pCurTransInfo->VehInfo.GBVehType = UVT_YJVeh;
        }
    }
    bool bFreeVeh = IsFreeVehType_GB(pCurTransInfo->VehInfo.GBVehType);

    //只有储值卡才判断余额 1-判断余额
    if (pIccInfo->ProCardBasicInfo.bType == CARD_TYPE_STORE_CARD) {
        if (0 == pIccInfo->dwBalance && !bFreeVeh) {
            nErrorCode = CSpEventMgr::SpEvent_NoBalance;
            sMsg = QString("储值卡余额为0");
            sFDMsg = QString("余额不足\n%1").arg(sVehPlate);
            if ((bHasTrans) && (hasTransInfo.etcTransType == TransType_EnStoreNoBlance)) {
                return false;
            }
            pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_CardBalanceLow);
            DebugLog("储值卡余额为0");
            bStop = false;
            this->WriteCardAndEfo04File(nIndex, OBUID, TransType_EnStoreNoBlance, 0, TransTime,
                                        &raw0019, &pCurTransInfo->IccInfo.Raw15, NULL);
            return false;
        }
    }

    if (!CheckOBU_Valid(nIndex, nErrorCode, sMsg)) {
        return false;
    }

    if (pCurTransInfo->VehInfo.GBVehType == UVT_Normal)
        pCurTransInfo->VehInfo.GBVehType =
                GetGBVehTypeByUserType(pCurTransInfo->IccInfo.ProCardBasicInfo.bUserType);

    bool bVerifyRet =
            VerifyProCardBasicInfo_Light(nIndex, pCurTransInfo, pIccInfo->ProCardBasicInfo, false,
                                         pCurTransInfo->VehInfo, nErrorCode, sMsg);
    if (!bVerifyRet) {
        DebugLog(QString("校验卡片信息失败:%1").arg(sMsg));
        return false;
    }

    int bInList =
            CheckVehList(nIndex, sVehPlate, pCurTransInfo->VehInfo.nVehPlateColor, nErrorCode, sMsg);
    if (bInList > 0) {
        return false;
    }

    // 5、节假日免费,如果折扣率1000，为全免车
    //计费车型
    CVehClass feeVehClass = pCurTransInfo->VehInfo.VehClass;
    if (pCurTransInfo->VehInfo.VehClass == VC_Car2) {
        if (pCurTransInfo->OBUVehInfo.dwManNum == 8 || 9 == pCurTransInfo->OBUVehInfo.dwManNum) {
            DebugLog(QString("%1座客2按客1处理").arg(pCurTransInfo->OBUVehInfo.dwManNum));
            feeVehClass = VC_Car1;
        }
    }

    CHolidayFreeTable *pHolidayTable =
            (CHolidayFreeTable *)CParamFileMgr::GetParamFile(cfHolidayFree);
    if (pHolidayTable) {
        bool bHolidayFree = pHolidayTable->IsHolidayFree(feeVehClass, sMsg);
        if (bHolidayFree) {
            if (feeVehClass == VC_Car1) {
                if (8 == pCurTransInfo->OBUVehInfo.dwManNum ||
                        9 == pCurTransInfo->OBUVehInfo.dwManNum) {
                    DebugLog(QString("客1车辆座位数%1,不予免费")
                             .arg(pCurTransInfo->OBUVehInfo.dwManNum));
                    bHolidayFree = false;
                    pCurTransInfo->bManNum8 = true;
                }
            }
            if (bHolidayFree) {
                pCurTransInfo->bHolidayFree = 1;
                pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_HolidayFree);
            }
        }
    }

    //保存车辆信息
    // pCurTransInfo->SetVehInfo(&pCurTransInfo->VehInfo);
    GetMainDlg()->Emit_ETCShowVehInfo(nIndex, pCurTransInfo->VehInfo);
    GetMainDlg()->Emit_ETCShowCardInfo(
                nIndex, pCurTransInfo->IccInfo.ProCardBasicInfo.bType,
                QString::fromAscii(pCurTransInfo->IccInfo.ProCardBasicInfo.szCardNo),
                pCurTransInfo->IccInfo.dwBalance);

    //门架计费
    QString sExStationHex = "";
    if (CTollGantryMgr::GetTollGantryMgr()->CheckNeedGrantryCalcFee()) {
        if (!CFareCalcUnit::CalcGantryFee(*pCurTransInfo, feeVehClass, sExStationHex,
                                          pCurTransInfo->bHolidayFree, TransTime, sMsg)) {
            nErrorCode = CSpEventMgr::SpEvent_Other;
            sMsg = QString("门架计费失败,Error:%1").arg(sMsg);
            pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_FeeQryFeeFailed);
            return false;
        } else {
            ErrorLog(QString("门架计费成功,fee:%1,payfee:%2,discountFee:%3,feeProSumLocal:%4,"
                             "计费车型:%5")
                     .arg(pCurTransInfo->gantryFeeInfo.cardFee)
                     .arg(pCurTransInfo->gantryFeeInfo.payFee)
                     .arg(pCurTransInfo->gantryFeeInfo.discountFee)
                     .arg(pCurTransInfo->gantryFeeInfo.feeProvSumLocal)
                     .arg(feeVehClass));
        }
    }

    QString sOBUId = QString::fromAscii(pCurTransInfo->OBUBaseInfo.szContractSerialNumber);
    QString sCardId = QString::fromAscii(pCurTransInfo->IccInfo.ProCardBasicInfo.szCardNo);
    quint16 wNetworkId = pCurTransInfo->IccInfo.ProCardBasicInfo.wNetWorkId;
    qint32 nBListType = 0;

    QString sDesc;
    int nQryBRlt =
            CParamFileMgr::QryCardBList(3, sOBUId, sCardId, wNetworkId, nBListType, sDesc, sMsg);
    bool bInCardBList = (0 != nQryBRlt);

    if (Ptr_Info->bCheckCardBList() && bInCardBList) {
        DebugLog(QString("黑名单返回结果:%1").arg(sMsg));
        if (1 == nQryBRlt)
            nErrorCode = CSpEventMgr::SpEvent_BlackOBU;
        else if (2 == nQryBRlt)
            nErrorCode = CSpEventMgr::GetEventIdByCardBListType(nBListType);
        return false;
    }
    //显示门架金额-但不扣费
    qint32 dwLastMoney = 0;
    qint32 dwConsumeMoney = 0;
    pCurTransInfo->GetLastMoney_Entry(dwLastMoney, dwConsumeMoney);
    GetMainDlg()->Emit_ETCShowMoney(nIndex, dwLastMoney);

    //重新设置19文件内的累计金额
    cardTollInfo.dwTotalFee = dwLastMoney;
    cardTollInfo.bVehTollType = pCurTransInfo->VehInfo.GBVehType;
    raw0019.File0012Raw_GB.TotalFee = qToBigEndian(dwLastMoney);
    pCurTransInfo->SetCardTollInfo(&cardTollInfo, &TransTime);
    CStdLog::StdLogTollCardInfo_CardTollInfo(QString::fromAscii(pCurTransInfo->OBUBaseInfo.szTime),
                                             &cardTollInfo);

    EF04Raw ef04Raw;
    memset(&ef04Raw, 0, sizeof(EF04Raw));
    pCurTransInfo->FillEF04Info_Entry(&raw0019);
    CCardFileConverter_JX::EF04Info2EFo4Raw(pCurTransInfo->IccInfo.ef04Info, ef04Raw);
    CStdLog::StdLog_EF04(QString::fromAscii(pCurTransInfo->OBUBaseInfo.szTime),
                         &pCurTransInfo->IccInfo.ef04Info);

    //入口，扣0元，不管模块返回的扣费金额。
    dwConsumeMoney = 0;
    this->WriteCardAndEfo04File(nIndex, OBUID, TransType_EnNormal, dwConsumeMoney, TransTime,
                                &raw0019, &pCurTransInfo->IccInfo.Raw15, &ef04Raw);
    DebugLog(QString("ETC 通行费金额[%1],扣费金额[%2],卡内余额[%3]")
             .arg(dwLastMoney)
             .arg(dwConsumeMoney)
             .arg(pIccInfo->dwBalance));

    CStdLog::StdLogConsumeInfo(
                QString::fromAscii(pCurTransInfo->OBUBaseInfo.szTime), dwLastMoney, dwConsumeMoney,
                pCurTransInfo->gantryFeeInfo.feeMileage, pCurTransInfo->actualFeeClass,
                QString::fromAscii(pCurTransInfo->IccInfo.ProCardBasicInfo.szCardNo));
    TransInfoShared Value;
    Value.nStatus = 0;
    Value.Transtime = TransTime;
    Value.nLaneid = Ptr_Info->GetLaneId();
    Value.nMediatype = 1;
    Value.nVehtype = pCurTransInfo->VehInfo.VehClass;
    CTransInfoShared::GetTransInfoShared()->SendData(sVehPlate, Value);
    sMsg = QString("等待交易结果...");
    return true;
}

/*
 *bStop 是否向天线发送停止交易指令。 true-表示需要发送 false -表示已经发送后续处理指令
 * 缺省取值为true
 */
bool CLaneState_VehInput::ProcessIccInfo_Exit(int nIndex, quint32 OBUID, int &nErrorCode,
                                              QString &sMsg, bool &bStop)
{
    DebugLog(QString("天线%1处理B4[%2]帧....").arg(nIndex).arg(OBUID));
    bStop = true;
    nErrorCode = 0;

    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(nIndex);
    if (!pCurTransInfo) {
        DebugLog("天线没有检测到车辆");
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sMsg = QString("无等待交易车辆");
        return false;
    }
    QString sFDMsg;

    if (!CheckAllowETCTrans(nIndex, sMsg, sFDMsg)) {
        nErrorCode = CSpEventMgr::SpEvent_Ignore;
        return false;
    }

    if (CTransInfo::Ts_WaitIccInfo != pCurTransInfo->transState) {
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sMsg = QString(
                    QString("%1 状态下接收到卡片信息，不予处理").arg(pCurTransInfo->GetTransStateStr()));
        return true;
    }

    CRsuDev_GB_EF *pRsuCtrl = (CRsuDev_GB_EF *)CDeviceFactory::GetRsuDev(nIndex);

    CRsuIccInfo iccInfo;
    iccInfo = *pRsuCtrl->GetRsuIccInfo();
    CRsuIccInfo *pIccInfo = &iccInfo;

    nErrorCode = 0;
    if (OBUID != pCurTransInfo->OBUVehInfo.dwOBUID) {
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sMsg = QString("B4帧OBUID 不符,原来:%1 当前:%2")
                .arg(pCurTransInfo->OBUVehInfo.dwOBUID)
                .arg(OBUID);
        return true;
    }

    pCurTransInfo->SetTransState(CTransInfo::Ts_IsReadingIcc);
    CTransInfo hasTransInfo;
    //需要通过重复交易信息判断是否多次清obu
    bool bHasTrans = Ptr_ETCCtrl->CheckVehhasTransByOBUId(OBUID, 300, hasTransInfo);
    //判断车辆是否已经交易成功,重复交易车辆,如果是不予处理

    if (bHasTrans) {
        if (hasTransInfo.bTransOk()) {
            if (Ptr_ETCCtrl->CheckVehInVehQue(&hasTransInfo)) {
                nErrorCode = CSpEventMgr::SpEvent_Other;
                sMsg = "车辆已交易";
                if (nIndex == DevIndex_Second) {
                    if (hasTransInfo.m_nRsuIndex == DevIndex_First) {
                        sMsg = QString("前天线已交易");
                    }
                }
                return true;
            } else {
                if (CheckCardHasTrans(pIccInfo->CardTollInfo, false)) {
                    //如果已经交易
                    hasTransInfo.SetSaveResult(true);
                    Ptr_ETCCtrl->ReCompleteTrans_HaveTrans(nIndex, &hasTransInfo);
                    DebugLog(QString("车辆已经交易重新放行"));
                    QString sError = QString("重新放行");
                    OnTransFinished(nIndex, true, 0, sError, true, &hasTransInfo);
                    return true;
                }
            }
        }
    }

    pCurTransInfo->SetIccInfo(pIccInfo);

    if (0 != pIccInfo->EF04RdStatus) {
        nErrorCode = CSpEventMgr::SPEVENT_EF04_ReadFaild;
        sMsg = QString("读标签入口信息失败");
        return false;
    }

    //取当前门架信息

    QString scurGantryHex;
    bool bOpenGantry = false;
    if (!CTollGantryMgr::GetTollGantryMgr()->GetCurGantryInfo(
                scurGantryHex, pCurTransInfo->m_curGantryInfo, bOpenGantry)) {
        ErrorLog(QString("门架字典内门架为空"));
        sMsg = "门架信息异常";
        nErrorCode = CSpEventMgr::SpEvent_Other;
        return false;
    }

    pCurTransInfo->curFrameId = 0xB4;
    pCurTransInfo->curFrameTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    if (pIccInfo->ErrorCode > 0) {
        if (2 == pIccInfo->ErrorCode) {
            nErrorCode = CSpEventMgr::SpEvent_NotInsertCard;
            sMsg = QString("请插卡");  // todo 应该记录特情，并保存报文
            return false;
        } else if (0x73 == pIccInfo->ErrorCode) {
            nErrorCode = CSpEventMgr::SPEVENT_EF04_ReadFaild;
            sMsg = QString("读标签入口信息失败");
            return false;
        }
        nErrorCode = CSpEventMgr::SpEvent_FrameError;
        pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_OBUNoCard);
        sMsg = QString("读卡失败,错误码[%1]").arg(pIccInfo->ErrorCode);
        pCurTransInfo->nErrorCode = pIccInfo->ErrorCode;

        return false;
    }

    CEmVehList *pEmVehList = (CEmVehList *)CParamFileMgr::GetParamFile(cfEmVeh);
    bool bEmVeh = false;
    CEmVehInfo emVehInfo;
    int nDiscountType = 0;
    if (pEmVehList) {
        QString sCardId = QString::fromAscii(pIccInfo->ProCardBasicInfo.szNetworkId) +
                QString::fromAscii(pIccInfo->ProCardBasicInfo.szCardNo);
        QString sPlate = GB2312toUnicode(pCurTransInfo->VehInfo.szVehPlate);
        bEmVeh = pEmVehList->IsEmVeh(emVehInfo, nDiscountType, sCardId, sPlate,
                                     pCurTransInfo->VehInfo.nVehPlateColor);
        pCurTransInfo->SetEmVehInfo(bEmVeh, emVehInfo);
        if (bEmVeh) {
            DebugEmvehInfo(emVehInfo);
        }
    }

    CStdLog::StdLogIccInfo_ProCardBasicInfo(QString::fromAscii(pCurTransInfo->OBUBaseInfo.szTime),
                                            &pIccInfo->ProCardBasicInfo, pIccInfo->dwBalance);
    CStdLog::StdLogTollCardInfo_CardTollInfo(QString::fromAscii(pCurTransInfo->OBUBaseInfo.szTime),
                                             &pIccInfo->CardTollInfo);
    CStdLog::StdLog_EF04(QString::fromAscii(pCurTransInfo->OBUBaseInfo.szTime),
                         &pIccInfo->ef04Info);

    GetMainDlg()->Emit_ETCShowCardInfo(nIndex, pIccInfo->ProCardBasicInfo.bType,
                                       QString::fromAscii(pIccInfo->ProCardBasicInfo.szCardNo),
                                       pIccInfo->dwBalance);

    // 【新增】在后天线处理B4帧时设置卡机定时器延时标志（出口车道专用）
    // 多重安全检查，确保只延时一次：
    // 1. 只对后天线(DevIndex_Second)生效
    // 2. 确保当前为出口车道（避免在入口车道误触发）
    // 3. 只对卡机类型31生效
    // 4. 延时标志未设置过
    // 5. 该OBUID未执行过延时（防止同一车辆多次B4帧重复延时）
    if (DevIndex_Second == nIndex && Ptr_Info->IsExitLane() && 
        Ptr_Info->GetCardMgrType() == 31 && !m_nTimerCardMgrInterval2Flag && 
        m_nDelayedOBUID != OBUID) {
        m_nTimerCardMgrInterval2Flag = true;
        m_nDelayedOBUID = OBUID; // 记录已延时的OBUID
        DebugLog(QString("B4帧处理（出口）：设置后天线卡机定时器延时标志，天线%1，OBUID:%2").arg(nIndex).arg(OBUID));
    }

    QString sLog = QString("天线%1,收到B4 %2,网络号:%3,卡号:%4,余额:%5")
            .arg(nIndex)
            .arg(CCardFileConverter::GetCardTypeName(pIccInfo->ProCardBasicInfo.bType))
            .arg(QString::fromAscii(pIccInfo->ProCardBasicInfo.szNetworkId))
            .arg(QString::fromLocal8Bit(pIccInfo->ProCardBasicInfo.szCardNo))
            .arg(pIccInfo->dwBalance);
    ShowLog(sLog);

    //判断卡片上次交易是否已经扣款不抬杆。
    CTransInfo *pLastTransInfo = Ptr_ETCCtrl->GetLastTransInfo(nIndex);
    QString sCardId = QString::fromAscii(pIccInfo->ProCardBasicInfo.szCardNo);
    quint32 dwLastConsumeMoney = 0;

    bool bHasConsumed = false;
    bHasConsumed = pLastTransInfo->CheckHasConsumeMoney(pIccInfo);
    dwLastConsumeMoney = pLastTransInfo->GetConsumeMoney();
    if (bHasConsumed) {
        //如果已经扣款，并且扣款金额大于0或者已经修改出入口记录了，重新取tac
        bool bValidEntry = CCardFileConverter::IsValidEnFlag(pIccInfo->CardTollInfo.bPassStatus);
        if (dwLastConsumeMoney > 0 || (!bValidEntry)) {
            sMsg = QString("卡片[%1]交易失败扣款成功,重新取TAC值").arg(sCardId);
            ErrorLog(sMsg);
            EF04Raw EF04Info;

            //不是一个天线交易的
            /*
            if (pLastTransInfo->m_nRsuIndex != nIndex) {
                if (0 == pLastTransInfo->m_nPsamSerial) {
                    //没有之前的Psam卡序列号，只能到原先天线重取tac
                    sMsg = QString("卡片[%1]交易失败").arg(sCardId);
                    nErrorCode = CSpEventMgr::SpEvent_Ignore;
                    return false;
                }
            }*/

            pRsuCtrl->RegetTac(OBUID, pLastTransInfo->TransTime,
                               (CPro0019Raw_NewGB *)&pIccInfo->Raw19, EF04Info);
            pLastTransInfo->SetReGetTacFlag(true, pLastTransInfo->TransTime);

            // fortac 暂不使用
            //            pLastTransInfo->TransTime = QDateTime::currentDateTime();
            pLastTransInfo->SetTransState(CTransInfo::Ts_WaitOpResult);
            bStop = false;
            return true;
        }
    }

    if (!CheckOBU_Valid(nIndex, nErrorCode, sMsg)) {
        return false;
    }

    if (!CheckOBUVehInfo(nIndex, pCurTransInfo, bEmVeh, nErrorCode, sMsg, sFDMsg)) {
        return false;
    }

    if (!CheckCardTollInfo(pIccInfo->CardTollInfo, Ptr_Info->IsEntryLane(), nErrorCode, sMsg)) {
        return true;
    }

    bool bEf04Ok = pCurTransInfo->IccInfo.ef04Info.IsValid();
    QDateTime TransTime = QDateTime::currentDateTime();
    CVehEntryInfo vehEnInfo;
    if (!GetEntryInfoFromCardTollInfo(TransTime, pIccInfo->CardTollInfo, vehEnInfo, nErrorCode,
                                      sMsg)) {
        // todo卡内入口信息无效，记录特请
        // pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_CardEntryError);
        return false;
    }

    if (vehEnInfo.EnTime.secsTo(QDateTime::currentDateTime()) >=
            Ptr_Info->GetMaxDriveDays() * 3600 * 24) {
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sMsg = QString(QString("行驶时间超过%1天").arg(Ptr_Info->GetMaxDriveDays()));
        return false;
    }
    QString sVehPlate = GB2312toUnicode(pCurTransInfo->VehInfo.szVehPlate);

    CUnionVehType vehType = UVT_Normal;
    CBigVehInfo bigVehInfo;
    if (isTruck(pCurTransInfo->VehInfo.VehClass)) {
        if (vehEnInfo.bVehState == VehState_BigTruck) {
            //大件车
            vehType = UVT_BigTruck;
        }

        bool bBigVeh = HandleBigVehSelection(sVehPlate, pCurTransInfo->VehInfo.nVehPlateColor, bigVehInfo);
        if (bBigVeh) {
            vehType = UVT_BigTruck;
        }

        CVehInfo tmpVehInfo = pCurTransInfo->VehInfo;
        tmpVehInfo.GBVehType = vehType;
        if (tmpVehInfo.GBVehType == UVT_BigTruck) {
            int bOpType = 0;
            bool bOpRlt = false;
            int ntmpAxisNum = pCurTransInfo->GetVehAxisNum();
            QString sDetail;
            bool bCheckRlt = CheckOtherXVehInfo(XEvent_ConfirmAxis, tmpVehInfo, bOpType, bOpRlt,
                                                &bigVehInfo, ntmpAxisNum, sDetail);
            if (bCheckRlt) {
                if (Operate_Continue != bOpType) {
                    if (Operate_WaitConfirm == bOpType) {
                        sMsg = QString("请确认是否为大件车");
                    } else if (Operate_Pause == bOpType) {
                        sMsg = QString("请确认是否为大件车");
                        sFDMsg = QString("大件运输车\n%1\n等待确认").arg(sVehPlate);
                    }
                    bStop = false;
                    return false;
                } else {
                    if (bOpRlt) {
                        pCurTransInfo->SetGBVehType(vehType);
                    }
                }
            }
        }
    }

    bool bYJVeh = CCardFileConverter::IsYJCard(pIccInfo->ProCardBasicInfo);

    bool bVerifyRet =
            VerifyProCardBasicInfo_Light(nIndex, pCurTransInfo, pIccInfo->ProCardBasicInfo, bEmVeh,
                                         pCurTransInfo->VehInfo, nErrorCode, sMsg);
    if (!bVerifyRet) {
        DebugLog(QString("校验卡片信息失败").arg(bVerifyRet));
        return false;
    }

    if (CCardFileConverter::IsArmyCard(pIccInfo->ProCardBasicInfo.szNetworkId)) {
        pCurTransInfo->VehInfo.GBVehType = UVT_Army;

    } else {
        if (bYJVeh) {
            pCurTransInfo->VehInfo.GBVehType = UVT_YJVeh;
        }
    }

    if (pCurTransInfo->VehInfo.GBVehType == UVT_Normal)
        pCurTransInfo->VehInfo.GBVehType =
                GetGBVehTypeByUserType(pCurTransInfo->IccInfo.ProCardBasicInfo.bUserType);

    bool bFreeVeh = IsFreeVehType_GB(pCurTransInfo->VehInfo.GBVehType);
    bool bHolidayFree = false;

    CAllRoadMinFeeInfo MinFeeInfo;
    MinFeeInfo.Clear();
    CVehClass feeVehClass = pCurTransInfo->VehInfo.VehClass;
    if (VC_Car2 == pCurTransInfo->VehInfo.VehClass) {
        if (8 == pCurTransInfo->OBUVehInfo.dwManNum || 9 == pCurTransInfo->OBUVehInfo.dwManNum)
            feeVehClass = VC_Car1;
    }
    if (Ptr_Info->bCheckMinFee()) {
        CVehClass minVehClass = feeVehClass;
        if (pCurTransInfo->VehInfo.GBVehType == UVT_TRUCK ||
                pCurTransInfo->VehInfo.GBVehType == UVT_J1 ||
                pCurTransInfo->VehInfo.GBVehType == UVT_J2) {
            minVehClass = GetTruckVehClassByAxisNum(vehEnInfo.VehicalAxles);
            if (minVehClass == VC_None) minVehClass = feeVehClass;
            DebugLog(QString("最小计费，根据入口轴数:%1,获取出口计费车型:%2")
                     .arg(vehEnInfo.VehicalAxles)
                     .arg(minVehClass));
        }
        bool bRlt = QryMinFeeInfo(minVehClass, vehEnInfo, true, MinFeeInfo, nErrorCode, sMsg);
        if (!bRlt) {
            nErrorCode = CSpEventMgr::SpEvent_Other;
            sMsg = QString("最短路径通行费查询失败");
            return false;
        }
        CAllRoadMinFeeInfo MinFeeInfo1;
        CAllRoadMinFeeInfo *pMinFeeInfo1 = NULL;
        if (DevIndex_Second == nIndex && pCurTransInfo->VehInfo.VehClass > VC_Truck1) {
            bRlt = QryMinFeeInfo(VC_Truck1, vehEnInfo, true, MinFeeInfo1, nErrorCode, sMsg);
            if (!bRlt) {
                nErrorCode = CSpEventMgr::SpEvent_Other;
                sMsg = QString("货1最短路径通行费查询失败");
                return false;
            }
            pMinFeeInfo1 = &MinFeeInfo1;
        }
        pCurTransInfo->SetAllRoadMinFeeInfo(MinFeeInfo, pMinFeeInfo1);
        DebugLog("最短路径费率查询成功");
    }

    //节假日免费,如果折扣率1000，为全免车
    CHolidayFreeTable *pHolidayTable =
            (CHolidayFreeTable *)CParamFileMgr::GetParamFile(cfHolidayFree);
    if (pHolidayTable) {
        //改为以出口时间为准 2020-09-22
        bHolidayFree = pHolidayTable->IsHolidayFree(feeVehClass, sMsg);
        DebugLog(QString("%1节假日免费").arg(bHolidayFree ? "是" : "不是"));

        if (bHolidayFree) {
            if (feeVehClass == VC_Car1) {
                if (8 == pCurTransInfo->OBUVehInfo.dwManNum ||
                        9 == pCurTransInfo->OBUVehInfo.dwManNum) {
                    DebugLog(QString("客1车辆座位数%1,不予免费")
                             .arg(pCurTransInfo->OBUVehInfo.dwManNum));
                    bHolidayFree = false;
                    pCurTransInfo->bManNum8 = true;
                }
            }
            if (bHolidayFree) {
                pCurTransInfo->bHolidayFree = 1;
                pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_HolidayFree);
            }
        }
    }
    bool bFree = bFreeVeh || bHolidayFree || bEmVeh;

    if (!bFree) {
        //拖挂、集装 入出轴数不符，需确认
        bool bCheckRlt = CheckVehEntryInfo(nIndex, pCurTransInfo, vehEnInfo, sMsg, sFDMsg);
        //入出轴数不符，改为人工处理
        if (!bCheckRlt) {
            /*
            int nOpType = 0;
            bool bOpRlt = false;
            bool bCheckRlt = CheckOtherXVehInfo(XEvent_ConfirmAxis, pCurTransInfo->VehInfo, nOpType,
                                                bOpRlt, NULL, pCurTransInfo->GetVehAxisNum(), sMsg);
            if (bCheckRlt) {
                if (Operate_Continue != nOpType) {
                    if (Operate_WaitConfirm == nOpType) {
                        sMsg = QString("请按<赣通卡>键继续天线交易");
                        bStop = false;
                        return false;
                    } else if (Operate_Pause == nOpType) {
                        sFDMsg = QString("%1\n等待确认车辆信息").arg(sVehPlate);
                        bStop = false;
                        return false;
                    }
                } else {
                    m_OtherXVehInfo.Clear();
                }
            }*/
            bStop = false;
            sFDMsg = QString("入出轴数不符\n转人工处理");
            return false;
        }
    }

    //保存车辆信息
    GetMainDlg()->Emit_ETCShowVehInfo(nIndex, pCurTransInfo->VehInfo);
    GetMainDlg()->Emit_ETCShowCardInfo(
                nIndex, pCurTransInfo->IccInfo.ProCardBasicInfo.bType,
                QString::fromAscii(pCurTransInfo->IccInfo.ProCardBasicInfo.szCardNo),
                pCurTransInfo->IccInfo.dwBalance);

    if (bEf04Ok) {
        // obu卡片入口信息是否相同
        if ((!bEmVeh) && (!pCurTransInfo->IccInfo.OBUandIcCardIsSame())) {
            bEf04Ok = false;
            nErrorCode = CSpEventMgr::SPEVENT_EF04_OBUCARDDIF;
            sMsg = QString("EF04入口信息与卡内不符");
            pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_Ef04EnInfoDiff);
            pCurTransInfo->IccInfo.ef04Info.SetValid(bEf04Ok);
            return false;
        } else {
        }

    } else {
        //拦截
        if (!bEmVeh) {
            nErrorCode = CSpEventMgr::SPEVENT_EF04_VALID;
            sMsg = QString("EF04文件信息无效");
            return false;
        }
    }

    QString sLastGantryHex;
    quint32 dwLastGantryTime = 0;
    sLastGantryHex = pIccInfo->CardTollInfo.sLastGantryHex;
    dwLastGantryTime = pIccInfo->CardTollInfo.dwDoorFramePassTime;

    //标签信息有效，以标签内的门架信息为准
    if (bEf04Ok) {
        sLastGantryHex = pCurTransInfo->IccInfo.ef04Info.cardTollInfo.sLastGantryHex;
        dwLastGantryTime = pCurTransInfo->IccInfo.ef04Info.cardTollInfo.dwDoorFramePassTime;
        vehEnInfo.sGantryNumHex = sLastGantryHex;
        vehEnInfo.gantryPassTime = dwLastGantryTime;
    }

    pCurTransInfo->SetVehEntryInfo(vehEnInfo, TransTime);
    GetMainDlg()->Emit_ETCShowEntryInfo(nIndex, vehEnInfo.EnTime, vehEnInfo.sEnStaionName,
                                        QString::fromLocal8Bit(vehEnInfo.szEnVLP),
                                        vehEnInfo.sEnStationHex);

    QString sExStationHex = "";
    if (Ptr_Info->IsExitLane()) {
        sExStationHex = Ptr_Info->GetHexStationID();
    }

    if ((pCurTransInfo->IccInfo.ef04Info.totalLastFee_After <
         pCurTransInfo->IccInfo.ef04Info.localLastFee_After) ||
            (pCurTransInfo->IccInfo.ef04Info.totalLastFee_After >
             pCurTransInfo->IccInfo.ef04Info.totalFee_After)) {
        DebugLog(QString("EF04的金额异常总应收[%1], 总实收[%2], 本省收费[%3]")
                 .arg(pCurTransInfo->IccInfo.ef04Info.totalFee_After)
                 .arg(pCurTransInfo->IccInfo.ef04Info.totalLastFee_After)
                 .arg(pCurTransInfo->IccInfo.ef04Info.localLastFee_After));
        nErrorCode = CSpEventMgr::SPEVENT_EF04_ABNORMAL;
        sMsg = "标签累计金额异常";
        if (!bEmVeh) return false;
    }
    if (CTollGantryMgr::GetTollGantryMgr()->CheckNeedGrantryCalcFee()) {
        if (!CFareCalcUnit::CalcGantryFee(*pCurTransInfo, feeVehClass, sExStationHex,
                                          pCurTransInfo->bHolidayFree, TransTime, sMsg)) {
            nErrorCode = CSpEventMgr::SpEvent_EntryNoFee;
            pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_FeeQryFeeFailed);
            sMsg = QString("门架计费失败,Error:%1").arg(sMsg);
            return false;
        }
        DebugLog(QString("门架计费成功,bEf04Ok:%1").arg(bEf04Ok));
        //累计通行费金额
        pCurTransInfo->SumCalcMoney_Exit(FeeClass_OBU, bFree, true, nDiscountType);
        DebugLog(QString("天线%1，出口计费成功,应收:%2,OBU累计实收:%3,交易金额:%4,本省累计应收:%5,"
                         "本省累计实收:%6,本省折扣优惠后:%7,计费车型:%8,计费里程:%9")
                 .arg(nIndex)
                 .arg(pCurTransInfo->IccInfo.ef04Info.totalFee_After)
                 .arg(pCurTransInfo->IccInfo.ef04Info.totalLastFee_After)
                 .arg(pCurTransInfo->IccInfo.ef04Info.totalLastFee_After)
                 .arg(pCurTransInfo->IccInfo.ef04Info.localTotalFee_After)
                 .arg(pCurTransInfo->IccInfo.ef04Info.localLastFee_After)
                 .arg(pCurTransInfo->IccInfo.ef04Info.localLastFee_After)
                 .arg(feeVehClass)
                 .arg(pCurTransInfo->IccInfo.ef04Info.totalTollMiles_After));
    }

    //比较是否是U型车
    bool isU = false;  //是否是U型车
    bool IsLocalEntry = (pCurTransInfo->vehEntryInfo.sEnNetWorkIdHex == ORG_NETWORKID_HEX) &&
            (1 == pCurTransInfo->IccInfo.ef04Info.bProvinceCount);

    QString sHex = pIccInfo->CardTollInfo.sNetworkHex + pIccInfo->CardTollInfo.sEnStationHex;
    isU = Ptr_Info->IsUVeh(sHex) && IsLocalEntry;

    int nXEventId = XEvent_None;
    QString sDetail;
    if (isU) {
        if (Ptr_Info->bStopUJ()) {
            if (DevIndex_First == nIndex) {
                //
                nErrorCode = CSpEventMgr::SpEvent_Other;
                sMsg = QString("U型车前方处理");
                return false;
            }

            if (DevIndex_Second == nIndex) {
                nXEventId = XEvent_U;
                sDetail = QString("行驶%1分钟").arg(pCurTransInfo->vehEntryInfo.nPassTime / 60);
            }
        }
    }

    /*
    if (IsLocalEntry && !isU) {
        if (Ptr_Info->bStopUJ()) {
            if (DevIndex_First == nIndex) {
                //
                nErrorCode = CSpEventMgr::SpEvent_Other;
                sMsg = QString("调头车前方处理");
                return false;
            }
            if (DevIndex_Second == nIndex) {
                CFlagListInfo flagInfo;
                bool bFlag = QueryFlagInfo(pCurTransInfo, true, flagInfo);
                if (bFlag) {
                    nXEventId = XEvent_J;
                    sDetail = QString("经过调头点<%1>").arg(flagInfo.UturnPoint);
                }
            }
        }
    }*/

    if (nXEventId != XEvent_None) {
        int nOpType = 0;
        bool bOpRlt = false;
        bool bCheckRlt = CheckOtherXVehInfo(nXEventId, pCurTransInfo->VehInfo, nOpType, bOpRlt,
                                            NULL, 0, sDetail);
        if (bCheckRlt) {
            if (Operate_Continue != nOpType) {
                if (Operate_WaitConfirm == nOpType) {
                    sMsg = QString("请按<赣通卡>键继续天线交易");
                    bStop = false;
                    return false;
                } else if (Operate_Pause == nOpType) {
                    sFDMsg = QString("%1\n等待确认车辆信息").arg(sVehPlate);
                    bStop = false;
                    return false;
                }
            } else {
                m_OtherXVehInfo.Clear();
            }
        }
    }

    CFeeClass feeClass = FeeClass_OBU;
    qint32 perCent = 0;

    if (!bEmVeh) {
        bool bCheckFeeResult = CheckFeeClass_Etc(pCurTransInfo, pCurTransInfo->m_nTransFee,
                                                 feeClass, isU, IsLocalEntry, bFree, sMsg);

        if (!bCheckFeeResult) {
            nErrorCode = CSpEventMgr::SPEvent_TolalFeeOver;
            DebugLog(sMsg);
            pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_OBUTotalFeeHigh);
            return false;
        } else {
            if (feeClass == FeeClass_Min) {
                pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_OBUTotalFeeLow);
                //按最小费额重新计费
                pCurTransInfo->SumCalcMoney_Exit(feeClass, bFree, true, nDiscountType);
            }
        }
        DebugLog(QString("出口最终计费方式%1").arg(feeClass));
    }

    if (pCurTransInfo->m_nTransFee > 0 && IsLocalEntry) {
        pCurTransInfo->ProcessDiffFeeInfo(IsLocalEntry);
    } else {
        pCurTransInfo->ClearFeeInfoL();
    }

    quint32 dwConsumeMoney = 0;
    qint32 nTransFee = 0;
    qint32 nTotalFee = 0;
    qint32 nCardCost = 0;
    pCurTransInfo->GetTollMoney_Exit(nTotalFee, nTransFee, nCardCost);
    dwConsumeMoney = nTransFee;
    DebugLog(QString("出口计费,应收:%1,实收金额：%2，扣费金额：%3,计费方式:%4")
             .arg(nTotalFee)
             .arg(nTransFee)
             .arg(dwConsumeMoney)
             .arg((int)feeClass));

    if (bEf04Ok) {
        pCurTransInfo->IccInfo.CardTollInfo.dwTotalFee =
                (quint32)nTransFee;  // dwConsumeMoney;//累计总金额
        pCurTransInfo->IccInfo.Raw19.File0012Raw_GB.TotalFee =
                qToBigEndian(pCurTransInfo->IccInfo.CardTollInfo.dwTotalFee);
    }

    GetMainDlg()->Emit_ETCShowMoney(nIndex, nTransFee);

    bool bInVehList = false;
    if (!bEmVeh && Ptr_Info->bCheckCardBList()) {
        bInVehList = CheckVehList(nIndex, sVehPlate, pCurTransInfo->VehInfo.nVehPlateColor,
                                  nErrorCode, sMsg) > 0;
    }
    DebugLog(QString("出口天线%1查询黑名单").arg(nIndex));
    bool bInCardBlack = false;
    if (!bEmVeh && Ptr_Info->bCheckCardBList()) {
        QString sOBUId = QString::fromAscii(pCurTransInfo->OBUBaseInfo.szContractSerialNumber);
        QString sCardId = QString::fromAscii(pCurTransInfo->IccInfo.ProCardBasicInfo.szCardNo);
        quint16 wNetworkId = pCurTransInfo->IccInfo.ProCardBasicInfo.wNetWorkId;
        qint32 nListType = 0;
        QString sDesc;
        int nQryBRlt =
                CParamFileMgr::QryCardBList(3, sOBUId, sCardId, wNetworkId, nListType, sDesc, sMsg);
        if (0 != nQryBRlt) {
            bInCardBlack = true;
            pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_CardInBList);
            if (1 == nQryBRlt) {
                nErrorCode = CSpEventMgr::SpEvent_BlackOBU;
            } else if (2 == nQryBRlt) {
                nErrorCode = CSpEventMgr::GetEventIdByCardBListType(nListType);
            }
        }
    }

    if (bInCardBlack || bInVehList) {
        if ((bHasTrans) && (hasTransInfo.etcTransType == TransType_ExBlack)) return false;
        // dwConsumeMoney =0; //0元扣费
        /*1217 部测试，不需更新1类金额
        pCurTransInfo->SetLastMoney(nTotalFee, nTransFee, 0, feeClass, perCent);
        bStop = false;
        pCurTransInfo->IccInfo.Raw19.File0012Raw_GB.TotalFee = qToBigEndian(dwConsumeMoney);
        this->WriteCardAndEfo04File(nIndex, OBUID, TransType_ExBlack, 0, TransTime,
                                    &pCurTransInfo->IccInfo.Raw19,
        &pCurTransInfo->IccInfo.Raw15, NULL);
                                    */
        return false;
    }

    //只有储值卡才判断余额
    if ((!bEmVeh) && dwConsumeMoney > pIccInfo->dwBalance) {
        if (pIccInfo->ProCardBasicInfo.bType != CARD_TYPE_STORE_CARD &&
                pIccInfo->ProCardBasicInfo.bType != CARD_TYPE_TALLY_CARD) {
            DebugLog(QString("卡余额不足，卡类%1型错误").arg(pIccInfo->ProCardBasicInfo.bType));
            sMsg = QString("余额不足");
            return false;
        }
        if (pIccInfo->ProCardBasicInfo.bType == CARD_TYPE_STORE_CARD) {
            nErrorCode = CSpEventMgr::SpEvent_NoBalance;
            sMsg = QString("余额不足");
            DebugLog(QString("储值卡余额不足,fee:%1,balance:%2")
                     .arg(dwConsumeMoney)
                     .arg(pIccInfo->dwBalance));

            /*1217 部测试，不需更新1类金额
            pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_CardNoBalance);
            pCurTransInfo->SetLastMoney(nTotalFee, nTransFee, 0, feeClass, perCent);
            if (pCurTransInfo->IccInfo.ef04Info.IsValid()) {
                bStop = false;
                pCurTransInfo->IccInfo.Raw19.File0012Raw_GB.TotalFee =
            qToBigEndian(dwConsumeMoney); this->WriteCardAndEfo04File(nIndex, OBUID,
            TransType_ExStoreCardNoBalance, 0, TransTime, &pCurTransInfo->IccInfo.Raw19,
                                            &pCurTransInfo->IccInfo.Raw15, NULL);
            }*/
            return false;
        }
        //扣实际余额
        pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_CardNoBalance);
        DebugLog("记账卡余额不足，扣0元");
        dwConsumeMoney = 0;  //记账卡余额不足0元扣费 pIccInfo->dwBalance;
    }

    /*
    if (!bFree) {
        CGreenCarInfo greenInfo;
        bool bGreenVeh =
            QryGreenVehInfo(sVehPlate, pCurTransInfo->VehInfo.nVehPlateColor, greenInfo);

        if (bGreenVeh) {
            DebugLog(QString("绿通车,预约编号:%1,车辆状态%2")
                         .arg(greenInfo.appointMentId)
                         .arg(greenInfo.passStateId));
            pCurTransInfo->SetGreenCarInfo(true, &greenInfo);
            dwConsumeMoney = 0;
        }
    }
    */
    pCurTransInfo->SetLastMoney(nTotalFee, nTransFee, dwConsumeMoney, feeClass, perCent);
    DebugLog(QString("最终扣费金额[%1],卡内余额[%2]").arg(dwConsumeMoney).arg(pIccInfo->dwBalance));
    CStdLog::StdLogConsumeInfo(
                QString::fromAscii(pCurTransInfo->OBUBaseInfo.szTime), nTotalFee, nTransFee,
                pCurTransInfo->IccInfo.ef04Info.totalTollMiles_After, pCurTransInfo->actualFeeClass,
                QString::fromAscii(pCurTransInfo->IccInfo.ProCardBasicInfo.szCardNo));

    CPro0019Raw_NewGB Raw0019;
    memset(&Raw0019, 0, sizeof Raw0019);
    CCardTollInfo cardTollInfo = pCurTransInfo->IccInfo.CardTollInfo;
    cardTollInfo.sEnStationHex = Ptr_Info->GetHexStationID().right(4);
    cardTollInfo.sNetworkHex = ORG_NETWORKID_HEX;
    cardTollInfo.sEnLaneHex = Ptr_Info->GetHexLaneID().right(2);
    cardTollInfo.bLaneId = Ptr_Info->GetLaneId();
    cardTollInfo.szPassTime = TransTime;
    if (Ptr_Info->CheckReplaceWriteCard()) {
        cardTollInfo.sLastGantryHex = Ptr_Info->GetReplaceGantryHex();
        if (cardTollInfo.sLastGantryHex.isEmpty())
            cardTollInfo.sLastGantryHex = CTollGantryMgr::GetTollGantryMgr()->GetGantryHex();
    } else
        cardTollInfo.sLastGantryHex = CTollGantryMgr::GetTollGantryMgr()->GetGantryHex();
    cardTollInfo.dwDoorFramePassTime = QDateTime2UnixTime_GB(TransTime);
    cardTollInfo.dwTotalFee = 0;
    CCardFileConverter::CardTollInfo2Pro0019Raw_NewGB(&Raw0019, &cardTollInfo);
    if (Ptr_Info->CheckReplaceWriteCard()) {
        Raw0019.File0012Raw_GB.bPassStatus = 0x0F;
    } else
        Raw0019.File0012Raw_GB.bPassStatus = Ptr_Info->GetLaneType();

    CETCTransType transType = TransType_ExNormal;
    if (!bEf04Ok) transType = TransType_ExOnlyAppTrans;

    this->WriteCardAndEfo04File(nIndex, OBUID, transType, dwConsumeMoney, TransTime, &Raw0019,
                                &pCurTransInfo->IccInfo.Raw15, NULL);

    TransInfoShared Value;
    Value.nStatus = 0;
    Value.Transtime = TransTime;
    Value.nLaneid = Ptr_Info->GetLaneId();
    Value.nMediatype = 1;
    Value.nVehtype = pCurTransInfo->VehInfo.VehClass;
    CTransInfoShared::GetTransInfoShared()->SendData(sVehPlate, Value);

    // pCurTransInfo->ClearTransInfo();
    sMsg = QString("等待交易结果...");
    bStop = false;
    return true;
}

bool CLaneState_VehInput::GetOpenGantyInfo_New(int nVehClass, const QString &sVehPlate,
                                               int nPlateColor, const CCardTollInfo *pCardTollInfo,
                                               COpenGantryInfo &openGantryInfo, bool bETC)
{
    if (!Ptr_Info->bOpenLane()) {
        return false;
    }

    if (isCar(nVehClass) && bETC) {
        QString sTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
        if (sTime >= QString("20250101000000")) {
            return false;
        }
    }

    time_t curTime = QDateTime::currentDateTime().toTime_t();

    QString sGantryHex;
    quint32 dwGantryTime = 0;
    bool bInCard = false;
    if (pCardTollInfo) {
        if (pCardTollInfo->bPassStatus == 0x0F) {
            quint32 dwPassTimeInCard = pCardTollInfo->dwDoorFramePassTime;
            QDateTime PassTimeInCard;
            UnixTime2QDateTime_GB(dwPassTimeInCard, PassTimeInCard);
            DebugLog(QString("卡内开放式车道:%1,%2")
                     .arg(pCardTollInfo->sLastGantryHex)
                     .arg(PassTimeInCard.toString("yyyy-MM-dd hh:mm:ss")));
            if (dwPassTimeInCard <= curTime) {
                if (curTime - dwPassTimeInCard < 60 * 60) {
                    sGantryHex = pCardTollInfo->sLastGantryHex;
                    dwGantryTime = dwPassTimeInCard;
                    bInCard = true;
                    openGantryInfo.sGantryHex = sGantryHex;
                    openGantryInfo.uEntryTime = dwGantryTime;
                    openGantryInfo.nDataFormat = 0;
                    openGantryInfo.nDataFrom = 0;
                } else {
                    DebugLog(QString("卡内开放式车道入口时间超过1小时,继续查询"));
                }
            } else {
                DebugLog("卡内开放式车道时间超出当前时间,继续查询");
            }
        }
    }

    DebugLog(QString("开放式查询,color:%1,Plate:%2").arg(nPlateColor).arg(sVehPlate));
    if (GantryByPlate::GetSTGantryByPlate()->QryOpenGantryInfo(nPlateColor, sVehPlate,
                                                               openGantryInfo)) {
        if (bInCard) {
            if (openGantryInfo.sGantryHex != sGantryHex) {
                DebugLog(QString("开放式查询车牌返回%1,与卡内%2不符,以查询为主")
                         .arg(openGantryInfo.sGantryHex)
                         .arg(sGantryHex));
            }
        }
        QDateTime entryTime;
        UnixTime2QDateTime_GB(openGantryInfo.uEntryTime, entryTime);
        DebugLog(QString("查询返回开放式门架信息:%1,%2,%3,vlpid:%4")
                 .arg(sVehPlate)
                 .arg(openGantryInfo.sGantryHex)
                 .arg(entryTime.toString("yyyy-MM-dd hh:mm:ss"))
                 .arg(openGantryInfo.sVlpId));
        openGantryInfo.nDataFrom = 1;
        return true;
    } else {
        if (bInCard) {
            openGantryInfo.sGantryHex = sGantryHex;
            openGantryInfo.uEntryTime = dwGantryTime;
            openGantryInfo.nDataFrom = 0;
            openGantryInfo.nDataFormat = 0;
            openGantryInfo.sVlpId.clear();
            QDateTime entryTime;
            if (!UnixTime2QDateTime_GB(dwGantryTime, entryTime)) {
                DebugLog(QString("卡内门架入口时间%1错误").arg(dwGantryTime));
                return false;
            }
            DebugLog(QString("[%1]开放式查询失败,以卡内为准,%2,%3")
                     .arg(sVehPlate)
                     .arg(sGantryHex)
                     .arg(entryTime.toString("yyyy-MM-dd hh:mm:ss")));
            return true;
        } else {
            QString sInfo = QString("开放式查询失败,vlp:%1").arg(sVehPlate);
            DebugLog(sInfo);
        }
    }
    return false;
}

bool CLaneState_VehInput::WriteCardAndEfo04File(int nIndex, quint32 OBUID, CETCTransType transType,
                                                qint32 dwMoney, const QDateTime &TransTime,
                                                const CPro0019Raw_NewGB *pRaw0019,
                                                const CPro0015Raw *pRaw0015, EF04Raw *pEf04Raw)
{
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(nIndex);
    CTransInfo *pLastTransInfo = Ptr_ETCCtrl->GetLastTransInfo(nIndex);
    if (!pCurTransInfo) return false;

    EF04Raw ef04Raw;
    if (pEf04Raw) {
        memcpy(&ef04Raw, pEf04Raw, sizeof ef04Raw);
    }
    const CPro0019Raw_NewGB *pRaw0019ToWrite = NULL;

    quint8 rsuTradeType = Rsu_TradeType_0019;
    switch (transType) {
    case TransType_EnNormal: {
        pRaw0019ToWrite = pRaw0019;
        ef04Raw.SetCardInfo(pRaw0019, pRaw0015);
        ef04Raw.SetTradeFlag(0xAA);

        rsuTradeType = Rsu_TradeType_EF04_0019;
        break;
    }
    case TransType_EnNoCard:  //写BB和入口信息 ，不写卡
    {
        ef04Raw.SetCardInfo(pRaw0019, NULL);
        ef04Raw.SetTradeFlag(0xBB);

        rsuTradeType = Rsu_TradeType_EF04;
        break;
    }
    case TransType_EnStoreNoBlance:  // Ef04 写AA+入口和基本信息。不写卡
    {
        ef04Raw.SetCardInfo(pRaw0019, pRaw0015);
        ef04Raw.SetTradeFlag(0xAA);
        rsuTradeType = Rsu_TradeType_EF04;
        break;
    }

        //
    case TransType_ExNormal:
    case TransType_ExBlack:
    case TransType_ExStoreCardNoBalance: {
        if (DevIndex_Second == nIndex) {
            memset(ef04Raw.file0019Raw, 0, 3);
        }
        pRaw0019ToWrite = pRaw0019;
        rsuTradeType = Rsu_TradeType_0019EF04;
        break;
    }
    case TransType_EnOnlyAppTrans:
    case TransType_ExOnlyAppTrans: {
        pRaw0019ToWrite = pRaw0019;
        rsuTradeType = Rsu_TradeType_0019;
        // pCurTransInfo->bChargeMode=0;
        break;
    }

    default: {
        return false;
        break;
    }
    }

    CRsuDev_GB_EF *pRsuCtrl = (CRsuDev_GB_EF *)CDeviceFactory::GetRsuDev(nIndex);
    if (!pRsuCtrl) return false;

    quint8 bCardDivFactor[8];
    memset(bCardDivFactor, 0, sizeof bCardDivFactor);
    if (pRaw0015) {
        memcpy(bCardDivFactor, pRaw0015->IssueOrg, 8);
    } else {
        memcpy(bCardDivFactor, pCurTransInfo->IccInfo.Raw15.IssueOrg, 8);
    }
    pCurTransInfo->etcTransType = transType;

    quint32 dwMoneyToConsume = dwMoney;

    // Lane_Test
    // if (dwMoneyToConsume > 0) dwMoneyToConsume = 1;

    pRsuCtrl->WriteCardAndEf04Info(OBUID, bCardDivFactor, pRaw0019ToWrite, dwMoneyToConsume,
                                   TransTime, &ef04Raw, rsuTradeType);

    //保存交易信息
    pCurTransInfo->SetConsumeMoney(dwMoney, TransTime);

    //提前保存序列号和终端机编号
    pCurTransInfo->m_nPsamSerial = pRsuCtrl->GetNextPsamSerial();
    pRsuCtrl->GetTerminateId(pCurTransInfo->m_TermCode);
    pCurTransInfo->m_nRsuIndex = nIndex;

    pCurTransInfo->SetTransState(CTransInfo::Ts_WaitOpResult);
    pCurTransInfo->SaveTo(*pLastTransInfo);
    return true;
}

bool CLaneState_VehInput::ProcessB5FrameInfo_Entry(int nIndex, quint32 OBUID, int &nErrorCode,
                                                   QString &sMsg)
{
    CRsuDev *pRsuCtrl = CDeviceFactory::GetRsuDev(nIndex);

    CRsuOpResult rsuOpResult = *pRsuCtrl->GetRsuOpResult();
    CRsuOpResult *pOpResult = &rsuOpResult;

    nErrorCode = 0;
    CTransInfo *pLastTransInfo = Ptr_ETCCtrl->GetLastTransInfo(nIndex);
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(nIndex);
    if (!pCurTransInfo) {
        DebugLog(QString("天线%1交易对象不存在").arg(nIndex));
        return false;
    }
    QString sLog;
    sLog = QString("天线[%1]收到B5 ErrorCode:%2").arg(nIndex).arg(pOpResult->ErrorCode);
    ShowLog(sLog);

    if (OBUID != pLastTransInfo->dwOBUID) {
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sMsg = QString("接收到的交易结果OBUID[%1]与原先[%2]不符")
                .arg(OBUID)
                .arg(pLastTransInfo->dwOBUID);
        pCurTransInfo->ClearTransInfo();
        pRsuCtrl->StopDeal(OBUID);
        return false;
    }

    //如果没有等待交易结果的流水数据，则不处理
#ifndef Lane_Test
    if (!pLastTransInfo->bWaitOpResult()) {
        nErrorCode = CSpEventMgr::SpEvent_Other;
        DebugLog(QString("%1状态下收到交易结果,不予处理").arg(pLastTransInfo->GetTransStateStr()));
        pCurTransInfo->ClearTransInfo();
        pRsuCtrl->StopDeal(OBUID);
        return false;
    }
#endif

    CTransInfo hasTransInfo;
    bool bHasTrans = Ptr_ETCCtrl->CheckVehhasTransByOBUId(OBUID, 120, hasTransInfo);

    if (pOpResult->ErrorCode != 0)  //错误码大于0，交易失败
    {
        pRsuCtrl->StopDeal(OBUID);
        nErrorCode = CSpEventMgr::SpEvent_TransFailed;
        pLastTransInfo->nErrorCode = pOpResult->ErrorCode;
        pLastTransInfo->curFrameId = 0xB5;
        pLastTransInfo->curFrameTime = QDateTime::currentMSecsSinceEpoch();

        sMsg = QString("交易失败,错误代码[%1]").arg(pOpResult->ErrorCode);
        if (!bHasTrans) {  //如果已经交易了，则不再保存流水（避免连续交易失败产生多条流水）
            if (pLastTransInfo->IsWhiteListVeh()) {
                CompleteTransForWhiteVeh(nIndex);
                pCurTransInfo->ClearTransInfo();
                return true;
            }
            pLastTransInfo->CompleteTrans(nIndex, TransPT_None, pOpResult, Tr_Failed);
            Ptr_ETCCtrl->CompleteTrans(Tr_Failed, nIndex);
        }
        pCurTransInfo->ClearTransInfo();
        return false;
    }

    //收到B5，清除当前业务
    pCurTransInfo->ClearTransInfo();

    //这里应该不用判断重复交易，因为异常处理前已经判断重复交易了
    if (pLastTransInfo->etcTransType >= TransType_abNormal) {
        //保存交易失败流水
        //入口无卡，余额不足时，发送C2，提示天线更变Id，重新搜索
        pRsuCtrl->StopDeal(OBUID);
        if (pLastTransInfo->IsWhiteListVeh()) {
            CompleteTransForWhiteVeh(nIndex);
            return true;
        }
        pLastTransInfo->CompleteTrans(nIndex, TransPT_None, pOpResult, Tr_Failed);
        Ptr_ETCCtrl->CompleteTrans(Tr_Failed, nIndex);
        return false;
    }

    sLog = QString("B5完成,终端交易序列号:%1,交易序列号: %2,TAC: %3")
            .arg(pOpResult->PSAMPaySerial)
            .arg(pOpResult->CPUCardPaySerial)
            .arg(pOpResult->TAC);

    ShowLog(sLog);
    if (pLastTransInfo->bReGetTac) {
        DebugLog(QString("重新获取TAC 成功"));
    }

    pLastTransInfo->CompleteTrans(nIndex, TransPT_None, pOpResult, Tr_Successed);
    Ptr_ETCCtrl->CompleteTrans(Tr_Successed, nIndex);

    bool bDelay = false;
    if (CheckAndInitForDelay(nIndex, OBUID, pLastTransInfo, DevChannel_ESAM)) {
        bDelay = true;
    } else if (CheckAndInitForDelay(nIndex, OBUID, pLastTransInfo, DevChannel_ETCCard)) {
        bDelay = true;
    }
    if (!bDelay) {
        pRsuCtrl->ContinueDeal(OBUID);
    }

    TransInfoShared Value;
    Value.nStatus = 1;
    Value.Transtime = pLastTransInfo->TransTime;
    Value.nLaneid = Ptr_Info->GetLaneId();
    Value.nMediatype = 1;
    Value.nVehtype = pLastTransInfo->VehInfo.VehClass;
    QString sPlate = GB2312toUnicode(pLastTransInfo->VehInfo.szVehPlate);
    CTransInfoShared::GetTransInfoShared()->SendData(sPlate, Value);
    sMsg = QString("ETC交易成功,车辆放行");
    this->ShowMessage(sMsg);
    if (nIndex == DevIndex_Second) {
        this->ClearVehInfo();
        QMutexLocker locker(&m_OtherXMt);
        m_OtherXVehInfo.Clear();
    }

    /*
    if (Ptr_Info->bHaveCardMgr()) {
        CCardMgr *pCardMgr = CDeviceFactory::GetCardMachine();
        if (pCardMgr) {
             pCardMgr->MoveCardHead(false);
             SleeperThread::msleep(80);
             pCardMgr->PlaySnd(CCardMgr::SndId_Thank);
        }
    }*/
    return true;
}

bool CLaneState_VehInput::ProcessB5FrameInfo_Exit(int nIndex, quint32 OBUID, int &nErrorCode,
                                                  QString &sMsg)
{
#ifdef QT_DEBUG
    StopCardMgrTimer();
    StartCardMgrTimer();
#endif
    CRsuDev_GB_EF *pRsuCtrl = (CRsuDev_GB_EF *)CDeviceFactory::GetRsuDev(nIndex);
    CRsuOpResult *pOpResult = NULL;

    CRsuOpResult rsuOpResult = *pRsuCtrl->GetRsuOpResult();
    pOpResult = &rsuOpResult;

    QString sLog;
    sLog = QString("天线[%1]收到B5 ErrorCode:%2").arg(nIndex).arg(pOpResult->ErrorCode);
    ShowLog(sLog);

    nErrorCode = 0;

    CTransInfo *pLastTransInfo = Ptr_ETCCtrl->GetLastTransInfo(nIndex);
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(nIndex);
    if (!pCurTransInfo) {
        ErrorLog(QString("天线%1 B5帧交易对象为空"));
        return false;
    }

    if (OBUID != pLastTransInfo->dwOBUID) {
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sMsg = QString("接收到的交易结果OBUID[%1]与原先[%2]不符")
                .arg(OBUID)
                .arg(pLastTransInfo->dwOBUID);
        pRsuCtrl->StopDeal(OBUID);
        return false;
    }

    //如果没有等待交易结果的流水数据，则不处理
#ifndef Lane_Test
    if (!pLastTransInfo->bWaitOpResult()) {
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sMsg = QString("%1状态下收到交易结果,不予处理").arg(pLastTransInfo->GetTransStateStr());
        pRsuCtrl->StopDeal(OBUID);
        return false;
    }
#endif

    CTransInfo hasTransInfo;
    bool bHasTrans = Ptr_ETCCtrl->CheckVehhasTransByOBUId(OBUID, 120, hasTransInfo);
    if (pOpResult->ErrorCode != 0)  //错误码大于0，交易失败
    {
        pRsuCtrl->StopDeal(OBUID);

        pLastTransInfo->nErrorCode = pOpResult->ErrorCode;
        pLastTransInfo->curFrameId = 0xB5;
        pLastTransInfo->curFrameTime = QDateTime::currentMSecsSinceEpoch();

        if (!bHasTrans) {
            if (pLastTransInfo->IsWhiteListVeh()) {
                CompleteTransForWhiteVeh(nIndex);
                return true;
            }
            nErrorCode = CSpEventMgr::SpEvent_TransFailed;
            CRsuOpResult opResult;
            opResult.Clear();
            opResult.CardBalance = pLastTransInfo->IccInfo.dwBalance;
            opResult.CPUCardPaySerial = 0;
            opResult.PSAMPaySerial = pOpResult->PSAMPaySerial;  // pRsuCtrl->GetNextPsamSerial();
            opResult.TransType = 0x09;
            opResult.PurchaseTime = pLastTransInfo->TransTime;
            pRsuCtrl->GetTerminateId(opResult.RSUTerminalId);
            sMsg = pRsuCtrl->GetErrorMsg_B5(nErrorCode);  // NO tac
            pLastTransInfo->CompleteTrans(nIndex, TransPT_OBU, &opResult, Tr_Failed);
            Ptr_ETCCtrl->CompleteTrans(Tr_Failed, nIndex);
        }
        pCurTransInfo->ClearTransInfo();
        return false;
    }

    sLog = QString("B5完成,终端交易序列号:%1,交易序列号: %2,TAC: %3")
            .arg(pOpResult->PSAMPaySerial)
            .arg(pOpResult->CPUCardPaySerial)
            .arg(pOpResult->TAC);

    ShowLog(sLog);

    if (pLastTransInfo->bReGetTac) {
        DebugLog(QString("重新获取TAC 成功"));
        if (pLastTransInfo->m_nRsuIndex != nIndex) {
            DebugLog(QString("天线%1重取Tac成功,原天线%2,更新交易序列号:%3,天线返回:%4")
                     .arg(nIndex)
                     .arg(pLastTransInfo->m_nRsuIndex)
                     .arg(pLastTransInfo->m_nPsamSerial)
                     .arg(pOpResult->PSAMPaySerial));
            pOpResult->PSAMPaySerial = pLastTransInfo->m_nPsamSerial;
            memcpy(pOpResult->RSUTerminalId, pLastTransInfo->m_TermCode, 6);
        }
    }

    if (pLastTransInfo->etcTransType >= TransType_abNormal) {
        //保存交易失败流水
        pRsuCtrl->ContinueDeal(OBUID);

        if (pLastTransInfo->IsWhiteListVeh()) {
            CompleteTransForWhiteVeh(nIndex);
            return true;
        }
        DebugLog(QString("异常交易流水%1,交易完毕").arg(pLastTransInfo->etcTransType));
        pLastTransInfo->CompleteTrans(nIndex, TransPT_OBU, pOpResult, Tr_Failed);
        Ptr_ETCCtrl->CompleteTrans(Tr_Failed, nIndex);
        pCurTransInfo->ClearTransInfo();
        //出口异常交易- 黑名单、余额不足认为交易成功，天线不需要重新搜索obu
        return false;
    }

    if (pLastTransInfo->IccInfo.ef04Info.IsValid()) {
        if (pLastTransInfo->IccInfo.ef04Info.bProvinceCount >= 1) {
            if (pLastTransInfo->IccInfo.ef04Info.bProvinceCount > 1) {
                pLastTransInfo->SetOncePaySpEvent(OncePay_Sp_RsuB7Failed);
            }
            pRsuCtrl->ReadEf04_C7(OBUID, pLastTransInfo->IccInfo.ef04Info.bProvinceCount);
        } else {
            pRsuCtrl->ContinueDeal(OBUID);
        }
    } else {
        pRsuCtrl->ContinueDeal(OBUID);
    }

    pLastTransInfo->CompleteTrans(nIndex, TransPT_OBU, pOpResult, Tr_Successed);
    Ptr_ETCCtrl->CompleteTrans(Tr_Successed, nIndex);
    if (isTruck(pLastTransInfo->VehInfo.VehClass)) Ptr_ETCCtrl->ShowQrCode(pLastTransInfo);
    //交易成功，清除当前业务
    pCurTransInfo->ClearTransInfo();
    TransInfoShared Value;
    Value.nStatus = 1;
    Value.Transtime = pLastTransInfo->TransTime;
    Value.nLaneid = Ptr_Info->GetLaneId();
    Value.nMediatype = 1;
    Value.nVehtype = pLastTransInfo->VehInfo.VehClass;
    QString sPlate = GB2312toUnicode(pLastTransInfo->VehInfo.szVehPlate);
    CTransInfoShared::GetTransInfoShared()->SendData(sPlate, Value);
    sMsg = QString("交易成功,车辆放行");

    this->ShowMessage(sMsg);
    if (nIndex == DevIndex_Second) this->ClearVehInfo();
#ifdef QT_DEBUG
    StopCardMgrTimer();
    StartCardMgrTimer();
#endif
    return true;
}

bool CLaneState_VehInput::ProcessB7FrameInfo(int nIndex, quint32 OBUID, int &nErrorCode,
                                             QString &sMsg)
{
    CRsuDev_GB_EF *pRsuCtrl = (CRsuDev_GB_EF *)CDeviceFactory::GetRsuDev(nIndex);

    CTransInfo *pLastTransInfo = Ptr_ETCCtrl->GetLastTransInfo(nIndex);
    if (!pLastTransInfo) {
        DebugLog("接收到B7帧,报文已经发送");
        return false;
    }

    if (OBUID != pLastTransInfo->dwOBUID) {
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sMsg = QString("B7帧OBUID[%1]与原先[%2]不符").arg(OBUID).arg(pLastTransInfo->dwOBUID);
        return false;
    }

    const CRsuProvinceFeeGroupInfo *pProvinceFeeGroup = pRsuCtrl->GetRsuProvinceFeeGroup();
    CRsuProvinceFeeGroupInfo feeGroupInfo = *pProvinceFeeGroup;
    pProvinceFeeGroup = &feeGroupInfo;
    if (pProvinceFeeGroup->ErrorCode > 0) {
        DebugLog(QString("B7帧数据返回失败,ErrorCode:%1").arg(pProvinceFeeGroup->ErrorCode));
        return false;
    }

    QList<CProvinceFeeInfo> ProvinceFeeGroup = pProvinceFeeGroup->ProvinceFeeGroup;
    QString str;
    QList<CProvinceFeeInfo>::iterator it = ProvinceFeeGroup.begin();
    for (; it != ProvinceFeeGroup.end(); it++) {
        if (0x36 != it->proviceId)
            str +=
                    QString("|%1:%2").arg(it->proviceId, 2, 16, QLatin1Char('0')).arg(it->provinceFee);

        if (pLastTransInfo->bIsFree) {
            it->provinceFee = 0;
        }
    }
    str.append(QString("|36:%2").arg(pLastTransInfo->IccInfo.ef04Info.localLastFee_After));
    str.remove(0, 1);
    DebugLog(QString("分省交易信息:%1").arg(str));

    QString sTime = QTime::currentTime().toString("hhmmsszzz");
    StdInfoLog(LogKey::OneKey_OBUEF04Info, LogKey::OBUEF04Info_ProvFee, sTime,
               QString::fromAscii(pLastTransInfo->OBUBaseInfo.szTime), str);

    pLastTransInfo->IccInfo.ef04Info.AddProvinceFeeInfo(ProvinceFeeGroup);
    if (pLastTransInfo->IccInfo.ef04Info.bProvinceCount_After == ProvinceFeeGroup.size() &&
            pLastTransInfo->IccInfo.ef04Info.bProvinceCount > 1)
        pLastTransInfo->SetOncePaySpEvent(OncePay_Sp_RsuB7Failed, false);

    Ptr_ETCCtrl->SetProvinceFeeGroupToLastTrans(nIndex, OBUID, ProvinceFeeGroup);
    return true;
}

bool CLaneState_VehInput::ProcessE2FrameInfo(int nIndex, quint32 OBUID, int &nErrorCode,
                                             QString &sMsg)
{
    m_bFrameId = 0xE2;
    CRsuDev *pRsuDev = CDeviceFactory::GetRsuDev(nIndex);
    nErrorCode = 0;

    if (!pRsuDev) {
        ErrorLog(QString("获取天线%1设备失败").arg(nIndex));
        return false;
    }

    // 安全的类型转换
    CRsuDev_GB_EF *pDev = dynamic_cast<CRsuDev_GB_EF*>(pRsuDev);
    if (!pDev) {
        ErrorLog(QString("天线%1不是CRsuDev_GB_EF类型，无法处理E2帧").arg(nIndex));
        return false;
    }

    CTransParentResult result;
    pDev->GetTransparentResult(result);
    QList<QByteArray> cmds;
    int nRlt = ETCDelay::GetSingleStance()->ProcessTransparentResult(OBUID, result.devChannel,
                                                                     result.responses, cmds, sMsg);
    if (1 == nRlt) {  //发送
        //获取到延期指令
        pDev->TransParentPass_F2(OBUID, result.devChannel, cmds);  //
        int nDelayType = ETCDelay::GetSingleStance()->GetDelayType(OBUID, result.devChannel);
        QString sDelayName =
                ETCDelay::ETC_AddDate == nDelayType ? QString("延期") : QString("续期");
        if (1 == result.devChannel)
            DisplayTransError_ETC(nIndex, 0, QString("发送卡片%1指令").arg(sDelayName),
                                  QString(""));
        else if (2 == result.devChannel) {
            DisplayTransError_ETC(nIndex, 0, QString("发送标签延期指令").arg(sDelayName),
                                  QString(""));
        }
    } else if (2 == nRlt) {
        //
        int nDelayType = ETCDelay::GetSingleStance()->GetDelayType(OBUID, result.devChannel);
        QString sDevName = 1 == result.devChannel ? QString("ETC卡") : QString("电子标签");
        if (ETCDelay::ETC_AddDate == nDelayType) {
            // C1
            pDev->ContinueDeal(OBUID);
            DisplayTransError_ETC(nIndex, 0, QString("%1延期成功").arg(sDevName), QString(""));
        } else if (ETCDelay::ETC_NewDate == nDelayType) {
            pDev->StopDeal(OBUID);
            DisplayTransError_ETC(nIndex, 0, QString("%1续期成功").arg(sDevName), QString(""));
        }
    } else {
        DisplayTransError_ETC(nIndex, 0, sMsg, QString(""));
    }
    return true;
}

bool CLaneState_VehInput::CheckCardTollInfo(const CCardTollInfo &cardTollInfo, bool bEntry,
                                            int nErrorCode, QString &sError)
{
    QString sEnHex = cardTollInfo.sNetworkHex + cardTollInfo.sEnStationHex;
    QDateTime curTime = QDateTime::currentDateTime();
    quint32 dwCurTime = curTime.toTime_t();
    bool bValidEnFlag = CCardFileConverter::IsValidEnFlag(cardTollInfo.bPassStatus);

    if (!Ptr_Info->IsUVeh(sEnHex)) {
        return true;
    }

    qint32 uTimes = Ptr_Info->GetUTimes();
    if (0 == uTimes) {
        return true;
    }

    if (bEntry) {
        if (!bValidEnFlag) {
            if (dwCurTime >= cardTollInfo.dwPassTime) {
                qint32 nSubTime = dwCurTime - cardTollInfo.dwPassTime;
                if (nSubTime < 15) {
                    nErrorCode = CSpEventMgr::SpEvent_Other;
                    sError = QString("出口调头车辆,请等候[%1]秒").arg(15 - nSubTime);
                    return false;
                }
            }
        }
    } else {
        if (bValidEnFlag) {
            if ((dwCurTime >= cardTollInfo.dwPassTime) &&
                    (dwCurTime - cardTollInfo.dwPassTime < uTimes)) {
                nErrorCode = CSpEventMgr::SpEvent_Other;
                sError = QString(QString("U型车行驶时间不超过%1秒钟").arg(uTimes));
                return false;
            }
        }
    }
    return true;
}

bool CLaneState_VehInput::CheckCardHasTrans(const CCardTollInfo &cardTollInfo, bool bEntry)
{
    /*
 #ifdef QT_DEBUG
     return true;
 #endif*/
    QString sEnHex = cardTollInfo.sNetworkHex + cardTollInfo.sEnStationHex;
    QDateTime curTime = QDateTime::currentDateTime();
    quint32 dwCurTime = curTime.toTime_t();
    bool bValidEnFlag = CCardFileConverter::IsValidEnFlag(cardTollInfo.bPassStatus);

    if (bEntry) {
        if (!bValidEnFlag) {
            return false;
        }
    } else {
        if (bValidEnFlag) return false;
    }
    //
    if (!Ptr_Info->IsUVeh(sEnHex)) {
        return false;
    }
#ifdef Lane_Test
    return true;
#endif

    if (dwCurTime >= cardTollInfo.dwPassTime) {
        qint32 nSubTime = dwCurTime - cardTollInfo.dwPassTime;
        if (sEnHex == Ptr_Info->GetHexStationID()) {
            if (nSubTime > 600) {
                DebugLog(QString("重复交易车辆,上次交易时间[%1]超过10分钟，按新车处理")
                         .arg(cardTollInfo.dwPassTime));
                return false;
            }
        } else {
            if (nSubTime > 3600) {
                DebugLog(QString("开放式入口，重复交易车辆,上次交易时间[%1]超过1小时，按新车处理")
                         .arg(cardTollInfo.dwPassTime));
                return false;
            } else {
                DebugLog(QString("开放式入口，重复交易车辆,上次交易时间[%1]")
                         .arg(cardTollInfo.dwPassTime));
            }
        }
    }
    return true;
}

void CLaneState_VehInput::GetEntryInforEntry(const QDateTime &TransTime,
                                             const CCardTollInfo &cardTollInfo,
                                             CTransInfo *pTransInfo, CVehEntryInfo &EntryInfo)
{
    EntryInfo.Clear();

    EntryInfo.nEntryType = Entry_ByManual;
    EntryInfo.sEnNetWorkIdHex = cardTollInfo.sNetworkHex;
    EntryInfo.sEnLaneHex = cardTollInfo.sEnLaneHex;
    EntryInfo.sEnStationHex = cardTollInfo.sEnStationHex;
    //入口的入口记录，门架记录上一个门架信息，不是当前门架
    EntryInfo.sGantryNumHex.clear();
    EntryInfo.sLastGantryId.clear();
    EntryInfo.dwTotalWeight = cardTollInfo.dwToltalWeight;
    if (isCar(cardTollInfo.bVehClass)) {
        EntryInfo.dwTotalWeight = cardTollInfo.dwToltalWeight;
        EntryInfo.dwWeightLimit = cardTollInfo.dwLimitWeight;
    } else {
        EntryInfo.dwTotalWeight = cardTollInfo.dwToltalWeight;
        EntryInfo.dwWeightLimit = cardTollInfo.dwLimitWeight;
    }

    DebugLog(QString("GetEntryInfo,TotalWeight:%1,LimitWeight:%2")
             .arg(EntryInfo.dwTotalWeight)
             .arg(EntryInfo.dwWeightLimit));

    EntryInfo.VehicalAxles = cardTollInfo.VehicalAxles;
    EntryInfo.bVehState = cardTollInfo.bVehState;
    EntryInfo.gantryPassTime = 0;
    EntryInfo.sEnGBStationId =
            pTransInfo->m_curGantryInfo
            .sGBStationId;  // gantryInfo.sGBStationId ;//Ptr_Info->GetGBStationId();
    EntryInfo.sEnStaionName =
            pTransInfo->m_curGantryInfo
            .sStationName;  // gantryInfo.sStationName;//Ptr_Info->GetStationName();
    EntryInfo.dwEnStationID = pTransInfo->m_curGantryInfo
            .nStationId;  // gantryInfo.nStationId;//Ptr_Info->GetStationID();
    EntryInfo.nEnLaneID = pTransInfo->m_curGantryInfo.bLaneId;      // Ptr_Info->GetLaneId();
    EntryInfo.sEnGBLaneId = pTransInfo->m_curGantryInfo.sGBLaneId;  // Ptr_Info->GetGBLaneId();

    EntryInfo.bEnLaneType = cardTollInfo.bPassStatus;
    EntryInfo.bEnVC = cardTollInfo.bVehClass;
    EntryInfo.bEnVT = cardTollInfo.bVehTollType;
    EntryInfo.bEnVLPC = cardTollInfo.bVehPlateColor;
    qstrncpy(EntryInfo.szEnVLP, cardTollInfo.szVehPlate, sizeof(EntryInfo.szEnVLP));

    EntryInfo.EnTime = TransTime;
    return;
}

/*
 * 处理车型键输入, 只响应车型键输入
 */
void CLaneState_VehInput::ProcessKeyEvent_VehClassInput(MtcKeyPressedEvent *mtcKeyEvent)
{
    //需要判断是否可编辑车型
    m_IsInputingVLP = true;
    CVehClass vehClass = (CVehClass)mtcKeyEvent->vehClass();
    //如果车型对应货6(集1键，显示车型选择菜单)
    if (vehClass == VC_Truck6) {
        vehClass = CFuncMenu::GetFunMenu()->ShowMenu_SelTruck6();
        if (vehClass == VC_None) {
            m_IsInputingVLP = false;
            return;
        }
    }
    CVehInfo vehInfo;
    GetCurVehInfo(vehInfo);
    if (isCar(vehClass) && vehInfo.GBVehType == UVT_FarmProduct) {
        ShowErrorMessage(QString("客车不能为绿通车"));
        m_IsInputingVLP = false;
        return;
    }
    SetInputVehClass(vehClass);

    //如果没输入过车牌, 自动弹出输入车牌, 车型
    if (!m_bInputPlate) {
        InputPlate();

        //由计重取出轴组，填写
        if (VehWeightInfo::GetVehWeightInfo()->GetVehCount() > 0) {
            CVehAxisInfo firstAxisInfo;
            VehWeightInfo::GetVehWeightInfo()->GetFirstVeh(&firstAxisInfo);
            SetInputAxleType(QString::number(firstAxisInfo.GetConfirmedAxisGroup()));
        }

        //默认车种
        RefreshVehTypeShow();
        //此时车辆信息输入完成，判断是否打开读写器
        if (Ptr_Info->IsExitLane()) CheckCardOperation();
    }
    //通知远控车辆录入信息变化
    m_IsInputingVLP = false;
    Ptr_RemoteCtrl->RefreshVehInput();
}

/*
 * 处理车种键输入, 只响应车种键输入
 */
void CLaneState_VehInput::ProcessKeyEvent_VehTypeInput(MtcKeyPressedEvent *mtcKeyEvent)
{
    CUnionVehType vehType = (CUnionVehType)mtcKeyEvent->vehType();

    CVehInfo vehInfo;
    GetCurVehInfo(vehInfo);
    if (isCar(vehInfo.VehClass) && vehType == UVT_FarmProduct) {
        ShowErrorMessage(QString("客车不能为绿通车"));
        return;
    }
    if (Ptr_Info->IsEntryLane()) SetInputVehType(vehType);
}

int CLaneState_VehInput::ProcessKeyEvent_VehPlateInput(MtcKeyPressedEvent *mtcKeyEvent)
{
    int nRlt = 0;
    if (m_pStateUI) {
        nRlt = ((CBaseOpWidget *)m_pStateUI)->mtcKeyPressed(mtcKeyEvent);
    }

    return nRlt;
}

int CLaneState_VehInput::ProcessKeyEvent_GanTongKa()
{
    if (XEvent_None == m_OtherXVehInfo.nEventId) {
        return 0;
    }
    if (m_OtherXVehInfo.bOpType != Operate_WaitConfirm) {
        return 0;
    }
    QMutexLocker locker(&m_OtherXMt);
    int nAxisNum = m_OtherXVehInfo.nAxisNum;
    QString sMsg;
    QString sError;
    if (Ptr_Info->IsEntryLane()) {
        if (m_OtherXVehInfo.nEventId == XEvent_ConfirmAxis) {
            if (!CheckInputVCAndAxisNum(nAxisNum, sError)) {
                QString sPrompt = QString("%1,按[赣通卡]键无效").arg(sError);
                GetMainDlg()->ShowPromptMsg(sPrompt, true);
                return 0;
            }
            if (m_OtherXVehInfo.bVehType == UVT_TRUCK) {
                sMsg = QString("拖挂车[%1]%2轴,确认发卡?")
                        .arg(m_OtherXVehInfo.sVehPlate)
                        .arg(nAxisNum);
            } else if (m_OtherXVehInfo.bVehType == UVT_BigTruck) {
                sMsg =
                        QString("大件车[%1]%2轴,确认发卡").arg(m_OtherXVehInfo.sVehPlate).arg(nAxisNum);
                CVehInfo curVehInfo;
                GetCurVehInfo(curVehInfo, true);
                QString sPlate = GB2312toUnicode(curVehInfo.szVehPlate);
                if (curVehInfo.IsEmpty() ||
                        (sPlate != m_OtherXVehInfo.sVehPlate ||
                         curVehInfo.nVehPlateColor != m_OtherXVehInfo.bVlpColor)) {
                    DebugLog(QString("大件车确认继续发卡,当前车辆%1_%2与%3_%4不符")
                             .arg(sPlate)
                             .arg(curVehInfo.nVehPlateColor)
                             .arg(m_OtherXVehInfo.sVehPlate)
                             .arg(m_OtherXVehInfo.bVlpColor));
                    m_OtherXVehInfo.Clear();
                    sMsg = QString("确认继续发卡?");
                } else {
                    QString sName = GetUnionVehTypeName(curVehInfo.GBVehType);
                    sMsg = QString("%1[%2]%3轴,确认发卡")
                            .arg(sName)
                            .arg(m_OtherXVehInfo.sVehPlate)
                            .arg(nAxisNum);
                }
            } else if (m_OtherXVehInfo.bVehType == UVT_J1 || m_OtherXVehInfo.bVehType == UVT_J2) {
                sMsg = QString("集装箱车[%1]%2轴,确认继续发卡?")
                        .arg(m_OtherXVehInfo.sVehPlate)
                        .arg(nAxisNum);
            } else {
                sMsg =
                        QString("[%1]%2轴确认继续发卡?").arg(m_OtherXVehInfo.sVehPlate).arg(nAxisNum);
            }
        } else if (m_OtherXVehInfo.nEventId == XEvent_Truck1Over) {
            sMsg = QString("[%1]货1超限,确认继续发卡?").arg(m_OtherXVehInfo.sVehPlate);
        } else if (XEvent_VehPassPermit == m_OtherXVehInfo.nEventId) {
            FormLogin loginUser;
            COperInfo operInfo;
            if (loginUser.auth(operInfo)) {
                AddToPermitList(m_OtherXVehInfo.sVehPlate, operInfo);
                m_OtherXVehInfo.bOpType = Operate_Continue;
                ShowErrorMessage("天线继续交易");
                PauseOBU(0);
                return 1;
            } else {
                return 0;
            }
        }
        DebugLog(sMsg);
        if (CMessageBox::Information(sMsg)) {
            if (Operate_WaitConfirm == m_OtherXVehInfo.bOpType)
                m_OtherXVehInfo.bOpType = Operate_Continue;
            PauseOBU(0);
        }
    } else if (Operate_WaitConfirm == m_OtherXVehInfo.bOpType) {
        m_OtherXVehInfo.bOpType = Operate_Continue;
        ShowErrorMessage("天线继续交易");
        PauseOBU(0);
    }
    return 0;
}

/*
 功能：将卡片收费信息文件转换为车辆入口信息,同时校验入口数据格式是否正确,不校验入口信息是否异常
 输入参数：CardTollInfo 卡片收费信息文件  OccurTime 出口交易时间，用于计算车辆行驶时间
 输出参数： CVehEntryInfo 车辆入口信息，sError 错误信息
 返回： true-成功 false-失败
*/
bool CLaneState_VehInput::GetEntryInfoFromCardTollInfo(const QDateTime &OccurTime,
                                                       const CCardTollInfo &cardTollInfo,
                                                       CVehEntryInfo &vehEntryInfo,
                                                       qint32 &nErrorCode, QString &sError)
{
    if (!CCardFileConverter::IsValidEnFlag(cardTollInfo.bPassStatus)) {
        nErrorCode = CSpEventMgr::SpEvent_NoEntry;
        sError = QString("卡内无入口信息");
        return false;
    }
    vehEntryInfo.Clear();
    vehEntryInfo.nEntryType = Entry_ByCard;
    // vehEntryInfo.wEnNetWorkId = cardTollInfo.wNetworkId;
    vehEntryInfo.dwEnStationID = cardTollInfo.dwStaId;
    vehEntryInfo.dwExStationID = Ptr_Info->GetStationID();
    vehEntryInfo.sExStationName = Ptr_Info->GetStationName();
    vehEntryInfo.nEnLaneID = cardTollInfo.bLaneId;
    QString sLog;
    vehEntryInfo.EnTime = cardTollInfo.szPassTime;
    if (vehEntryInfo.EnTime < OccurTime) {
        vehEntryInfo.nPassTime = vehEntryInfo.EnTime.secsTo(OccurTime);
    } else {
        sError = "车辆入口时间大于当前时间！";
        sLog = QString("车辆入口时间[%1]大于当前时间[%2]")
                .arg(vehEntryInfo.EnTime.toString("yyyy-MM-dd hh:mm:ss"))
                .arg(OccurTime.toString("yyyy-MM-dd hh:mm:ss"));
        DebugLog(sLog);
        vehEntryInfo.nPassTime = 0;
    }
    vehEntryInfo.bEnLaneType = cardTollInfo.bPassStatus;
    vehEntryInfo.bEnVC = cardTollInfo.bVehClass;
    vehEntryInfo.bEnVT = cardTollInfo.bVehTollType;
    vehEntryInfo.bEnVLPC = cardTollInfo.bVehPlateColor;
    qstrncpy(vehEntryInfo.szEnVLP, cardTollInfo.szVehPlate, sizeof(vehEntryInfo.szEnVLP));

    vehEntryInfo.sEnNetWorkIdHex = cardTollInfo.sNetworkHex;
    vehEntryInfo.sEnLaneHex = cardTollInfo.sEnLaneHex;
    vehEntryInfo.sEnStationHex = cardTollInfo.sEnStationHex;
    vehEntryInfo.sGantryNumHex = cardTollInfo.sLastGantryHex;

    vehEntryInfo.dwTotalWeight = cardTollInfo.dwToltalWeight;
    if (0 == vehEntryInfo.dwTotalWeight || 0xffffff <= vehEntryInfo.dwTotalWeight) {
        vehEntryInfo.dwTotalWeight = TotalWeight_Default;
    }
    vehEntryInfo.dwWeightLimit = cardTollInfo.dwLimitWeight;

    vehEntryInfo.VehicalAxles = cardTollInfo.VehicalAxles;
    vehEntryInfo.bVehState = cardTollInfo.bVehState;
    vehEntryInfo.gantryPassTime = cardTollInfo.dwDoorFramePassTime;
    QString sEnPlate = GB2312toUnicode(vehEntryInfo.szEnVLP);

    DebugLog(QString("国标卡格式,入口网络号:%1,入口车道:%2，入口站:%3，门架:%4,车型:%5,车牌:%6_%7,"
                     "vehState:%8,enAxles:%9")
             .arg(vehEntryInfo.sEnNetWorkIdHex)
             .arg(vehEntryInfo.sEnLaneHex)
             .arg(vehEntryInfo.sEnStationHex)
             .arg(vehEntryInfo.sGantryNumHex)
             .arg(vehEntryInfo.bEnVC)
             .arg(vehEntryInfo.bEnVLPC)
             .arg(sEnPlate)
             .arg(vehEntryInfo.bVehState, 2, 16, QLatin1Char('0'))
             .arg(vehEntryInfo.VehicalAxles));

    COrgBasicInfoTable *pOrgBasicInfo =
            (COrgBasicInfoTable *)CParamFileMgr::GetParamFile(cfOrgBasicInfo);
    if (pOrgBasicInfo) {
        qint32 nProvinceId = cardTollInfo.sNetworkHex.left(2).toInt();
        QString sHex = cardTollInfo.sNetworkHex + cardTollInfo.sEnStationHex;
        COrgBasicInfo orgBasicInfo;
        bool bRlt =
                pOrgBasicInfo->QryOrgBasicInfo(nProvinceId, ORG_TYPE_STATION_GB, sHex, orgBasicInfo);
        if (!bRlt) {
            DebugLog(QString("不存在入口为%1的收费站").arg(sHex));
            nErrorCode = CSpEventMgr::SpEvent_InvalidEntry;
            sError = QString(QString("入口站[%1]错误").arg(sHex));
            return false;
        }

        vehEntryInfo.sEnGBStationId = orgBasicInfo.sId;
        vehEntryInfo.sEnStaionName = orgBasicInfo.sName;
        vehEntryInfo.sExStationName = Ptr_Info->GetStationName();
        vehEntryInfo.dwExStationID = Ptr_Info->GetStationID();

        if ((vehEntryInfo.sEnNetWorkIdHex == QString("3601")) && (cardTollInfo.dwStaId < 3600000))
            vehEntryInfo.dwEnStationID = 3600000 + cardTollInfo.dwStaId;
        else
            vehEntryInfo.dwEnStationID = cardTollInfo.dwStaId;

        vehEntryInfo.nEnLaneID = cardTollInfo.bLaneId;
        DebugLog(QString("入口站:%1,%2(%3)")
                 .arg(vehEntryInfo.dwEnStationID)
                 .arg(vehEntryInfo.sEnStaionName)
                 .arg(vehEntryInfo.sEnStationHex));

        sHex = sHex + cardTollInfo.sEnLaneHex;
        bRlt = pOrgBasicInfo->QryOrgBasicInfo(nProvinceId, ORG_TYPE_LANE_GB, sHex, orgBasicInfo);
        if (!bRlt) {
            DebugLog(QString("不存在入口为%1的车道").arg(sHex));
            sError = QString("入口车道[%1]错误").arg(sHex);
            nErrorCode = CSpEventMgr::SpEvent_InvalidEntry;
            return false;
        } else
            vehEntryInfo.sEnGBLaneId = orgBasicInfo.sId;

        if (cardTollInfo.sLastGantryHex.length() > 0) {
            bRlt = pOrgBasicInfo->QryOrgBasicInfo(nProvinceId, ORG_TYPE_GANTRY_GB,
                                                  cardTollInfo.sLastGantryHex, orgBasicInfo);
            if (bRlt) {
                vehEntryInfo.sLastGantryId = orgBasicInfo.sId;
            }
        }
    }
    vehEntryInfo.dwEnOper = cardTollInfo.dwOpId;
    vehEntryInfo.bEnShiftId = cardTollInfo.bShiftId;
    return true;
}

bool CLaneState_VehInput::QryMinFeeInfo(int nVehClass, const CVehEntryInfo &vehEntryInfo, bool bETC,
                                        CAllRoadMinFeeInfo &minFeeInfo, int &nErrorCode,
                                        QString &sError)
{
    if (!Ptr_Info->bCheckMinFee()) {
        return false;
    }

    bool bRlt = CFareCalcUnit::QryMinFeeInfo(nVehClass, vehEntryInfo, bETC, minFeeInfo, sError);
    if (!bRlt) {
        nErrorCode = CSpEventMgr::SpEvent_Other;
        return false;
    }
    return true;
    /*
    CGAllRoadFare *pAllRoadFare = (CGAllRoadFare *)CParamFileMgr::GetParamFile(cfMinFee);
    if (!pAllRoadFare) {
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sError = QString("最短路径费率参数异常");
        return false;
    }

    minFeeInfo.enProvinceId = vehEntryInfo.sEnNetWorkIdHex.left(2);
    minFeeInfo.enStationId = vehEntryInfo.sEnGBStationId;
    minFeeInfo.exProvinceId = QString("36");
    minFeeInfo.exStationId = Ptr_Info->GetGBStationId();
    minFeeInfo.bVehClass = nVehClass;  // pCurTransInfo->VehInfo.VehClass;
    minFeeInfo.nSeconds = vehEntryInfo.nPassTime;
    if (0 == minFeeInfo.nSeconds) {
        minFeeInfo.nSeconds = 60;
    }

    bool bRlt = pAllRoadFare->QueryMinFee_inThread(minFeeInfo);
    if (!bRlt) {
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sError = QString("最短路径通行费查询失败");
        return false;
    }
    return true;*/
}

bool CLaneState_VehInput::QryGreenVehInfo(const QString &sVehPlate, int nColor,
                                          CGreenCarInfo &greenInfo)
{
    CGreenCarReserveTabble *pTable = (CGreenCarReserveTabble *)CParamFileMgr::GetParamFile(cfGreen);
    if (!pTable) {
        return false;
    }

    bool bRlt = pTable->QryGreenVehInfo(sVehPlate, nColor, greenInfo);
    return bRlt;
}

bool CLaneState_VehInput::CheckOBU_Valid(int nIndex, qint32 &nErrorCode, QString &sError)
{
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(nIndex);
    if (!pCurTransInfo) return false;

    COBUBaseInfo *pOBUBaseInfo = &pCurTransInfo->OBUBaseInfo;

    CAreaCodeTable *pAreaCodeTable = (CAreaCodeTable *)CParamFileMgr::GetParamFile(cfAreaCode);
    QString sAreaName = GB2312toUnicode(pOBUBaseInfo->ContractProvider, 4);

    if (Ptr_Info->bCheckLocal() && pAreaCodeTable) {
        CAreaCode AreaCode;
        if (!pAreaCodeTable->QryOBUArea(sAreaName, AreaCode)) {
            nErrorCode = CSpEventMgr::SpEvent_NotLocalOBU;
            sError = QString("非联网区域标签[%1]").arg(sAreaName);
            pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_OBUIssuerError);
            return false;
        }
    }

    if (pCurTransInfo->IsWhiteListVeh() || pCurTransInfo->m_bEmVeh) {
        DebugLog("紧急车辆不校验obu有效时间");
        return true;
    }

    //是否启用
    QDate curDate = QDate::currentDate();
    QDateTime tmpDateTime;
    COBUVehInfo *pOBUVehInfo = &pCurTransInfo->OBUVehInfo;

    QString sVehPlate = GB2312toUnicode(pOBUVehInfo->szVehPlate);
    bool bYJVeh = CCardFileConverter::IsYJCard(sVehPlate, pOBUVehInfo->bUserType);

    if ((!bYJVeh) &&
            (!ConvertChar14ToDateTime(tmpDateTime, pCurTransInfo->OBUBaseInfo.szContractSignedDate))) {
        ErrorLog(
                    QString("OBU启用时间转换错误,%1").arg(pCurTransInfo->OBUBaseInfo.szContractSignedDate));
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sError = QString("标签启用时间异常");
        pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_OBUNotUse);
        return false;
    }

    if ((!bYJVeh) && (curDate < tmpDateTime.date())) {
        nErrorCode = CSpEventMgr::SpEvent_UnUseOBU;
        QString sDate = tmpDateTime.toString("yyyy-MM-dd");
        sError = QString("电子标签未启用[%1]").arg(sDate);
        pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_OBUNotUse);
        return false;
    }
    //是否过期
    if ((!bYJVeh) &&
            (!ConvertChar14ToDateTime(tmpDateTime, pCurTransInfo->OBUBaseInfo.szContractExpiredDate))) {
        ErrorLog(QString("OBU过期时间转换失败,%1")
                 .arg(pCurTransInfo->OBUBaseInfo.szContractExpiredDate));
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sError = QString("OBU 过期时间转换错误");
        return false;
    }

    if ((!bYJVeh) && (curDate > tmpDateTime.date()) && Ptr_Info->GetCheckOBUExpired()) {
        nErrorCode = CSpEventMgr::SpEvent_OutTimeOBU;
        QString sDate = tmpDateTime.toString("yyyy-MM-dd");
        sError = QString("电子标签过期[%1]").arg(sDate);
        pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_OBUOutTime);
        return false;
    }
    return true;
}

/**
 * @brief
 * @param
 * @return 1-黑名单车辆 2-信用黑名单车辆 3-追缴黑名单车辆 4-补费名单车辆
 */
int CLaneState_VehInput::CheckVehList(int nIndex, const QString &sVehPlate, qint32 nColor,
                                      qint32 &nErrorCode, QString &sError)
{
    //黑名单
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(nIndex);
    if (!pCurTransInfo) return 0;
    if (pCurTransInfo->IsWhiteListVeh()) {
        return 0;
    }

    CVBListTable *pVBListTable = (CVBListTable *)CParamFileMgr::GetParamFile(cfVBList);
    if (pVBListTable) {
        quint8 bType = 0, bVehClass = 0;
        if (pVBListTable->IsBlackVeh(sVehPlate, nColor, bType, bVehClass)) {
            // if(bVehClass==m_curVehInfo.VehClass){
            if (1) {
                nErrorCode = CSpEventMgr::SpEvent_BlackCar;
                sError = QString("[%1]黑名单车辆").arg(sVehPlate);

                return 1;
            }
        }
    }

    CCrediteBList *pCreditBList = (CCrediteBList *)CParamFileMgr::GetParamFile(cfCreditBList);
    if (pCreditBList) {
        QString sType;
        if (pCreditBList->QueryVehInDB(sVehPlate, nColor, sType)) {
            nErrorCode = CSpEventMgr::SpEvent_BlackCar;
            sError = QString("[%1]信用名单车辆[%2]").arg(sVehPlate).arg(sType);
            pCurTransInfo->SetGBSpEvent(GBSP_CreditGrayBlist, true);
            pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_VehBList);
            return 2;
        }
    }
    CPrePayBList *pPrePayList = (CPrePayBList *)CParamFileMgr::GetParamFile(cfPayBList);
    if (pPrePayList) {
        QString sType;
        quint32 dwMoney = 0;
        if (pPrePayList->QueryVehInDB(sVehPlate, nColor, sType, dwMoney)) {
            nErrorCode = CSpEventMgr::SpEvent_BlackCar;
            sError = QString("[%1%2]追缴名单车辆,欠费[%3]")
                    .arg(GetVehPlateColorName(nColor))
                    .arg(sVehPlate)
                    .arg(QString::number(dwMoney / 100.0, 'f', 2));
            pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_VehBList);
            return 3;
        }
    }
    
    // 检查省内追收名单（SQLite版本，2025.04新版补费）
    CProvinceDebtTable *pProvinceDebtTable = (CProvinceDebtTable *)CParamFileMgr::GetParamFile(cfProvinceDebtList);
    if (pProvinceDebtTable) {
        CProvinceDebt debtInfo;
        if (pProvinceDebtTable->IsInDebtList(sVehPlate, nColor, debtInfo)) {
            nErrorCode = CSpEventMgr::SpEvent_BlackCar;
//            sError = QString("[%1%2]省内追收名单车辆,欠费[%3]元,共%4笔,类型[%5]")
//                    .arg(GetVehPlateColorName(nColor))
//                    .arg(sVehPlate)
//                    .arg(QString::number(debtInfo.nOweFee / 100.0, 'f', 2))
//                    .arg(debtInfo.nEvasionCount)
//                    .arg(debtInfo.sBlackType);
            sError = QString("[%1%2]省内追收名单车辆,欠费[%3]元,共%4笔")
                    .arg(GetVehPlateColorName(nColor))
                    .arg(sVehPlate)
                    .arg(QString::number(debtInfo.nOweFee / 100.0, 'f', 2))
                    .arg(debtInfo.nEvasionCount);
            pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_VehBList);
            return 4; // 返回新的类型值，区分于其他名单
        }
    }
    
    return 0;
}

void CLaneState_VehInput::CompleteTransForWhiteVeh(int nIndex, bool bSaveCurTransInfo)
{
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(nIndex);
    CTransInfo *pLastTransInfo = Ptr_ETCCtrl->GetLastTransInfo(nIndex);
    if (!pCurTransInfo) return;

    if (bSaveCurTransInfo) {
        pCurTransInfo->TransTime = QDateTime::currentDateTime();
        pCurTransInfo->SaveTo(*pLastTransInfo);
    }
    CRsuOpResult opResult;
    pLastTransInfo->CompleteTrans(nIndex, TransPT_OBU, &opResult, Tr_Successed,
                                  CTransInfo::Ts_Finished);
    Ptr_ETCCtrl->CompleteTrans(Tr_Successed, nIndex);
    pCurTransInfo->ClearTransInfo();
}

bool CLaneState_VehInput::CheckAllowETCTrans(int nIndex, QString &sError, QString &sFDMsg)
{
    //
#ifdef Lane_Test
    return true;
#endif

    if (DevIndex_First == nIndex) {
        return true;
    }
    sError.clear();
    sFDMsg.clear();
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(nIndex);
    if (!pCurTransInfo) {
        sError = QString("无等待交易车辆,天线暂停交易");
        return false;
    }
    CTransInfo *pManualTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);

    if (pManualTransInfo) {
        if (pManualTransInfo->transState >= CTransInfo::Ts_IsReadingIcc) {
            sError = QString("人工处理中,天线暂停交易");
            return false;
        }
    }

    if (!Ptr_Info->bCheckExistLoop()) return true;

    QString sOpStateName;
    if (!CheckAndSetOpState(opState_None, sOpStateName)) {
        sError = QString("%1,天线暂停交易").arg(sOpStateName);
        return false;
    }
    if (Ptr_ETCCtrl->bAllowllContinuePass(false)) {
        // sError = QString("等待车辆离开, 天线暂停交易");
        return false;
    }
    CIODevStatus iostatus;
    CDeviceFactory::GetIOCard()->GetDIPortStatus(DI_LoopExist, iostatus);
    if (!iostatus.bStatus) {
#ifndef QT_DEBUG
        sError = QString("抓拍线圈无车,天线暂停交易");
        return false;
#endif
    }
    return true;
}

void CLaneState_VehInput::OnSpEventVehMessageEvent(qint32 nXEventId, quint8 bVehType,
                                                   int nPlateColor, const QString &sVehPlate,
                                                   int nAxisNum, const QString &sCertNo,
                                                   const QString &sDetail)
{
    QString sMessage;
    QString sHelpMessage;
    QString sFullPlate = QString("%1%2").arg(GetVehPlateColorName(nPlateColor)).arg(sVehPlate);
    if (Operate_info == m_OtherXVehInfo.bOpType) {
        return;
    }

    if (sVehPlate != m_OtherXVehInfo.sVehPlate) {
        DebugLog(QString("SpEvent %1,%2 车牌不符").arg(sVehPlate).arg(m_OtherXVehInfo.sVehPlate));
        return;
    }
    if (nXEventId == XEvent_Truck1Over) {
        if (sDetail.length() > 0) {
            sMessage = sDetail;
        } else {
            sMessage = QString("货1车辆超限但总重量低于1.8吨");
        }
        sHelpMessage = QString("确认后按[赣通卡]键可ETC放行车辆");
        CMessageBox::Information_Help(QString("提示"), sMessage, sHelpMessage,
                                      CMessageBox::Style_Ok, GetMainDlg(), true);
        m_OtherXMt.lock();
        m_OtherXVehInfo.bOpType = Operate_WaitConfirm;
        m_OtherXVehInfo.bOpRlt = true;
        m_OtherXMt.unlock();
        return;
    } else if (XEvent_VehPassPermit == nXEventId) {
        sMessage = QString("%1,%2夜间禁止行驶")
                .arg(sVehPlate)
                .arg(GetVehClassName(m_OtherXVehInfo.bVehClass));
        sHelpMessage = QString("确认后,如果允许放行可按【赣通卡】键");
        CMessageBox::Information_Help(QString("夜间禁止通行"), sMessage, sHelpMessage,
                                      CMessageBox::Style_Ok, GetMainDlg(), true);
        m_OtherXMt.lock();
        m_OtherXVehInfo.bOpType = Operate_WaitConfirm;
        m_OtherXVehInfo.bOpRlt = true;
        m_OtherXMt.unlock();
        ShowMessage(QString("按【赣通卡】键可继续放行车辆"));
        return;
    } else if (XEvent_U == nXEventId || XEvent_J == nXEventId) {
        if (XEvent_U == nXEventId) {
            sMessage = QString("车辆<%1> U型行驶,%2").arg(sVehPlate).arg(sDetail);
        } else {
            sMessage =
                    QString("车辆<%1>经过调头点<%2>\n请检查是否J型行驶").arg(sVehPlate).arg(sDetail);
        }
        sHelpMessage = QString("确认后,按【赣通卡】键可继续处理");
        QString sTitle;

        if (XEvent_U == nXEventId) {
            sTitle = QString("U型行驶");
        } else
            sTitle = QString("J型行驶");

        CMessageBox::Information_Help(sTitle, sMessage, sHelpMessage, CMessageBox::Style_Ok,
                                    GetMainDlg(), true);
        m_OtherXMt.lock();
        m_OtherXVehInfo.bOpType = Operate_WaitConfirm;
        m_OtherXVehInfo.bOpRlt = true;
        m_OtherXMt.unlock();
        ShowMessage(QString("按【赣通卡】键可继续放行车辆"));
        return;
    }

    sHelpMessage = QString("请确认或修改轴组后按[赣通卡]键继续天线交易");
    if (sDetail.length() > 0) {
        sMessage = sDetail;
    } else {
        // CUnionVehType
        if (UVT_TRUCK == bVehType) {
            sMessage = QString("牵引拖挂车[%1][%2轴],请确认").arg(sFullPlate).arg(nAxisNum);
            sHelpMessage = QString("请确认或修改轴组后按[赣通卡]键继续发卡");

        } else if (UVT_BigTruck == bVehType) {
            if (Ptr_Info->IsEntryLane()) {
                //入口大件只有车辆超限才会处理到这里，所以如果不是大件，也就不用处理了。
                sMessage = QString("大件运输车[%1][%2轴]\n运单号[%3]")
                        .arg(sFullPlate)
                        .arg(nAxisNum)
                        .arg(sCertNo);
                sHelpMessage = QString("确认后按[赣通卡]键继续天线交易");
                bool bRlt =
                        CMessageBox::Information_Help(QString("提示"), sMessage, sHelpMessage,
                                                      CMessageBox::Style_Ok, GetMainDlg(), true);
                m_OtherXMt.lock();
                m_OtherXVehInfo.bOpType = Operate_WaitConfirm;
                m_OtherXVehInfo.bOpRlt = true;
                m_OtherXMt.unlock();
                SetInputVehClass((CVehClass)m_OtherXVehInfo.bVehClass);
                SetInputVehPlate(m_OtherXVehInfo.sVehPlate, m_OtherXVehInfo.bVlpColor);
                SetInputVehType((CUnionVehType)m_OtherXVehInfo.bVehType);
                SetInputAxleType(QString::number(m_OtherXVehInfo.nAxleType));

                return;
            } else {
                //出口
                sMessage = QString("大件运输车[%1][%2轴]\n运单号[%3]")
                        .arg(sFullPlate)
                        .arg(nAxisNum)
                        .arg(sCertNo);
                sHelpMessage = QString("[确认键]按大件车处理,[取消]键为普通车");
                bool bRlt =
                        CMessageBox::Information_Help(QString("提示"), sMessage, sHelpMessage,
                                                      CMessageBox::Style_OkCancel, GetMainDlg(), true);
                m_OtherXMt.lock();
                m_OtherXVehInfo.bOpType = Operate_Continue;
                m_OtherXVehInfo.bOpRlt = bRlt;
                m_OtherXMt.unlock();
                return;
            }
        } else if (UVT_J1 == bVehType || UVT_J2 == bVehType) {
            sMessage = QString("集装箱车[%1][%2轴],请确认").arg(sFullPlate).arg(nAxisNum);
            sHelpMessage = QString("请确认或修改轴组后按[赣通卡]键继续天线交易");
        }
    }
    CMessageBox::Information_Help(QString("提示"), sMessage, sHelpMessage, CMessageBox::Style_Ok,
                                GetMainDlg(), true);
    m_OtherXMt.lock();
    m_OtherXVehInfo.bOpType = Operate_WaitConfirm;
    m_OtherXVehInfo.bOpRlt = true;
    m_OtherXMt.unlock();
}

void CLaneState_VehInput::OnVehStayOutTimer()
{
    StopVehStayOutTimer();
    RemoteMsgMgr::GetSingleInst()->SendCallVideoReq("1");
    return;
}

bool CLaneState_VehInput::CheckRsuPause()
{
    if (m_OtherXVehInfo.nEventId > 0) {
        if (m_OtherXVehInfo.bOpType == Operate_Pause) {
            return true;
        }
    }
    return false;
}

bool CLaneState_VehInput::InputInvoiceNo()
{
    //先输入发票代码
    FormInputInvoiceNo dlgTicket(GetMainDlg());
    //发票代码、起号、止号
    if (dlgTicket.InputInvoiceNo()) {
        quint64 batchNo, beginNo, endNo;
        dlgTicket.GetResult(batchNo, beginNo, endNo);
    } else {
        return false;
    }
    return true;
}

bool CLaneState_VehInput::CheckOtherXVehInfo(qint32 nEventId, const CVehInfo &vehInfo, int &nOpType,
                                             bool &bRlt, const CBigVehInfo *pBigVehInfo,
                                             int nAxisNum, QString &sDetail, quint32 nAxleType)
{
    QString sPlate = GB2312toUnicode(vehInfo.szVehPlate);
    if (nEventId == XEvent_None) {
        return false;
    }

    if (nEventId == XEvent_ConfirmAxis) {  //确认轴数车辆
        if (!vehInfo.bNeedConfirmAxis()) {
            m_OtherXVehInfo.Clear();
            return false;
        }
    }

    if (Operate_info == m_OtherXVehInfo.bOpType) {
        {
            QMutexLocker locker(&m_OtherXMt);
            m_OtherXVehInfo.nEventId = nEventId;
            m_OtherXVehInfo.sVehPlate = sPlate;
            m_OtherXVehInfo.bVehClass = vehInfo.VehClass;
            m_OtherXVehInfo.bVehType = vehInfo.GBVehType;
            m_OtherXVehInfo.bVlpColor = vehInfo.nVehPlateColor;
            m_OtherXVehInfo.bOpType = Operate_Pause;
            m_OtherXVehInfo.nAxisNum = nAxisNum;
            m_OtherXVehInfo.nAxleType = nAxleType;
            if (sDetail.length() > 0) m_OtherXVehInfo.sDetail = sDetail;

            if (UVT_BigTruck == vehInfo.GBVehType) {
                if (pBigVehInfo) {
                    m_OtherXVehInfo.sCertNo = pBigVehInfo->cerNo;
                    m_OtherXVehInfo.sTailePlate = pBigVehInfo->trailer_vehicle_vlp;
                    m_OtherXVehInfo.nTailVlpColor = pBigVehInfo->trailer_vehicle_vlpc;
                }
            }
            nOpType = m_OtherXVehInfo.bOpType;
            sDetail = m_OtherXVehInfo.sDetail;
            bRlt = m_OtherXVehInfo.bOpRlt;
        }
        emit NotifySpEventVehMessageEvent(nEventId, m_OtherXVehInfo.bVehType,
                                          m_OtherXVehInfo.bVlpColor, m_OtherXVehInfo.sVehPlate,
                                          nAxisNum, m_OtherXVehInfo.sCertNo, sDetail);
        return true;
    } else {
        if (m_OtherXVehInfo.sVehPlate == sPlate) {
            nOpType = m_OtherXVehInfo.bOpType;
            sDetail = m_OtherXVehInfo.sDetail;
            bRlt = m_OtherXVehInfo.bOpRlt;
            return true;
        } else {
            /*
            if(1==m_OtherXVehInfo.bOpType){
                nOpType=1;
                return true;
            }*/
            {
                QMutexLocker locker(&m_OtherXMt);
                m_OtherXVehInfo.Clear();
                m_OtherXVehInfo.nEventId = nEventId;
                m_OtherXVehInfo.sVehPlate = sPlate;
                m_OtherXVehInfo.bVehClass = vehInfo.VehClass;
                m_OtherXVehInfo.bVehType = vehInfo.GBVehType;
                m_OtherXVehInfo.bVlpColor = vehInfo.nVehPlateColor;
                m_OtherXVehInfo.nAxisNum = nAxisNum;
                m_OtherXVehInfo.nAxleType = nAxleType;
                if (UVT_BigTruck == vehInfo.GBVehType) {
                    if (pBigVehInfo) {
                        m_OtherXVehInfo.sCertNo = pBigVehInfo->cerNo;
                        m_OtherXVehInfo.sTailePlate = pBigVehInfo->trailer_vehicle_vlp;
                        m_OtherXVehInfo.nTailVlpColor = pBigVehInfo->trailer_vehicle_vlpc;
                    }
                }
                m_OtherXVehInfo.bOpType = Operate_Pause;
                nOpType = m_OtherXVehInfo.bOpType;
                bRlt = m_OtherXVehInfo.bOpRlt;
            }
            emit NotifySpEventVehMessageEvent(nEventId, m_OtherXVehInfo.bVehType,
                                              m_OtherXVehInfo.bVlpColor, m_OtherXVehInfo.sVehPlate,
                                              nAxisNum, m_OtherXVehInfo.sCertNo, sDetail);
            return true;
        }
    }

    m_OtherXVehInfo.Clear();
    return false;
}

bool CLaneState_VehInput::CheckVCAndAxis(const CVehInfo &vehInfo, int nAxisNum, bool bYj,
                                         QString &sError, QString &sFDMsg)
{
    if (0 == nAxisNum) return true;

    if (bYj) return true;

    int nAxisNumVC = GetVehAxisNumByVC(vehInfo.VehClass);
    QString sVCName = GetVehClassName(vehInfo.VehClass);
    QString sPlate = GB2312toUnicode(vehInfo.szVehPlate);

    //拖挂和集装箱都需要人工确认轴组信息
    if (vehInfo.GBVehType == UVT_TRUCK || vehInfo.GBVehType == UVT_J1 ||
            vehInfo.GBVehType == UVT_J2 || vehInfo.GBVehType == UVT_BigTruck) {
        return true;
    }

    //为避免称重错误，客车也比较车型和轴数
    if (nAxisNumVC != nAxisNum) {
        if (vehInfo.VehClass == VC_Truck6) {
            if (nAxisNum > nAxisNumVC) {
                DebugLog(QString("货6异型轴%1，允许交易").arg(nAxisNum));
                return true;
            }
        }
        sError = QString("%1车型与称重轴数%2不符").arg(sVCName).arg(nAxisNum);
        sFDMsg = QString("%1\n%2\n车型轴数不符").arg(sVCName).arg(sPlate);
        return false;
    }
    return true;
}

void CLaneState_VehInput::OnRemoteSimulateDownBar()
{
    DebugLog("接收到平板指令模拟落杆");
    Ptr_ETCCtrl->SimulateDownBar();
    GetMainDlg()->RefreshPassVehQueue();
    GetMainDlg()->RefreshTradeVehQueue();
    SetOpState(opState_None);
}

bool CLaneState_VehInput::VerifyAreaCode(CTransInfo *pTransInfo,
                                         const CProCardBasicInfo &ProCardBasicInfo,
                                         quint16 &NetWorkId, CAreaCode &AreaCode,
                                         qint32 &nErrorCode, QString &sError)
{
    if ((CARD_TYPE_STORE_CARD != ProCardBasicInfo.bType) &&
            (CARD_TYPE_TALLY_CARD != ProCardBasicInfo.bType)) {
        nErrorCode = CSpEventMgr::SpEvent_AbnormalCard;
        sError = QString("卡类型[%1]错误").arg(ProCardBasicInfo.bType);
        return false;
    }
    if (!pTransInfo) {
        DebugLog("pTransInfo is null");
        return false;
    }

    CAreaCodeTable *pAreaCodeTable = (CAreaCodeTable *)CParamFileMgr::GetParamFile(cfAreaCode);
    NetWorkId = ArCharToInt(ProCardBasicInfo.szNetworkId, 4);
    if (pAreaCodeTable) {
        bool bFinded = false;
        bool bSame = true;

        QString sOBUProvider = GB2312toUnicode(pTransInfo->OBUBaseInfo.ContractProvider, 4);
        QString sCardIssuer = GB2312toUnicode(ProCardBasicInfo.IssueOrgId, 4);

        DebugLog(QString("校验卡片:%1,%2").arg(NetWorkId).arg(sCardIssuer));
        if (pAreaCodeTable->QryAreaCode(NetWorkId, sCardIssuer, &AreaCode)) {
            bFinded = true;  //找到
            if (pTransInfo->dwOBUID > 0 && sOBUProvider.length() > 0 &&
                    AreaCode.sAreaName != sOBUProvider) {
                DebugLog(QString("区域名称不符[%1;%2]").arg(AreaCode.sAreaName).arg(sOBUProvider));
                bSame = false;
            }
        }

        if (bFinded) {
            DebugLog(QString("卡片所属区域:%1,标签对应区域:%2")
                     .arg(AreaCode.sAreaName)
                     .arg(sOBUProvider));
            if (!bSame) {
                nErrorCode = CSpEventMgr::SpEvent_NotSameOBUCard;
                sError = QString("卡片[%1]与标签[%2],所属区域不符")
                        .arg(AreaCode.sAreaName)
                        .arg(sOBUProvider);
                return false;
            }
        } else {
            nErrorCode = CSpEventMgr::SpEvent_NotLocalCard;
            sError = QString("非本区域卡片[%1]").arg(NetWorkId);
            return false;
        }
        return true;
    }
    return false;
}
bool CLaneState_VehInput::VerifyProCardBasicInfo_Light(int nIndex, CTransInfo *pTransInfo,
                                                       const CProCardBasicInfo &ProCardBasicInfo,
                                                       bool bEmVeh, CVehInfo &vehInfo,
                                                       qint32 &nErrorCode, QString &sError)
{
    quint8 bVehClass = ProCardBasicInfo.bVehClass;
    bool bYJVeh = CCardFileConverter::IsYJCard(ProCardBasicInfo);

    if (!bEmVeh) {
        if (Ptr_Info->bETCStopTruck() || DevIndex_First == nIndex) {
            if ((bVehClass > VC_Truck1 && bVehClass != VC_YJ1) && (!bYJVeh)) {
                // if((bVehClass>VC_Truck) && (!bYJVeh)){
                nErrorCode = CSpEventMgr::SpEvent_TruckStopPass;
                if (DevIndex_First == nIndex) {
                    sError = QString("货车请前方处理");
                } else {
                    sError = QString("货车请转人工处理");
                }
                return false;
            }

            if (bVehClass == VC_Truck1 && ProCardBasicInfo.Is40Card()) {
                nErrorCode = CSpEventMgr::SpEvent_TruckStopPass;
                // sError = QString("货车请走混合车道");
                if (DevIndex_First == nIndex) {
                    sError = QString("货车请前方处理");
                } else {
                    sError = QString("货车请转人工处理");
                }
                DebugLog(QString("卡版本4.0货1走混合车道"));
                return false;
            }
            if (ProCardBasicInfo.bUserType == 0x1B) {
                nErrorCode = CSpEventMgr::SpEvent_TruckStopPass;
                // sError = QString("牵引车请走混合车道");
                if (DevIndex_First == nIndex) {
                    sError = QString("牵引车请前方处理");
                } else {
                    sError = QString("牵引车请转人工处理");
                }
                return false;
            }

            if (ProCardBasicInfo.bUserType == 0x18) {
                nErrorCode = CSpEventMgr::SpEvent_TruckStopPass;
                // sError = QString("集装箱车辆请走混合车道");
                if (DevIndex_First == nIndex) {
                    sError = QString("集装箱请前方处理");
                } else {
                    sError = QString("集装箱请转人工处理");
                }
                return false;
            }
        }
    }

    quint16 wNetWorkId = 0;
    CAreaCode AreaCode;
    bool bVerifyAreaCode =
            VerifyAreaCode(pTransInfo, ProCardBasicInfo, wNetWorkId, AreaCode, nErrorCode, sError);
    if (!bVerifyAreaCode && Ptr_Info->bCheckLocal()) {
        DebugLog(QString("校验卡片区域编码失败[%1],[%2]")
                 .arg(bVerifyAreaCode)
                 .arg(Ptr_Info->bCheckLocal()));
        pTransInfo->SetOncePaySpEvent(OncePay_Sp_CardIssuerError);
        return false;
    }

    if (pTransInfo->IsWhiteListVeh()) {
        return true;
    }

    //判断卡片过期
    QDate sDate = QDate::currentDate();
    QDateTime useTime, expiredTime;

    if (!Ptr_Info->bCheckVehInfo()) {
        return true;
    }

    //    qint32 nVehPlateLen =
    //      qstrnlen(ProCardBasicInfo.szVehPlate, sizeof ProCardBasicInfo.szVehPlate - 1);
    QString sVehPlateInCard = GB2312toUnicode(ProCardBasicInfo.szVehPlate).trimmed();
    QString sVehPlate = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate).trimmed();

    if (1) {
        // QString sVehPlateInCard = GB2312toUnicode(ProCardBasicInfo.szVehPlate).trimmed();
        // QString sVehPlate = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate).trimmed();

        if (sVehPlateInCard != sVehPlate) {
            //车卡不符
            nErrorCode = CSpEventMgr::SpEvent_NotSelfCard;
            sError =
                    QString("非本车卡,卡内车牌[%1]实际车牌[%2]").arg(sVehPlateInCard).arg(sVehPlate);
            pTransInfo->SetOncePaySpEvent(OncePay_Sp_VLPDiff);
            return false;
        }

        if (1) {
            /*
            if((ProCardBasicInfo.bVehPlateColor!=0xFF) && (ProCardBasicInfo.bVehPlateColor
            !=vehInfo.nVehPlateColor))
            {
                ErrorLog(QString("车卡绑定车牌颜色不符,卡内车牌颜色:%1,OBU内车牌颜色:%2")
                         .arg(ProCardBasicInfo.bVehPlateColor)
                         .arg(m_curVehInfo.nVehPlateColor));
                nErrorCode =CSpEventMgr::SpEvent_NotSelfCard;
                sError=QString("非本车卡,车牌颜色不符[%1][%2]").arg(ProCardBasicInfo.bVehPlateColor).arg(m_curVehInfo.nVehPlateColor);
                return false;
            }*/

            if (ProCardBasicInfo.bVehClass > 0 && ProCardBasicInfo.bVehClass != vehInfo.VehClass) {
                if (!bYJVeh) {
                    nErrorCode = CSpEventMgr::SpEvent_NotSelfCard;
                    sError = QString("非本车卡,车型不符卡内车型[%1]出口车型[%2]")
                            .arg(ProCardBasicInfo.bVehClass)
                            .arg(vehInfo.VehClass);
                    DebugLog(sError);
                    // return false;
                } else {
                    sError = QString("应急车非本车卡,车型不符[%1][%2]")
                            .arg(ProCardBasicInfo.bVehClass)
                            .arg(vehInfo.VehClass);
                    DebugLog(sError);
                }
            }
        }
    }

    bool bCheckTime = !(bYJVeh || pTransInfo->m_bEmVeh);
    if (bCheckTime) {
        if (ConvertChar14ToDateTime(useTime, ProCardBasicInfo.szStartTime)) {
            if (sDate < useTime.date()) {
                DebugLog(QString("卡片启用时间:%1").arg(useTime.toString("yyyy-MM-dd")));
                if (Ptr_Info->bCheckOutTime()) {
                    nErrorCode = CSpEventMgr::SpEvent_UnUseCard;
                    sError = QString("尚未启用卡片[%1]").arg(useTime.toString("yyyy-MM-dd"));
                    pTransInfo->SetOncePaySpEvent(OncePay_Sp_CardNotUse);
                    return false;
                }
            }
        }

        if (ConvertChar14ToDateTime(expiredTime, ProCardBasicInfo.szExpireTime)) {
            DebugLog(QString("卡片过期时间:%1").arg(expiredTime.toString("yyyy-MM-dd")));
            if ((Ptr_Info->bCheckOutTime()) && sDate > expiredTime.date()) {
                nErrorCode = CSpEventMgr::SpEvent_OutTimeCard;
                sError = QString("过期卡[%1]").arg(expiredTime.toString("yyyy-MM-dd"));
                pTransInfo->SetOncePaySpEvent(OncePay_Sp_CardOutTime);
                return false;
            }
        }
    }

    return true;
}

void CLaneState_VehInput::DisplayTransError_CPC(int nIndex, const QString &sError,
                                                const QString &sFDMsg, bool bAlarm)
{
    this->ShowMessage(sError);
    DebugLog(sError);
    if (sFDMsg.length() > 0) {
        CDeviceFactory::FareDisplay_ShowHelpMsg(nIndex, sFDMsg);
        if (bAlarm) {
            CDeviceFactory::StartAlarm(nIndex, 6000);
        }
    }
    if (bAlarm) {
        RemoteMsgMgr::GetSingleInst()->SendLaneReportEventReq(0, CSpEventMgr::SpEvent_Other,
                                                              sError);
    }
    return;
}

void CLaneState_VehInput::CheckCardOperation()
{
    if (IfInputVehInfoFinished()) {
        //如果已经输入完成，打开卡读写器
        CCardReader::StartCardDetection(this);
    } else {
        CCardReader::CancelCardDetection();
    }
}

void CLaneState_VehInput::ClearTransInfo_OnFrm()
{
    bool bClearTrans = true;
    if (Ptr_Info->IsExitLane()) {
        CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
        if (pCurTransInfo) {
            if (pCurTransInfo->hasWriteCard_Exit()) {
                bClearTrans = false;
            }
        }
    }

    GetMainDlg()->Emit_ClearVehInfoFrm(bClearTrans);
    ShowMessage(QString("请输入车辆信息"));
    CCardReader::CancelCardDetection();
}

bool CLaneState_VehInput::ProcessKeyReprint() { return false; }

void CLaneState_VehInput::DoOhterFreeVeh(CUnionVehType vehType)
{
    //选择车种
    if (vehType == UVT_Reverse) {
        if (Ptr_Info->IsEntryLane()) DoQuanFanCheEntry();
        return;
    } else if (UVT_Holiday == vehType) {
        //入口节假日免费
        if (Ptr_Info->IsEntryLane()) {
            DoHolidyFreeEntry();
        }

        return;
    } else {
        SetInputVehType(vehType);
    }
    return;
}

bool CLaneState_VehInput::DoHolidyFreeEntry() { return false; }

bool CLaneState_VehInput::DoQuanFanCheEntry() { return false; }

void CLaneState_VehInput::FillAutoVLPInfo_FrontETC()
{
    if (!Ptr_Info->bHaveFrontDev()) {
        return;
    }
    DebugLog("车辆离开存在线圈");  //实际车道处理，车辆压上后线圈比离开存在线圈先触发
    CTransInfo *pFirstVeh = Ptr_ETCCtrl->RemoveTransInfoFromQue(true, false);
    CTransInfo transInfo = *pFirstVeh;
    if (pFirstVeh) {
        DebugLog(QString("车辆离开存在线圈,当前队列天线id:%1").arg(transInfo.m_nRsuIndex));
        if (DevIndex_First == transInfo.m_nRsuIndex && transInfo.bTransOk()) {
            if (transInfo.AutoRegInfo.id.isEmpty()) {
                bool bRlt = Ptr_ETCCtrl->GetETCAutoRegInfo(DevIndex_Second, pFirstVeh->AutoRegInfo,
                                                           true, false);
                if (bRlt) {
                    DebugLog(QString("前天线车辆离开存在线圈,获取车牌识别结果,Id:%1,palte:%2")
                             .arg(pFirstVeh->AutoRegInfo.id)
                             .arg(pFirstVeh->AutoRegInfo.sAutoVehPlate));

                    //清空车辆信息
                    this->ClearVehInfo(true);

                } else {
                    ErrorLog("前天线车辆离开存在线圈,获取车牌识别结果失败");
                }
            } else {
                DebugLog(QString("车辆离开存在线圈,前天线车辆抓拍id:%1,vlp:%2")
                         .arg(pFirstVeh->AutoRegInfo.id)
                         .arg(pFirstVeh->AutoRegInfo.sAutoVehPlate));
            }
        }
    } else {
        DebugLog(QString("车辆离开存在线圈,过车队列为空"));
    }
    return;
}

void CLaneState_VehInput::NotifyFrontRsuHasTrans()
{
    if (!Ptr_Info->bHaveFrontDev()) return;

    QString sPlate;
    if (Ptr_ETCCtrl->CheckTheFirstVehInQueIsFrontRsu(sPlate)) {
        QString sInfo;
        if (sPlate.length() > 0)
            sInfo = QString("%1\nETC 交易成功").arg(sPlate);
        else {
            sInfo = QString("ETC 交易成功");
        }
        DebugLog(QString("前天线交易:%1").arg(sInfo));
        /*
        VcrResult vcrResult;
        VCRDev *pDev = CDeviceFactory::GetVCRDev();
        if (pDev) {
            bool bRlt = pDev->GetFistVcrResult(vcrResult);
            if (bRlt && vcrResult.sPlate == sPlate) {
                DebugLog(QString("前天线ETC车辆%1,删除车型识别队列首辆车").arg(sPlate));
            }
        }
        pDev->RemoveFirstCar();
        */
        CDeviceFactory::FareDisplay_ShowHelpMsg(DevIndex_Second, sInfo);
    }
    return;
}

void CLaneState_VehInput::UpdateVehInfo_First(CVehInfo &vehInfo, int nAxleNums)
{
    if (vehInfo.nVehClassWay >= VehClassway_Input) return;

    if (nAxleNums >= 5) {
        vehInfo.VehClass = vehInfo.AutoVehClass;
        vehInfo.PVehClass = vehInfo.VehClass;
        vehInfo.nVehClassWay = VehClassWay_Auto;
        DebugLog(QString("设置车型,设备,车型:%1,轴数:%2").arg(vehInfo.VehClass).arg(nAxleNums));
        return;
    }

    if (vehInfo.AutoVehClass == vehInfo.qryVehClass) {
        vehInfo.VehClass = vehInfo.AutoVehClass;
        vehInfo.nVehClassWay = VehClassWay_Auto;
        vehInfo.PVehClass = vehInfo.VehClass;
        DebugLog(QString("设置车型,设备与车型库相同，车型:%1").arg(vehInfo.VehClass));
    } else {
        if (vehInfo.qryVehClass > VC_None && vehInfo.nScore >= Ptr_Info->GetVehLibScore(Ptr_Info->IsEntryLane()?1:2)) {
            vehInfo.VehClass = vehInfo.qryVehClass;
            vehInfo.PVehClass = vehInfo.VehClass;
            vehInfo.nVehClassWay = VehClassWay_Lib;
            DebugLog(QString("设置车型,车型库，车型:%1,score:%2")
                     .arg(vehInfo.VehClass)
                     .arg(vehInfo.nScore));
        } else {
            vehInfo.VehClass = vehInfo.AutoVehClass;
            vehInfo.PVehClass = vehInfo.AutoVehClass;
            vehInfo.nVehClassWay = VehClassWay_Auto;
            DebugLog(QString("设置车型,设备，车型:%1").arg(vehInfo.VehClass));
        }
    }
    return;
}

void CLaneState_VehInput::UpdateVehInfo_ByEntryInfo(CVehInfo &vehInfo, int nEnVC)
{
    if (vehInfo.nVehClassWay >= VehClassway_Input) return;
    if (vehInfo.AutoVehClass > VC_None && vehInfo.AutoVehClass == nEnVC) {
        vehInfo.VehClass = vehInfo.AutoVehClass;
        vehInfo.nVehClassWay = VehClassWay_Auto;
        vehInfo.PVehClass = vehInfo.VehClass;
        DebugLog(QString("设置车型,设备与入口相同，车型:%1").arg(vehInfo.VehClass));
        return;
    }
    if (vehInfo.qryVehClass > VC_None && vehInfo.qryVehClass == nEnVC) {
        vehInfo.VehClass = vehInfo.qryVehClass;
        vehInfo.nVehClassWay = VehClassWay_Lib;
        vehInfo.PVehClass = vehInfo.VehClass;
        DebugLog(QString("设置车型,车型库与入口相同，车型:%1").arg(vehInfo.VehClass));
    }
    //此次更新在First 之后，因此不必重新设置都不相同时的车型
    return;
}

bool CLaneState_VehInput::CheckAndRepassFrontETCVeh(const CTransInfo &transInfo)
{
    if (!Ptr_Info->bHaveFrontDev()) return false;
    if (DevIndex_First == transInfo.m_nRsuIndex) {
        QString sVehPlate = GB2312toUnicode(transInfo.VehInfo.szVehPlate);
        VCRDev *pVcr = CDeviceFactory::GetVCRDev();
        VcrResult vcrResult;
        if (pVcr) {
            bool bRlt = pVcr->GetFistVcrResult(vcrResult);
            if (bRlt) {
                DebugLog(QString("取车型识别首辆车:%1").arg(vcrResult.sPlate));
            }
        }
        CAutoRegInfo auRegInfo;
        bool bAutoVlp = Ptr_ETCCtrl->GetETCAutoRegInfo(1, auRegInfo, false);
        QString sAutoVlp;
        if (bAutoVlp) sAutoVlp = auRegInfo.sAutoVehPlate;

        if (vcrResult.sPlate == sVehPlate || sAutoVlp == sVehPlate) {
            DebugLog(QString("后天线检测到已交易车辆%1,车型队列首辆车:%2,车牌识别结果:%3")
                     .arg(sVehPlate)
                     .arg(vcrResult.sPlate)
                     .arg(sAutoVlp));

            CTransInfo firstTransInfo;
            if (Ptr_ETCCtrl->bAbnormalVehOfTheFirstVehInQueue(firstTransInfo)) {
                DebugLog(QString("车辆队列首辆车为异常车,删除异常车"));
                CTransInfo *pTransInfo = Ptr_ETCCtrl->RemoveTransInfoFromQue(true, true);
                if (pTransInfo) delete pTransInfo;
                Ptr_ETCCtrl->SetAllowPass();
                return true;
            }
        }
    }
    return false;
}

/**
 * @brief 车型与轴数校验函数(新版)
 *
 * 根据需求实现了如下功能：
 * 1. 对非ETC普通货车和ETC普通货车增加地磅轴组校验功能
 * 2. 根据不同车道类型实现不同的校验逻辑
 * 3. 使用车型库管理器查询车型库轴数并准备轴组信息输出至交易流水表
 *
 * 注意：为保持与原CheckVCAndAxis函数接口一致，一些额外的信息需要通过其他方式传递
 *
 * @param vehInfo 车辆信息
 * @param nAxisNum 地磅测得的轴数
 * @param bYj 是否为应急车辆
 * @param sError 错误信息
 * @param sFDMsg 错误提示信息
 * @return 校验是否通过
 */
bool CLaneState_VehInput::CheckVCAndAxisNew(const CVehInfo &vehInfo, int nAxisNum, bool bYj,
                                            QString &sError, QString &sFDMsg, bool bEtcTrade)
{
    // 如果轴数为0，直接返回通过
    if (0 == nAxisNum) return true;

    // 应急车辆不检查轴数
    if (bYj) return true;

    // 获取OBU车型对应的标准轴数
    int nAxisNumVC = GetVehAxisNumByVC(vehInfo.VehClass);
    QString sVCName = GetVehClassName(vehInfo.VehClass);
    QString sPlate = GB2312toUnicode(vehInfo.szVehPlate);

    // 实际计费车型默认为当前车型
    QString sCalVehicleType = QString::number(vehInfo.VehClass);

    // 特殊处理：牵引拖挂车和集装箱都需要人工确认轴组信息
    bool bNeedManualCheck = false;
    if (vehInfo.GBVehType == UVT_TRUCK || vehInfo.GBVehType == UVT_J1 ||
            vehInfo.GBVehType == UVT_J2 || vehInfo.GBVehType == UVT_BigTruck) {
        bNeedManualCheck = true;
    }

    // 从当前上下文获取其他相关信息
    int nVehTypeDeviceAxisNum = 0;
    // 通过车型识别设备的车型反算轴数（要求4）
    if (vehInfo.AutoVehClass > 0) {
        nVehTypeDeviceAxisNum = GetVehAxisNumByVC(vehInfo.AutoVehClass);
    }
    // 使用传入的bEtcTrade参数判断是否为ETC交易
    bool bETC = bEtcTrade;
    
    // 根据bCardMgrEnabled判断车道类型（要求1和2）
    bool bSelfServiceLane = Ptr_Info->bCardMgrEnabled();
    // 不使用LANE_TYPE枚举，直接使用布尔值判断车道类型

    // 使用车型库管理器查询车型库信息（要求5）
    CVehInfo_VehLib vehLibInfo;
    int nVehTypeDbAxisNum = 0;
    QString sVehTypeDbAxisType;
    QString sVehicleTypeDbInfo;

    // 先尝试从缓存获取信息，如果失败则发起查询
    bool bDbQueryResult = CVehTypeLibMgr::GetVehTypeLibMgr()->QueryVehFromCache(sPlate, vehInfo.nVehPlateColor, vehLibInfo);
    if (!bDbQueryResult) {
        // 缓存查询失败，尝试完整查询
        bDbQueryResult = CVehTypeLibMgr::GetVehTypeLibMgr()->GetVehResult(sPlate, vehInfo.nVehPlateColor, vehLibInfo);
    }

    // 从车型库查询结果中提取轴数信息
    if (bDbQueryResult) {
        nVehTypeDbAxisNum = vehLibInfo.axleNum;
        sVehTypeDbAxisType = ""; // 轴型信息不可用，设置为空字符串
    }

    // 组装车型库信息字符串：车型|车种|轴数|轴型
    if (bDbQueryResult) {
        sVehicleTypeDbInfo = QString("%1|%2|%3|%4").arg(vehInfo.VehClass)
                .arg(vehInfo.GBVehType)
                .arg(nVehTypeDbAxisNum)
                .arg(sVehTypeDbAxisType);
    } else {
        // 车型库查询失败，填充空格
        sVehicleTypeDbInfo = QString("%1|%2| | ").arg(vehInfo.VehClass)
                .arg(vehInfo.GBVehType);
    }

    // 保存车型库信息到当前交易数据中，用于后续记录到流水表
    //SaveVehicleTypeInfoToCurrentTransaction(sVehicleTypeDbInfo, sCalVehicleType);

    // 如果车型识别设备轴数为0或车型库轴数为0，则采用原来CheckVCAndAxis函数的逻辑（要求6）
    if ((nVehTypeDeviceAxisNum == 0 || nVehTypeDbAxisNum == 0) && !bNeedManualCheck) {
        DebugLog(QString("车型识别设备轴数或车型库轴数为0，采用原CheckVCAndAxis逻辑"));
        // 非货车或轴数符合，直接返回true
        if (!(vehInfo.VehClass >= VC_Truck1 && vehInfo.VehClass <= VC_Truck6)) {
            if (nAxisNumVC != nAxisNum) {
                sError = QString("%1车型与称重轴数%2不符").arg(sVCName).arg(nAxisNum);
                sFDMsg = QString("%1\n%2\n车型轴数不符").arg(sVCName).arg(sPlate);
                return false;
            }
            return true;
        }
        
        // 对于拖挂和集装箱货车，需要人工确认轴组信息
        if (bNeedManualCheck) {
            return true;
        }

        // 普通货车检查
        if (nAxisNumVC != nAxisNum) {
            if (vehInfo.VehClass == VC_Truck6) {
                if (nAxisNum > nAxisNumVC) {
                    DebugLog(QString("货6异型轴%1，允许交易").arg(nAxisNum));
                    return true;
                }
            }
            sError = QString("%1车型与称重轴数%2不符").arg(sVCName).arg(nAxisNum);
            sFDMsg = QString("%1\n%2\n车型轴数不符").arg(sVCName).arg(sPlate);
            return false;
        }
        return true;
    }

    // 3、ETC牵引拖挂车特殊处理
    if (bETC && bNeedManualCheck) {
        // 入口ETC/MTC混合车道，ETC牵引拖挂车，由收费员确认最终轴数
        sFDMsg = QString("%1\n%2\n车型库轴数：%3\n请确认最终轴数")
                     .arg(sVCName)
                     .arg(sPlate)
                     .arg(nVehTypeDbAxisNum);
        return true;
    }

    // 根据不同车型和车道类型进行校验
    if (vehInfo.VehClass >= VC_Truck1 && vehInfo.VehClass <= VC_Truck6) {
        // 普通货车处理
        
        // 判断是入口ETC/MTC混合车道还是自助车道
        if (!bSelfServiceLane) {
            // 混合车道
            if (bETC) {
                // ①入口ETC/MTC混合车道，ETC普通货车
                // 校验地磅上送轴数a与OBU发行车型对应轴数b，ab一致
                if (nAxisNum != nAxisNumVC) {
                    sError = QString("%1车型与称重轴数%2不符，请按<改轴>键修改")
                                 .arg(sVCName)
                                 .arg(nAxisNum);
                    sFDMsg = QString("%1\n%2\n车型轴数不符\n按<改轴>键修改后\n按<赣通卡>键继续")
                                 .arg(sVCName)
                                 .arg(sPlate);
                    return false;
                }
            } else {
                // ①入口ETC/MTC混合车道，非ETC普通货车
                // 维持原来判断逻辑
                if (nAxisNumVC != nAxisNum) {
                    if (vehInfo.VehClass == VC_Truck6) {
                        if (nAxisNum > nAxisNumVC) {
                            DebugLog(QString("货6异型轴%1，允许交易").arg(nAxisNum));
                            return true;
                        }
                    }
                    sError = QString("%1车型与称重轴数%2不符").arg(sVCName).arg(nAxisNum);
                    sFDMsg = QString("%1\n%2\n车型轴数不符").arg(sVCName).arg(sPlate);
                    return false;
                }
            }
        } else {
            // 自助车道
            if (bETC) {
                // ②入口ETC/MTC自助车道，ETC普通货车
                // 校验地磅轴数a与OBU发行车型对应轴数b，ab一致，且不小于c的轴数
                if (nAxisNum != nAxisNumVC || (bDbQueryResult && nAxisNum < nVehTypeDbAxisNum)) {
                    sError = QString("%1车型与称重轴数%2不符或小于车型库轴数%3")
                                 .arg(sVCName)
                                 .arg(nAxisNum)
                                 .arg(nVehTypeDbAxisNum);
                    sFDMsg = QString("%1\n%2\n需收费员核实").arg(sVCName).arg(sPlate);
                    return false;
                }
            } else {
                // ②入口ETC/MTC自助车道，非ETC普通货车
                // 校验地磅轴数a与车型识别设备轴数b、车型库轴数c，ab一致，且不小于c的轴数
                if (nAxisNum != nVehTypeDeviceAxisNum ||
                    (bDbQueryResult && nAxisNum < nVehTypeDbAxisNum)) {
                    sError = QString("称重轴数%1与识别轴数%2不符或小于车型库轴数%3")
                                 .arg(nAxisNum)
                                 .arg(nVehTypeDeviceAxisNum)
                                 .arg(nVehTypeDbAxisNum);
                    sFDMsg = QString("%1\n%2\n需收费员核实").arg(sVCName).arg(sPlate);
                    return false;
                }
            }
        }
    } else {
        // 非货车(如客车)，保持原有逻辑
        if (nAxisNumVC != nAxisNum) {
            sError = QString("%1车型与称重轴数%2不符").arg(sVCName).arg(nAxisNum);
            sFDMsg = QString("%1\n%2\n车型轴数不符").arg(sVCName).arg(sPlate);
            return false;
        }
    }

    // 处理轴数大于6轴的货车，按照货六填写
    if (vehInfo.VehClass >= VC_Truck1 && vehInfo.VehClass <= VC_Truck6) {
        if (nAxisNum > 6) {
            sCalVehicleType = QString::number(VC_Truck6);
        }
    }

    return true;
}

bool CLaneState_VehInput::HandleBigVehSelection(const QString &sVehPlate, int nVlpColor, CBigVehInfo &bigVehInfo)
{
    // 获取大件车参数文件
    CBigVehList *pBigVehList = (CBigVehList *)CParamFileMgr::GetParamFile(cfBigVehList);
    if (!pBigVehList) {
        DebugLog("无法获取大件车参数文件");
        return false;
    }
    
    // 使用新的多结果查询函数
    QList<CBigVehInfo> bigVehInfoList;
    BigVehQueryResult result = pBigVehList->QueryBigVehInfoList(sVehPlate, nVlpColor, bigVehInfoList);
    
        switch (result) {
        case BigVehQuery_NotFound:
            {
                // 未找到大件车记录
                DebugLog(QString("未找到车牌为 %1 的大件车记录").arg(sVehPlate));
                return false;
            }
            
        case BigVehQuery_SingleFound:
            {
                // 找到单个记录，直接使用
                bigVehInfo = bigVehInfoList.first();
                DebugLog(QString("找到单个大件车记录，证书号:%1").arg(bigVehInfo.cerNo));
                return true;
            }
            
        case BigVehQuery_MultiFound:
            {
                // 找到多个记录，需要用户选择
                DebugLog(QString("找到 %1 个大件车记录，弹出选择对话框").arg(bigVehInfoList.size()));
                
                // 创建选择对话框
                FormSelectBigVeh selectDialog(GetMainDlg());

                CBigVehInfo selectedInfo;
                
                // 显示选择对话框
                if (selectDialog.ShowSelectDialog(bigVehInfoList, selectedInfo)) {
                    // 用户确认选择
                    bigVehInfo = selectedInfo;
                    DebugLog(QString("用户选择了大件车记录，证书号:%1").arg(bigVehInfo.cerNo));
                    return true;
                } else {
                    // 用户取消选择
                    DebugLog("用户取消了大件车选择");
                    return false;
                }
            }
            
        default:
            {
                DebugLog("大件车查询返回未知结果");
                return false;
            }
    }
}
