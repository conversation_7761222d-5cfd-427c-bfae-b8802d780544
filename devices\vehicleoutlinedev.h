#ifndef VEHICLEOUTLINEDEV_H
#define VEHICLEOUTLINEDEV_H

#include "abstractdev.h"
#include "device.h"
#include <QLibrary>
#include <QMutex>
#include <QTimer>
#include <QDateTime>

/**
 * @brief 车辆外轮廓尺寸数据结构
 */
struct VehicleOutlineData
{
    int length;     // 长度，单位：cm
    int width;      // 宽度，单位：cm  
    int height;     // 高度，单位：cm
    QDateTime timestamp; // 检测时间
    
    VehicleOutlineData()
    {
        length = 0;
        width = 0;
        height = 0;
        timestamp = QDateTime::currentDateTime();
    }
    
    VehicleOutlineData(int len, int w, int h)
    {
        length = len;
        width = w;
        height = h;
        timestamp = QDateTime::currentDateTime();
    }
    
    bool isValid() const
    {
        return length > 0 && width > 0 && height > 0;
    }
};

/**
 * @brief 车辆外轮廓尺寸检测仪设备类
 * 
 * 该类封装了车辆外轮廓尺寸检测仪的接口，提供设备初始化、
 * 数据获取、设备关闭等功能。
 */
class CVehicleOutlineDev : public CAbstractDev
{
    Q_OBJECT

public:
    explicit CVehicleOutlineDev(QObject *parent = nullptr);
    virtual ~CVehicleOutlineDev();

    // 继承自CAbstractDev的虚函数
    virtual bool StartDev() override;
    virtual void CloseDev() override;
    virtual bool LoadDriver() override;
    virtual void ReleaseDriver();

    /**
     * @brief 获取识别结果
     * @param index 获取队列数据序列号，0第一条，1第二条...
     * @param data 输出参数，存储获取到的车辆外轮廓尺寸数据
     * @return true-成功，false-失败
     */
    bool GetVehicleOutlineInfo(int index, VehicleOutlineData &data);

    /**
     * @brief 获取最新的识别结果
     * @param data 输出参数，存储获取到的车辆外轮廓尺寸数据
     * @return true-成功，false-失败
     */
    bool GetLatestVehicleOutlineInfo(VehicleOutlineData &data);

    /**
     * @brief 删除识别结果
     * @param index 删除车辆队列数据序列号，0第一条，1第二条...
     * @return true-成功，false-失败
     */
    bool DeleteVehicleOutlineInfo(int index);

    /**
     * @brief 获取队列长度
     * @return 已经识别车辆外轮廓尺寸数据的数量
     */
    int GetQueueLength();

    /**
     * @brief 强制收尾获取识别结果
     * @param data 输出参数，存储获取到的车辆外轮廓尺寸数据
     * @return true-成功，false-失败
     */
    bool ForceGetVehicleOutlineInfo(VehicleOutlineData &data);

    /**
     * @brief 清空所有缓存的识别结果
     */
    void ClearAllResults();

    /**
     * @brief 检查设备连接状态
     * @return true-连接正常，false-连接异常
     */
    bool CheckDeviceStatus();

signals:
    /**
     * @brief 接收到新的车辆外轮廓尺寸数据信号
     * @param data 车辆外轮廓尺寸数据
     */
    void OnVehicleOutlineData(const VehicleOutlineData &data);

private slots:
    /**
     * @brief 定时检查设备状态
     */
    void OnCheckDeviceStatus();

private:
    // 动态库函数指针类型定义
    typedef int (__stdcall *Func_InitDev)(const char* lpszConnPara1, const char* lpszConnPara2);
    typedef int (__stdcall *Func_GetVehInfo)(int Index, int *length, int *width, int *height);
    typedef void (__stdcall *Func_CloseDev)();
    typedef int (__stdcall *Func_DelVehInfo)(int Index);
    typedef int (__stdcall *Func_GetQueueLength)();
    typedef int (__stdcall *Func_FocusGetVehInfo)(int *length, int *width, int *height);

    // 动态库函数指针
    Func_InitDev m_pInitDev;
    Func_GetVehInfo m_pGetVehInfo;
    Func_CloseDev m_pCloseDev;
    Func_DelVehInfo m_pDelVehInfo;
    Func_GetQueueLength m_pGetQueueLength;
    Func_FocusGetVehInfo m_pFocusGetVehInfo;

    // 静态成员变量
    static bool m_bDriverLoaded;        // 驱动是否已加载
    static QLibrary m_hLibModule;       // 动态库句柄

    // 成员变量
    QTimer *m_pStatusTimer;             // 状态检查定时器
    QMutex m_dataMutex;                 // 数据访问互斥锁
    QList<VehicleOutlineData> m_dataCache; // 本地数据缓存
    QDateTime m_lastCheckTime;          // 上次检查时间
    bool m_bDeviceConnected;            // 设备连接状态

    /**
     * @brief 从动态库加载函数
     * @param funcName 函数名
     * @return 函数指针，失败返回nullptr
     */
    void* LoadFunction(const char* funcName);

    /**
     * @brief 更新本地数据缓存
     */
    void UpdateDataCache();

    /**
     * @brief 检查并发送新数据信号
     */
    void CheckAndEmitNewData();
};

#endif // VEHICLEOUTLINEDEV_H
