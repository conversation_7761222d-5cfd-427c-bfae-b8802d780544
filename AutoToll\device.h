#ifndef DEVICE_H
#define DEVICE_H

enum CDevID
{
    DEV_None,
    DEV_VideoCard,      //视频卡
    DEV_VPR,            //车牌识别
    DEV_VPR1,           //后车牌识别
    DEV_FareDisplayer,  //费显
    DEV_IOCard,         // IO卡
    DEV_VCR,            //车型识别
    DEV_AutoMachine,    //收卡机
    DEV_POS,            // POS机
    DEV_Printf,         //打印机
    DEV_Weight,
    DEV_RSU,  //天线
    DEV_RSU1,
    DEV_VDM,               //字符叠加器
    DEV_ETCFare,           // ETC前费显
    DEV_MTCFare,           // MTC小费显
    DEV_LED,               // ETC LED显示屏
    DEV_NETWORK,           //网络
    DEV_CardReader,        //卡读写器
    DEV_CardReader1,       //卡读写器上
    DEV_CardReader2,       //卡读写器下
    DEV_AutoExTollScreen,  //自助收费外屏
    DEV_MobilePay,         //移动支付
    DEV_TwsmPay,         //特微壁挂扫码
    DEV_VehicleOutline,    //车辆外轮廓尺寸检测仪
    DEV_END
};

#endif  // DEVICE_H
