#ifndef VEHPLATEFUNC_H
#define VEHPLATEFUNC_H

#include <QString>

// 获取车牌长度，汉字视为1位
qint32 GetVehPlateLen(const char *szVehPlate);
// 进行车牌比较
bool CompareVehPlate(const char *szVehPlate1, const char *szVehPlate2);
// 输入车牌颜色代码，返回车牌颜色对应的汉字
QString GetVehPlateColorName(quint8 nVehPlateColor);
// 获取全车牌
void GetFullVehPlate(const char *szVehPlate, quint8 nColor, QString &strFullVehPlate);
// 将全车牌转换为按旧系统人工车牌输入规则输入的车牌
void FullVehPlate2OldInputVehPlate(char *szOldInputVehPlate, const char *szFullVehPlate);
// 去除车牌特殊符号；
void RemovePlateSpecChar(char *szDest, qint32 nDestSize, const char *szSrc,
                         qint32 nMaxVehPlateLen = 12);
// void RemoveSpCharFromPlate(const char *szSrc,char *szDest,qint32 nMaxVehPlateLen=12);

quint8 TransVehPlateColor(const QString &sColor);
quint8 TransVehPlateColor(const char *sPlateColor, int nSize);

// 将车牌识别结果按格式要求进行转换
bool ConvertGDWPlate(quint8 &nColor, QString &sPlate, const QString &sFullPlate);

int GetDefaultVehPlateColorForVehClass(int nVehClass);
bool IsValidVehPlateColor(quint8 bColor);

bool IsValidVehPlate(const QString &sPlate);

int compareVlp(const QString &str1, const QString &str2);
int compareVlp_funnzzy(const QString &str1, const QString &str2);

#endif  // VEHPLATEFUNC_H
