#include "forminputvehtype.h"
#include <QPainter>
#include <QKeyEvent>
#include "../../globalui.h"
#include "../../dlgmain.h"
#include "../../log4qt/ilogmsg.h"

FormInputVehType::FormInputVehType(QWidget *parent)
    : CBaseOpWidget(parent)
    , m_pLblTitle(0)
    , m_pLblVehTypeLabel(0)
    , m_pLblVehTypeResult(0)
    , m_pLblHelpInfo(0)
    , m_inputVehType(1)
{
    CreateControls();
    SetupControlProperties();
    InitConnections();
    
    filterChildrenKeyEvent();
    setObjectName("FormInputVehType");
}

FormInputVehType::~FormInputVehType()
{
    // Qt会自动清理子控件
}

void FormInputVehType::CreateControls()
{
    // 创建界面控件
    m_pLblTitle = new QLabel(QString::fromUtf8("补费车型"), this);
    m_pLblVehTypeLabel = new QLabel(QString::fromUtf8("车型"), this);
    m_pLblVehTypeResult = new QLabel(QString::fromUtf8("客1"), this);  // 车型输入结果显示控件
    m_pLblHelpInfo = new QLabel(this);
}

void FormInputVehType::SetupControlProperties()
{
    // 设置字体
    m_fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize);
    m_fontLabel = QFont(g_GlobalUI.m_FontName, 18);
    m_fontHelp = QFont(g_GlobalUI.m_FontName, 12);
    
    // 设置背景色
    m_colorBackground = g_GlobalUI.m_ColorBackground;
    
    // 标题
    m_pLblTitle->setFont(m_fontTitle);
    m_pLblTitle->setAlignment(Qt::AlignCenter);
    
    // 车型标签
    m_pLblVehTypeLabel->setFont(m_fontLabel);
    m_pLblVehTypeLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    
    // 车型输入结果显示
    m_pLblVehTypeResult->setFont(m_fontLabel);
    m_pLblVehTypeResult->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    m_pLblVehTypeResult->setStyleSheet("background-color: white; border: 2px solid gray; padding: 5px;");
    
    // 底部提示信息
    m_pLblHelpInfo->setFont(m_fontHelp);
    m_pLblHelpInfo->setAlignment(Qt::AlignCenter);
    m_pLblHelpInfo->setWordWrap(false);
    m_pLblHelpInfo->setStyleSheet("color: rgb(100, 100, 100);");
    m_pLblHelpInfo->setText(QString::fromUtf8("按【确认】键继续，按【ESC】键取消"));
    m_pLblHelpInfo->show();
}

void FormInputVehType::InitLayout()
{
    int width = rect().width();
    int height = rect().height();
    
    // 标题
    m_pLblTitle->setGeometry(0, 10, width, 40);
    
    // 主要布局区域
    int centerY = height / 2;
    int centerX = width / 2;
    
    // 一行显示：标签 + 结果框
    int labelWidth = 80;
    int labelHeight = 30;
    int resultWidth = 220;
    int resultHeight = 50;
    int spacing = 12;
    int totalWidth = labelWidth + spacing + resultWidth;
    int startX = centerX - totalWidth / 2;
    int rowY = centerY - resultHeight / 2;

    m_pLblVehTypeLabel->setGeometry(startX, rowY + (resultHeight - labelHeight) / 2, labelWidth, labelHeight);
    m_pLblVehTypeResult->setGeometry(startX + labelWidth + spacing, rowY, resultWidth, resultHeight);
    
    // 底部提示信息
    m_pLblHelpInfo->setGeometry(20, height - 60, width - 40, 30);
}

void FormInputVehType::InitConnections()
{
    // 目前不需要信号连接
}

bool FormInputVehType::InputVehType(int currentVehType)
{
    // 保存参数
    m_inputVehType = currentVehType;
    
    // 初始化界面
    InitUI();
    InitLayout();
    
    // 更新车型显示
    OnVehTypeChanged();
    
    // 显示模态对话框
    return (CBaseOpWidget::Rlt_OK == doModalShow());
}

void FormInputVehType::InitUI()
{
    CBaseOpWidget::InitUI();
}

int FormInputVehType::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (!mtcKeyEvent) return 0;

    // 处理车型输入键
    mtcKeyEvent->setKeyType(KC_Func);
    
    // 根据键盘映射输入车型
    if (mtcKeyEvent->func() == KeyCar1) {
        OnInputVehType(1);  // 客1
    } else if (mtcKeyEvent->func() == KeyCar2) {
        OnInputVehType(2);  // 客2
    } else if (mtcKeyEvent->func() == KeyCar3) {
        OnInputVehType(3);  // 客3
    } else if (mtcKeyEvent->func() == KeyCar4) {
        OnInputVehType(4);  // 客4
    } else if (mtcKeyEvent->key() == KeyY) {
        OnInputVehType(11); // 货1
    } else if (mtcKeyEvent->key() == KeyZ) {
        OnInputVehType(12); // 货2
    } else if (mtcKeyEvent->key() == KeyU) {
        OnInputVehType(13); // 货3
    } else if (mtcKeyEvent->key() == KeyF) {
        OnInputVehType(14); // 货4
    } else if (mtcKeyEvent->key() == KeyK) {
        OnInputVehType(15); // 货5
    } else if (mtcKeyEvent->func() == KeyConfirm) {
        OnConfirmClicked();
    } else if (mtcKeyEvent->func() == KeyEsc) {
        OnCancelClicked();
    } else if (mtcKeyEvent->func() == KeyDel) {
        OnClearInput();
    }

    return 1;
}

void FormInputVehType::OnInputVehType(int vehType)
{
    // 设置车型值
    m_inputVehType = vehType;
    
    // 更新显示
    OnVehTypeChanged();
    
    InfoLog(QString("车型输入：%1(%2)").arg(GetVehTypeName(vehType)).arg(vehType));
}

void FormInputVehType::OnClearInput()
{
    // 清除输入，回到默认车型
    m_inputVehType = 1;
    OnVehTypeChanged();
}

bool FormInputVehType::ValidateVehType(QString &errorMsg)
{
    // 检查车型是否有效
    if (m_inputVehType < 1) {
        errorMsg = QString::fromUtf8("请选择有效的车型");
        return false;
    }
    
    // 检查是否为支持的车型
    QString vehTypeName = GetVehTypeName(m_inputVehType);
    if (vehTypeName.startsWith("未知")) {
        errorMsg = QString::fromUtf8("不支持的车型，请重新选择");
        return false;
    }
    
    return true;
}

void FormInputVehType::OnVehTypeChanged()
{
    // 更新车型结果显示控件
    QString vehTypeName = GetVehTypeName(m_inputVehType);
    m_pLblVehTypeResult->setText(vehTypeName);
    InfoLog(QString("车型已选择：%1(%2)").arg(vehTypeName).arg(m_inputVehType));
}

void FormInputVehType::OnConfirmClicked()
{
    QString errorMsg;
    if (ValidateVehType(errorMsg)) {
        InfoLog(QString("车型输入完成：%1(%2)").arg(GetVehTypeName(m_inputVehType)).arg(m_inputVehType));
        OnOk();
    } else {
        ShowErrorMsg(errorMsg);
    }
}

void FormInputVehType::OnCancelClicked()
{
    InfoLog("车型输入取消");
    OnCancel();
}

void FormInputVehType::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);
    
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);

    // 绘制背景
    painter.setBrush(m_colorBackground);
    painter.setPen(Qt::black);
    painter.drawRect(rectClient.x(), rectClient.y(), rectClient.width() - 1, rectClient.height() - 1);

    painter.end();
    QPainter ptThis(this);
    ptThis.drawPixmap(rectClient, pixmap);
}

QString FormInputVehType::GetVehTypeName(int vehType)
{
    switch (vehType) {
        case 1: return "客1";
        case 2: return "客2";
        case 3: return "客3";
        case 4: return "客4";
        case 11: return "货1";
        case 12: return "货2";
        case 13: return "货3";
        case 14: return "货4";
        case 15: return "货5";
        case 16: return "货6";
        default: return QString("未知车型(%1)").arg(vehType);
    }
}

QString FormInputVehType::GetVehTypeDescription(int vehType)
{
    switch (vehType) {
        case 1: return "7座以下小客车";
        case 2: return "8-19座客车";
        case 3: return "20-39座客车";
        case 4: return "40座以上客车";
        case 11: return "2吨以下货车";
        case 12: return "2-5吨货车";
        case 13: return "5-10吨货车";
        case 14: return "10-15吨货车";
        case 15: return "15-25吨货车";
        case 16: return "25吨以上货车";
        default: return QString("未知车型描述");
    }
}
