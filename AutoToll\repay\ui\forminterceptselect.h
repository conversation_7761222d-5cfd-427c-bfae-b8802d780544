#ifndef FORMINTERCEPTSELECT_H
#define FORMINTERCEPTSELECT_H

#include <QLabel>
#include <QPushButton>
#include <QTimer>
#include <QPainter>
#include <QKeyEvent>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>

#include "../../baseopwidget.h"
#include "../../MtcKey/MtcKeyDef.h"
#include "../../globalui.h"
#include "../common/repaytypes.h"

/**
 * @brief 拦截方式选择界面
 * 用于省内名单补费时选择拦截方式（入口拦截或出口拦截）
 */
class FormInterceptSelect : public CBaseOpWidget
{
    Q_OBJECT

public:
    explicit FormInterceptSelect(QWidget *parent = 0);
    ~FormInterceptSelect();

    // 初始化界面
    virtual void InitUI(int iFlag = 0);
    
    // 显示拦截方式选择界面
    bool ShowInterceptSelect(InterceptType &selectedType);
    
    // 设置默认选择项
    void SetDefaultSelection(InterceptType defaultType);
    
    // 移除提示信息控件，保留接口删除

protected:
    // 重写绘制事件
    void paintEvent(QPaintEvent *event);
    
    // 重写按键事件处理
    virtual int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
    
    // 重写模态显示后的处理
    virtual void OnModalShowed();

private slots:
    // 选择定时器超时
    void OnSelectionTimeout();
    
    // 延时操作
    void OnDelayedOk();
    void OnDelayedConfirmSelection();

private:
    // 界面初始化
    void InitControls();
    void SetupControlProperties();
    void InitLayout();
    void InitConnections();
    void InitUIConfig();
    
    // 绘制函数
    void DrawBackground(QPainter &painter);
    void DrawTitle(QPainter &painter);
    void DrawOptions(QPainter &painter);
    void DrawStatusMessage(QPainter &painter);
    void DrawHelpMessage(QPainter &painter);
    void DrawSelectionIndicator(QPainter &painter);
    
    // 选择处理
    void SetCurrentSelection(int index);
    void ConfirmSelection();
    void CancelSelection();
    void UpdateSelectionDisplay();
    void ProcessNumberKey(int number);
    
    // 输入处理
    void ProcessKey1();
    void ProcessKey2();
    void ProcessEnterKey();
    void ProcessEscapeKey();
    void ProcessUpKey();
    void ProcessDownKey();
    
    // 界面状态控制
    void SetUIEnabled(bool enabled);
    void StartSelectionTimer();
    void StopSelectionTimer();
    void UpdateHelpMessage();
    
    // 错误处理
    void ShowErrorMessage(const QString &message);
    void ShowSuccessMessage(const QString &message);
    void ShowWarningMessage(const QString &message);
    void ClearStatusMessage();
    
    // 数据处理
    InterceptType GetInterceptTypeByIndex(int index) const;
    int GetIndexByInterceptType(InterceptType type) const;
    QString GetInterceptTypeName(InterceptType type) const;
    QString GetInterceptTypeDescription(InterceptType type) const;

private:
    // 界面控件
    QLabel *m_pLblTitle;              // 标题标签
    QLabel *m_pLblOption1;            // 选项1标签
    QLabel *m_pLblOption2;            // 选项2标签
    // 描述标签已移除
    QLabel *m_pLblStatus;             // 状态信息标签
    QLabel *m_pLblHelp;               // 帮助信息标签
    
    // 选择数据
    InterceptType m_selectedType;     // 选择的拦截类型
    int m_nCurrentIndex;              // 当前选择索引（0=入口拦截，1=出口拦截）
    bool m_bSelectionConfirmed;       // 是否已确认选择
    
    // 界面状态
    bool m_bProcessing;               // 是否正在处理
    int m_nSelectionTimeout;          // 选择超时时间（秒）
    
    // 定时器
    QTimer *m_pSelectionTimer;        // 选择超时定时器
    
    // 界面配置
    QFont m_fontTitle;                // 标题字体
    QFont m_fontText;                 // 普通文本字体
    QFont m_fontOption;               // 选项字体
    QFont m_fontDescription;          // 描述字体
    
    // 颜色配置
    QColor m_colorBackground;         // 背景色
    QColor m_colorTitle;              // 标题色
    QColor m_colorText;               // 文本色
    QColor m_colorOption;             // 选项色
    QColor m_colorSelectedOption;     // 选中选项色
    QColor m_colorDescription;        // 描述色
    QColor m_colorBorder;             // 边框色
    QColor m_colorError;              // 错误色
    QColor m_colorSuccess;            // 成功色
    QColor m_colorWarning;            // 警告色
    
    // 布局配置
    static const int TITLE_HEIGHT = 70;           // 标题高度
    // 提示区已移除
    static const int OPTION_HEIGHT = 80;          // 选项高度
    static const int STATUS_HEIGHT = 30;          // 状态区高度
    static const int HELP_HEIGHT = 60;            // 帮助区高度
    static const int MARGIN = 20;                 // 边距
    static const int OPTION_SPACING = 30;         // 选项间距
    static const int OPTION_WIDTH = 300;          // 选项宽度
    
    // 选择配置
    static const int SELECTION_TIMEOUT_SECONDS = 60;  // 选择超时时间（秒）
    static const int OPTION_COUNT = 2;                 // 选项数量
    
    // 选项定义
    struct InterceptOption {
        InterceptType type;
        QString name;
        QString description;
        QString shortcut;
    };
    
    static const InterceptOption INTERCEPT_OPTIONS[OPTION_COUNT];
};

#endif // FORMINTERCEPTSELECT_H 