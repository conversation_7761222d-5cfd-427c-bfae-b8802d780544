#include "formauthorization.h"
#include "../../globalui.h"
#include "../../dlgmain.h"
#include "../../log4qt/ilogmsg.h"
#include <QApplication>
#include <QKeyEvent>
#include <QMessageBox>

FormAuthorization::FormAuthorization(QWidget *parent)
    : CBaseOpWidget(parent)
    , m_pLblTitle(0)
    , m_pLblOperatorIdLabel(0)
    , m_pEditOperatorId(0)
    , m_pLblPasswordLabel(0)
    , m_pEditPassword(0)
    , m_pLblStatus(0)
    , m_pLblHelp(0)
    , m_bOperatorIdFocus(true)
    , m_bProcessing(false)
    , m_bAuthorized(false)
    , m_nCursorPos(0)
    , m_nRetryCount(0)
    , m_nRetryCountdown(0)
    
    , m_pRetryTimer(0)
    , m_pAuthTimer(0)
    , m_pAuthManager(0)
{
    // 获取AuthManager实例
    m_pAuthManager = AuthManager::GetInstance();
    
    // 初始化定时器
    m_pRetryTimer = new QTimer(this);
    m_pAuthTimer = new QTimer(this);
    
    // 初始化界面配置
    InitUIConfig();
    
    InfoLog("创建补费授权界面");

    InitUI();
}

FormAuthorization::~FormAuthorization()
{
    InfoLog("销毁补费授权界面");
}

void FormAuthorization::InitUIConfig()
{
    // 设置字体
    m_fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize, QFont::Bold);
    m_fontText = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TextFontSize);
    m_fontEdit = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_EditFontSize);
    m_fontButton = QFont(g_GlobalUI.m_FontName, 14);
    
    // 设置颜色
    m_colorBackground = g_GlobalUI.m_ColorBackground;
    m_colorTitle = QColor(0, 0, 0);
    m_colorText = QColor(50, 50, 50);
    m_colorFocus = QColor(0, 120, 215);
    m_colorError = QColor(220, 50, 50);
    m_colorSuccess = QColor(50, 150, 50);
    m_colorWarning = QColor(200, 120, 0);
}

void FormAuthorization::InitUI(int iFlag)
{
    CBaseOpWidget::InitUI(iFlag);
    
    InfoLog("初始化补费授权界面");
    
    // 初始化控件
    InitControls();
    
    // 设置控件属性
    SetupControlProperties();
    
    // 设置布局
    InitLayout();
    
    // 连接信号
    InitConnections();
    
    // 重置授权状态
    ResetAuthState();
    
    // 安装子控件键盘事件过滤器，在所有控件创建完成后调用
    filterChildrenKeyEvent();
    
    DebugLog("补费授权界面初始化完成");
}

void FormAuthorization::InitControls()
{
    // 创建所有控件
    m_pLblTitle = new QLabel(this);
    
    m_pLblOperatorIdLabel = new QLabel("工 号", this);
    m_pEditOperatorId = new QLineEdit(this);
    m_pLblPasswordLabel = new QLabel("密 码", this);
    m_pEditPassword = new QLineEdit(this);
    m_pLblStatus = new QLabel(this);
    m_pLblHelp = new QLabel(this);
}

void FormAuthorization::SetupControlProperties()
{
    // 设置标题
    m_pLblTitle->setFont(m_fontTitle);
    m_pLblTitle->setAlignment(Qt::AlignCenter);
    m_pLblTitle->setText("班长授权");
    m_pLblTitle->setStyleSheet("color: rgb(0, 0, 0);");
    
    // 提示信息控件已移除
    
    // 设置标签
    m_pLblOperatorIdLabel->setFont(m_fontText);
    m_pLblOperatorIdLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    
    m_pLblPasswordLabel->setFont(m_fontText);
    m_pLblPasswordLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    
    // 设置输入框
    m_pEditOperatorId->setFont(m_fontEdit);
    m_pEditOperatorId->setMaxLength(MAX_OPERATOR_ID_LEN);
    m_pEditOperatorId->setPlaceholderText("请输入工号");
    m_pEditOperatorId->setStyleSheet("border: 2px solid rgb(180, 180, 180); border-radius: 4px; padding: 5px;");
    
    m_pEditPassword->setFont(m_fontEdit);
    m_pEditPassword->setMaxLength(MAX_PASSWORD_LEN);
    m_pEditPassword->setEchoMode(QLineEdit::Password);
    m_pEditPassword->setPlaceholderText("请输入密码");
    m_pEditPassword->setStyleSheet("border: 2px solid rgb(180, 180, 180); border-radius: 4px; padding: 5px;");
    
    // 设置状态信息
    m_pLblStatus->setFont(m_fontText);
    m_pLblStatus->setAlignment(Qt::AlignCenter);
    m_pLblStatus->setWordWrap(true);
    
    // 设置帮助信息
    m_pLblHelp->setFont(m_fontText);
    m_pLblHelp->setAlignment(Qt::AlignCenter);
    m_pLblHelp->setWordWrap(true);
    m_pLblHelp->setText(QString::fromUtf8("请输入具有班长权限的工号和密码\n按【确认/放行】键确认，按【取消】键取消"));
    m_pLblHelp->setStyleSheet("color: rgb(100, 100, 100);");
}

void FormAuthorization::InitLayout()
{
    QRect rectClient = this->rect();
    int totalHeight = rectClient.height();
    int width = rectClient.width();
    int currentY = 0;
    
    // 标题区域
    m_pLblTitle->setGeometry(0, currentY, width, TITLE_HEIGHT);
    currentY += TITLE_HEIGHT;
    
    // 提示区域已移除，上移布局
    currentY += MARGIN;
    
    // 输入区域布局
    int centerX = width / 2;
    int totalInputWidth = LABEL_WIDTH + EDIT_WIDTH;
    int inputStartX = centerX - totalInputWidth / 2;
    
    // 工号输入 - 增加标签宽度以确保文字完全显示
    m_pLblOperatorIdLabel->setGeometry(inputStartX, currentY, LABEL_WIDTH + 20, INPUT_HEIGHT+INPUT_HEIGHT*0.5);
    m_pEditOperatorId->setGeometry(inputStartX + LABEL_WIDTH + 20, currentY, EDIT_WIDTH, INPUT_HEIGHT+INPUT_HEIGHT*0.5);
    currentY += INPUT_HEIGHT+INPUT_HEIGHT*0.5 + SPACING;
    
    // 密码输入 - 增加标签宽度以确保文字完全显示
    m_pLblPasswordLabel->setGeometry(inputStartX, currentY, LABEL_WIDTH + 20, INPUT_HEIGHT+INPUT_HEIGHT*0.5);
    m_pEditPassword->setGeometry(inputStartX + LABEL_WIDTH + 20, currentY, EDIT_WIDTH, INPUT_HEIGHT+INPUT_HEIGHT*0.5);
    currentY += INPUT_HEIGHT + MARGIN;
    
    // 状态信息区域
    m_pLblStatus->setGeometry(MARGIN, currentY, width - 2*MARGIN, STATUS_HEIGHT);
    currentY += STATUS_HEIGHT + SPACING;
    
    // 帮助信息区域
    int helpHeight = totalHeight - currentY - MARGIN;
    m_pLblHelp->setGeometry(MARGIN, currentY, width - 2*MARGIN, helpHeight);
}

void FormAuthorization::InitConnections()
{
    // 只连接定时器信号，输入框通过mtcKeyPressed处理
    connect(m_pRetryTimer, SIGNAL(timeout()), this, SLOT(OnRetryTimer()));
    connect(m_pAuthTimer, SIGNAL(timeout()), this, SLOT(OnAuthTimeout()));
}

bool FormAuthorization::DoAuthorization(QString &operatorId, QString &operatorName)
{
    InfoLog("开始补费授权验证");
    
    // 重置状态
    ResetAuthState();
    
    // 启动授权超时定时器
    m_pAuthTimer->start(AUTH_TIMEOUT_SECONDS * 1000);
    
    // 显示界面
    int result = doModalShow();
    
    // 停止定时器
    m_pAuthTimer->stop();
    m_pRetryTimer->stop();
    
    if (result == CBaseOpWidget::Rlt_OK) {
        // 授权成功
        operatorId = QString::number(m_authOperInfo.dwOper);
        operatorName = m_authOperInfo.sOperName;
        InfoLog(QString("补费授权成功 - 操作员:%1(%2)").arg(operatorId).arg(operatorName));
        return true;
    } else {
        // 授权失败或取消
        InfoLog("补费授权取消或失败");
        return false;
    }
}

// SetAuthPrompt 已移除：不再使用独立的提示控件

void FormAuthorization::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);
    
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);
    
    // 绘制背景
    DrawBackground(painter);
    
    // 绘制标题
    DrawTitle(painter);
    
    // 提示信息区域已移除
    
    // 绘制输入标签
    DrawInputLabels(painter);
    
    // 绘制状态信息
    DrawStatusMessage(painter);
    
    // 绘制帮助信息
    DrawHelpMessage(painter);
    
    painter.end();
    
    // 绘制到窗口
    QPainter windowPainter(this);
    windowPainter.drawPixmap(rectClient, pixmap);
}

void FormAuthorization::DrawBackground(QPainter &painter)
{
    QRect rectClient = this->rect();
    
    // 绘制背景色
    painter.setBrush(m_colorBackground);
    painter.setPen(Qt::black);
    painter.drawRect(rectClient.x(), rectClient.y(), rectClient.width() - 1, rectClient.height() - 1);
}

void FormAuthorization::DrawTitle(QPainter &painter)
{
    // 标题由QLabel绘制，这里不需要额外绘制
}

// 提示信息绘制已移除

void FormAuthorization::DrawInputLabels(QPainter &painter)
{
    // 输入标签由QLabel绘制，这里可以绘制焦点指示
//    if (m_bOperatorIdFocus && m_pEditOperatorId) {
//        QRect rect = m_pEditOperatorId->geometry();
//        painter.setPen(QPen(m_colorFocus, 3));
//        painter.setBrush(Qt::NoBrush);
//        painter.drawRect(rect.adjusted(-2, -2, 2, 2));
//    } else if (!m_bOperatorIdFocus && m_pEditPassword) {
//        QRect rect = m_pEditPassword->geometry();
//        painter.setPen(QPen(m_colorFocus, 3));
//        painter.setBrush(Qt::NoBrush);
//        painter.drawRect(rect.adjusted(-2, -2, 2, 2));
//    }
}

void FormAuthorization::DrawStatusMessage(QPainter &painter)
{
    // 状态信息由QLabel绘制，这里不需要额外绘制
}

void FormAuthorization::DrawHelpMessage(QPainter &painter)
{
    // 帮助信息由QLabel绘制，这里不需要额外绘制
}

int FormAuthorization::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (!mtcKeyEvent || m_bProcessing) return 0;
    
    if (mtcKeyEvent->isNumKey()) {
        // 处理数字输入
        mtcKeyEvent->setKeyType(KC_Number);
        ProcessNumberInput(mtcKeyEvent->key());
    } else {
        // 处理功能键
        mtcKeyEvent->setKeyType(KC_Func);
        if (mtcKeyEvent->func() == KeyDel) {
            // 删除键处理
            ProcessBackspace();
        } else if (mtcKeyEvent->func() == KeyConfirm) {
            // 确认键处理
            ProcessEnter();
        } else if (mtcKeyEvent->func() == KeyEsc) {
            // 取消键处理
            ProcessEscape();
        } else if (mtcKeyEvent->func() == KeyPoint) {
            // 点号键切换焦点
            SwitchInputFocus();
        } else {
            return CBaseOpWidget::mtcKeyPressed(mtcKeyEvent);
        }
    }
    
    return 1;
}

void FormAuthorization::OnModalShowed()
{
    CBaseOpWidget::OnModalShowed();
    
    // 设置焦点到工号输入框
    SetOperatorIdFocus();
    
    DebugLog("补费授权界面显示完成");
}

void FormAuthorization::ProcessNumberInput(int keyInput)
{
    if (m_bOperatorIdFocus) {
        if (m_operatorId.length() < MAX_OPERATOR_ID_LEN) {
            m_operatorId.insert(m_nCursorPos, QChar(keyInput));
            m_nCursorPos++;
        } else {
            // 如果工号输入满了，自动跳到密码栏
            SwitchInputFocus();
        }
    } else {
        if (m_password.length() < MAX_PASSWORD_LEN) {
            m_password.insert(m_nCursorPos, QChar(keyInput));
            m_nCursorPos++;
        }
    }
    RefreshInput();
}

void FormAuthorization::RefreshInput()
{
    m_pEditOperatorId->setText(m_operatorId);
    m_pEditPassword->setText(m_password);
    
    if (m_bOperatorIdFocus) {
        m_pEditOperatorId->setFocus();
        if (m_nCursorPos > m_operatorId.length()) {
            m_nCursorPos = m_operatorId.length();
        }
        m_pEditOperatorId->setCursorPosition(m_nCursorPos);
    } else {
        m_pEditPassword->setFocus();
        if (m_nCursorPos > m_password.length()) {
            m_nCursorPos = m_password.length();
        }
        m_pEditPassword->setCursorPosition(m_nCursorPos);
    }
}

void FormAuthorization::ProcessBackspace()
{
    if (m_bOperatorIdFocus) {
        if (m_nCursorPos > 0) {
            m_operatorId.remove(--m_nCursorPos, 1);
        }
    } else {
        if (m_nCursorPos > 0) {
            m_password.remove(--m_nCursorPos, 1);
        } else if (m_password.length() == 0) {
            // 如果密码为空且按删除键，切换到工号输入
            SwitchInputFocus();
        }
    }
    RefreshInput();
}

void FormAuthorization::ProcessClear()
{
    if (m_bOperatorIdFocus) {
        m_operatorId.clear();
        m_nCursorPos = 0;
    } else {
        m_password.clear();
        m_nCursorPos = 0;
    }
    
    // 清除状态信息
    if (m_pLblStatus) {
        m_pLblStatus->clear();
    }
    
    RefreshInput();
}

void FormAuthorization::ProcessEnter()
{
    if (m_bOperatorIdFocus) {
        // 工号输入状态，切换到密码输入
        SwitchInputFocus();
    } else {
        // 密码输入状态，执行验证
        QString sErrMsg;
        if (ValidateInputs()) {
            PerformAuthorization();
        } else {
            ShowWarningMessage("请输入完整的工号和密码");
        }
    }
}

void FormAuthorization::ProcessEscape()
{
    InfoLog("用户取消授权验证");
    
    // 结束授权会话
    if (m_pAuthManager && m_bAuthorized) {
        m_pAuthManager->EndAuthSession();
    }
    
    OnCancel();
}

void FormAuthorization::SwitchInputFocus()
{
    m_bOperatorIdFocus = !m_bOperatorIdFocus;
    if (m_bOperatorIdFocus) {
        m_nCursorPos = m_operatorId.length();
    } else {
        m_nCursorPos = m_password.length();
    }
    RefreshInput();
    UpdateFocusDisplay();
}

void FormAuthorization::SetOperatorIdFocus()
{
    m_bOperatorIdFocus = true;
    m_nCursorPos = m_operatorId.length();
    RefreshInput();
    UpdateFocusDisplay();
}

void FormAuthorization::SetPasswordFocus()
{
    m_bOperatorIdFocus = false;
    m_nCursorPos = m_password.length();
    RefreshInput();
    UpdateFocusDisplay();
}

void FormAuthorization::UpdateFocusDisplay()
{
    // 更新输入框样式
    QString normalStyle = "border: 2px solid rgb(180, 180, 180); border-radius: 4px; padding: 5px;";
    QString focusStyle = "border: 2px solid rgb(0, 120, 215); border-radius: 4px; padding: 5px;";
    
    if (m_pEditOperatorId) {
        m_pEditOperatorId->setStyleSheet(m_bOperatorIdFocus ? focusStyle : normalStyle);
    }
    
    if (m_pEditPassword) {
        m_pEditPassword->setStyleSheet(!m_bOperatorIdFocus ? focusStyle : normalStyle);
    }
    
    update();
}

bool FormAuthorization::ValidateOperatorId()
{
    if (m_operatorId.isEmpty()) {
        return false;
    }
    
    // 检查是否为纯数字
    bool ok;
    m_operatorId.toInt(&ok);
    return ok && m_operatorId.length() >= 3;
}

bool FormAuthorization::ValidatePassword()
{
    return !m_password.isEmpty();
}

bool FormAuthorization::ValidateInputs()
{
    if (!ValidateOperatorId()) {
        ShowErrorMessage("请输入有效的操作员工号（至少3位数字）");
        SetOperatorIdFocus();
        return false;
    }
    
    if (!ValidatePassword()) {
        ShowErrorMessage("请输入操作员密码");
        SetPasswordFocus();
        return false;
    }
    
    return true;
}

void FormAuthorization::PerformAuthorization()
{
    if (!ValidateInputs()) {
        return;
    }
    
    InfoLog(QString("开始验证操作员授权 - 工号:%1").arg(m_operatorId));
    
    m_bProcessing = true;
    SetUIEnabled(false);
    ShowWarningMessage("正在验证授权...");
    
    // 使用项目现有的操作员验证
    COperTable *pOperTable = (COperTable *)CParamFileMgr::GetParamFile(cfOper);
    if (!pOperTable) {
        HandleAuthFailure("操作员表未加载");
        return;
    }
    
    COperInfo operInfo;
    QString errorMsg;
    quint32 dwOper = m_operatorId.toInt();
    
    // 验证操作员信息
    bool verifyResult = pOperTable->VerifyOperatorInput(
        Ptr_Info->GetStationID(), dwOper, m_password, operInfo, errorMsg);
    
    if (!verifyResult) {
        HandleAuthFailure(errorMsg.isEmpty() ? "工号或密码错误" : errorMsg);
        return;
    }
    
    // 验证班长权限
    bool hasManagerPermission = false;
    if (operInfo.wOperType == 2) {
        // 班长类型
        hasManagerPermission = true;
    } else if (operInfo.GetOperRole(OR_Special) == OR_Special) {
        // 特殊权限
        hasManagerPermission = true;
    }
    
    if (!hasManagerPermission) {
        HandleAuthFailure("该操作员不具有班长权限");
        return;
    }
    
    // 授权成功
    HandleAuthSuccess(operInfo);
}

void FormAuthorization::HandleAuthSuccess(const COperInfo &operInfo)
{
    m_authOperInfo = operInfo;
    m_bAuthorized = true;  // 设置授权成功标志
    
    // 启动AuthManager的授权会话
    if (m_pAuthManager) {
        QString operatorId = QString::number(operInfo.dwOper);
        m_pAuthManager->StartAuthSession(operatorId);
    }
    
    InfoLog(QString("授权验证成功 - 操作员:%1(%2)")
            .arg(operInfo.dwOper)
            .arg(operInfo.sOperName));
    
    ShowSuccessMessage(QString("授权成功！操作员：%1")
                      .arg(operInfo.sOperName));
    
    // 延迟1秒后关闭界面
    QTimer::singleShot(1000, this, SLOT(OnDelayedOk()));
}

void FormAuthorization::HandleAuthFailure(const QString &errorMsg)
{
    m_nRetryCount++;
    
    ErrorLog(QString("授权验证失败 - 错误:%1, 重试次数:%2").arg(errorMsg).arg(m_nRetryCount));
    
    if (m_nRetryCount >= MAX_RETRY_COUNT) {
        ShowErrorMessage(QString("授权失败次数过多，请稍后重试\n错误：%1").arg(errorMsg));
        
        // 启动重试延迟
        StartRetryCountdown();
    } else {
        ShowErrorMessage(QString("授权失败：%1\n还可重试 %2 次")
                        .arg(errorMsg)
                        .arg(MAX_RETRY_COUNT - m_nRetryCount));
        
        // 清空密码，重新输入
        m_password.clear();
        if (m_pEditPassword) {
            m_pEditPassword->clear();
        }
        SetPasswordFocus();
        
        m_bProcessing = false;
        SetUIEnabled(true);
    }
}

void FormAuthorization::SetUIEnabled(bool enabled)
{
    if (m_pEditOperatorId) m_pEditOperatorId->setEnabled(enabled);
    if (m_pEditPassword) m_pEditPassword->setEnabled(enabled);
}

void FormAuthorization::ClearInputs()
{
    m_operatorId.clear();
    m_password.clear();
    m_nCursorPos = 0;
    
    if (m_pLblStatus) m_pLblStatus->clear();
    
    RefreshInput();
}

void FormAuthorization::ResetAuthState()
{
    m_nRetryCount = 0;
    m_nRetryCountdown = 0;
    m_bProcessing = false;
    m_bAuthorized = false;  // 重置授权状态
    
    ClearInputs();
    SetOperatorIdFocus();
    SetUIEnabled(true);
    
    // 提示文本已移除
}

void FormAuthorization::StartRetryCountdown()
{
    m_nRetryCountdown = RETRY_DELAY_SECONDS;
    m_bProcessing = true;
    SetUIEnabled(false);
    
    UpdateRetryPrompt();
    
    // 启动倒计时定时器
    m_pRetryTimer->start(1000);
}

void FormAuthorization::UpdateRetryPrompt()
{
    ShowWarningMessage(QString("重试次数过多，请等待 %1 秒后重试").arg(m_nRetryCountdown));
}

void FormAuthorization::ShowErrorMessage(const QString &message)
{
    if (m_pLblStatus) {
        m_pLblStatus->setText(message);
        m_pLblStatus->setStyleSheet("color: rgb(220, 50, 50);");
    }
    ErrorLog(QString("补费授权界面错误：%1").arg(message));
}

void FormAuthorization::ShowSuccessMessage(const QString &message)
{
    if (m_pLblStatus) {
        m_pLblStatus->setText(message);
        m_pLblStatus->setStyleSheet("color: rgb(50, 150, 50);");
    }
    InfoLog(QString("补费授权界面成功：%1").arg(message));
}

void FormAuthorization::ShowWarningMessage(const QString &message)
{
    if (m_pLblStatus) {
        m_pLblStatus->setText(message);
        m_pLblStatus->setStyleSheet("color: rgb(200, 120, 0);");
    }
    WarnLog(QString("补费授权界面警告：%1").arg(message));
}



void FormAuthorization::OnRetryTimer()
{
    m_nRetryCountdown--;
    
    if (m_nRetryCountdown <= 0) {
        // 倒计时结束，恢复输入
        m_pRetryTimer->stop();
        ResetAuthState();
        ShowWarningMessage("现在可以重新尝试授权验证");
    } else {
        // 更新倒计时显示
        UpdateRetryPrompt();
    }
}

void FormAuthorization::OnAuthTimeout()
{
    WarnLog("补费授权验证超时");
    ShowErrorMessage("授权验证超时，请重新操作");
    
    // 结束授权会话
    if (m_pAuthManager && m_bAuthorized) {
        m_pAuthManager->EndAuthSession();
    }
    
    QTimer::singleShot(2000, this, SLOT(OnDelayedCancel()));
}

QString FormAuthorization::MaskPassword(const QString &password)
{
    return QString(password.length(), '*');
}

QString FormAuthorization::GetInputDisplay(const QString &input, bool isPassword)
{
    if (isPassword) {
        return MaskPassword(input);
    }
    return input;
}

// 延时操作slot实现
void FormAuthorization::OnDelayedOk()
{
    OnOk();
}

void FormAuthorization::OnDelayedCancel()
{
    OnCancel();
}

bool FormAuthorization::IsAuthorizationValid() const
{
    if (m_pAuthManager) {
        return m_pAuthManager->IsAuthorizationValid();
    }
    return m_bAuthorized;  // 如果AuthManager不可用，则使用本地状态
}

// FormAuthorization 实现完成
