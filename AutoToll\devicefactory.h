#ifndef DEVICEFACTORY_H
#define DEVICEFACTORY_H
#include <QObject>

#include "IOCard.h"
#include "PrinterDevice.h"
#include "VDMDev.h"
#include "VPRDev.h"
#include "abstractdev.h"
#include "autoextollscreen.h"
#include "cardreader.h"
#include "ccardmachine.h"
#include "ccardmachinefx.h"
#include "cpaymentmachine.h"
#include "device.h"
#include "faredisplayer_gb.h"
#include "fs_little.h"
#include "invoiceqrcode.h"
#include "laneinfo.h"
#include "rsudev.h"
#include "speventdev.h"
#include "vcrdev.h"
#include "videocard.h"
#include "wtsysdev.h"
#include "wtsysdev_zc.h"
#include "mobilepay.h"
#include "vehicleoutlinedev.h"

const int MAX_CARD_READER_NUM = 3;
const int MAX_FAREDISPLAYER_NUM = 2;
const int MAX_VPR_NUM = 2;
const int MAX_RSU_NUM = 2;
const int MAX_DEV_COUNT = DEV_END;

class CDeviceFactory : public QObject
{
    Q_OBJECT
public:
    static void InitDevFactory(int nLaneType, bool bHaveCardMgr);
    static bool InitDev(QString &sError);
    static void ReleaseAllDev();
    //设置设备状态信号与槽连接
    static bool ConnDevStateSignal(const QObject *receiver, const char *method);
    static void DisConnDevStateSignal(const QObject *receiver, const char *method);
    static void FareDisplay_ShowHelpMsg(int nIndex, const QString &sMsg);

    //该函数调用完需判断是否返回空
    static CFareDisplayer_GB *GetETCFareDisPlayer(int nIndex)
    {
        int devIndex = nIndex;
        if (nIndex >= MAX_FAREDISPLAYER_NUM) devIndex = 1;
        return m_pETCFareDisplayer[nIndex];
    }

    static CFs_little *GetLEDFareDisPlayer()
    {
        if (!m_pLEDFare) m_pLEDFare = new CFs_little();
        return m_pLEDFare;
    }
    static CVideoCard *GetVideoCard()
    {
        if (!m_pVideoCard) m_pVideoCard = new CVideoCard();
        return m_pVideoCard;
    }
    static CVPRDev *GetVPRDev(int nDevIndex)
    {
        int nIndex = nDevIndex;
        if (nIndex >= MAX_VPR_NUM) nIndex = DevIndex_Second;
        return m_pVPRDevs[nIndex];
    }

    static CCardReader *GetCardReader(int nIndex = 0);
    static CIOCard *GetIoCard()
    {
        if (!m_pIOCard) m_pIOCard = new CIOCard();
        return m_pIOCard;
    }

    static CIOCard *GetIOCard()
    {
        if (!m_pIOCard) {
            m_pIOCard = new CIOCard();
        }
        return m_pIOCard;
    }

    static CVDMDev *GetVDMDev()
    {
        if (!m_pVDMDev) m_pVDMDev = new CVDMDev();
        return m_pVDMDev;
    }
    static void StartAlarm(int nIndex, int nMSeconds = 10000);
    static void StopAlarm(int nIndex);
    static void SetAllow(bool bAllow);

    static CRsuDev *GetRsuDev(int nIndex);
    static CCardMgr *GetCardMachine();
    static CPaymentMachine *GetPayMentMgr();
    static CPrinterDevice *GetPrinterDev();
    static MobilePayBase *GetMobilePayTw();
    static CSpEventDev *GetSpEventDev();

    static CWtSysDev_ZC *GetWeightDev()
    {
        if (!m_pWtDev) m_pWtDev = new CWtSysDev_ZC();
        return m_pWtDev;
    }

    static VCRDev *GetVCRDev()
    {
        if (!m_pVcrDev) {
            m_pVcrDev = new VCRDev();
        }
        return m_pVcrDev;
    }

    static AutoExTollScreen *GetAutoExTollScreen()
    {
        if (!m_pAutoExTollScreen) {
            m_pAutoExTollScreen = new AutoExTollScreen();
        }
        return m_pAutoExTollScreen;
    }

    static CVehicleOutlineDev *GetVehicleOutlineDev()
    {
        if (!m_pVehicleOutlineDev) {
            m_pVehicleOutlineDev = new CVehicleOutlineDev();
        }
        return m_pVehicleOutlineDev;
    }

    static QString GetReaderStatus(QString &sManuId, QString &sReaderVer);
    static QString GetVPRStatus();
    static QString GetRsuStatus(QString &sManuId, QString &HardVer, QString &softVer);
    static CInvoiceQrCode *GetQrCode();

protected:
    CDeviceFactory(void) {}
    ~CDeviceFactory(void) {}

public:
    static bool InitCardReader(QString &sError, const QObject *receiver, const char *method);
    static bool InitCardMachine(QString &sError, const QObject *receiver, const char *method,
                                const char *helpBtn);
    static bool InitInvoiceQrCode();

protected:
    static bool m_bHaseReader[MAX_CARD_READER_NUM];        //判断是否启用卡读写器
    static CCardReader *m_pcardRead[MAX_CARD_READER_NUM];  //读卡器 //IO设备
    static CFareDisplayer_GB *m_pETCFareDisplayer[MAX_FAREDISPLAYER_NUM];  //国标费显
    static CFs_little *m_pLEDFare;                                         // LED大显
    static CVideoCard *m_pVideoCard;                                       //视频采集卡
    static CVPRDev *m_pVPRDevs[MAX_VPR_NUM];                               //车牌
    static CIOCard *m_pIOCard;                                             // IO卡
    static CVDMDev *m_pVDMDev;
    static CRsuDev *m_pRsuDevs[2];
    static CCardMgr *m_pCardMachine;
    static int m_nCardReaderNum;
    static quint8 m_bLaneType;
    static bool m_bHaveCardMgr;
    //计重设备
    static CWtSysDev_ZC *m_pWtDev;
    //车型识别设备
    static VCRDev *m_pVcrDev;
    //自助收费外屏
    static AutoExTollScreen *m_pAutoExTollScreen;
    static CPaymentMachine *m_pPaymentMgr;
    static CPrinterDevice *m_pPrinter;
    //移动支付接口 网络直连
    // static MobilePayBase *m_pMobilePayTw;
    static MobilePayBase *m_pMobilePay;
    //特微壁挂扫码设备
    static CSpEventDev *m_pSpClient;
    static CInvoiceQrCode *m_pQrCode;
    //车辆外轮廓尺寸检测仪
    static CVehicleOutlineDev *m_pVehicleOutlineDev;
};

#endif
