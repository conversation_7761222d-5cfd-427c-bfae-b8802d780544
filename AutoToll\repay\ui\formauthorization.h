#ifndef FORMAUTHORIZATION_H
#define FORMAUTHORIZATION_H

#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QTimer>
#include <QPainter>
#include <QKeyEvent>
#include <QDateTime>

#include "../../baseopwidget.h"
#include "../../MtcKey/MtcKeyDef.h"
#include "paramfilemgr.h"
#include "../../common/lanetype.h"
#include "../business/authmanager.h"

/**
 * @brief 补费授权验证界面
 * 专门用于补费操作的班长授权验证
 */
class FormAuthorization : public CBaseOpWidget
{
    Q_OBJECT

public:
    explicit FormAuthorization(QWidget *parent = 0);
    ~FormAuthorization();

    // 初始化界面
    virtual void InitUI(int iFlag = 0);
    
    // 执行授权验证
    bool DoAuthorization(QString &operatorId, QString &operatorName);
    
    // 获取授权状态
    bool IsAuthorized() const { return m_bAuthorized; }
    
    // 检查授权是否仍然有效（通过AuthManager）
    bool IsAuthorizationValid() const;

protected:
    // 重写绘制事件
    void paintEvent(QPaintEvent *event);
    
    // 重写按键事件处理
    int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
    
    // 重写模态显示后的处理
    virtual void OnModalShowed();

private slots:
    // 重试定时器
    void OnRetryTimer();
    
    // 授权超时
    void OnAuthTimeout();
    
    // 延时操作
    void OnDelayedOk();
    void OnDelayedCancel();

private:
    // 界面初始化
    void InitUIConfig();
    void InitControls();
    void SetupControlProperties();
    void InitLayout();
    void InitConnections();
    
    // 绘制函数
    void DrawBackground(QPainter &painter);
    void DrawTitle(QPainter &painter);
    
    void DrawInputLabels(QPainter &painter);
    void DrawStatusMessage(QPainter &painter);
    void DrawHelpMessage(QPainter &painter);
    
    // 输入处理
    void ProcessNumberInput(int keyInput);
    void ProcessBackspace();
    void RefreshInput();
    void ProcessClear();
    void ProcessEnter();
    void ProcessEscape();
    
    // 焦点管理
    void SwitchInputFocus();
    void SetOperatorIdFocus();
    void SetPasswordFocus();
    void UpdateFocusDisplay();
    
    // 验证处理
    bool ValidateOperatorId();
    bool ValidatePassword();
    bool ValidateInputs();
    void PerformAuthorization();
    void HandleAuthSuccess(const COperInfo &operInfo);
    void HandleAuthFailure(const QString &errorMsg);
    
    // 界面状态控制
    void SetUIEnabled(bool enabled);
    void ClearInputs();
    void ResetAuthState();
    void UpdateRetryPrompt();
    
    // 错误处理
    void ShowErrorMessage(const QString &message);
    void ShowSuccessMessage(const QString &message);
    void ShowWarningMessage(const QString &message);
    void StartRetryCountdown();
    
    // 数据处理
    QString MaskPassword(const QString &password);
    QString GetInputDisplay(const QString &input, bool isPassword);

private:
    // 界面控件
    QLabel *m_pLblTitle;              // 标题标签
    QLabel *m_pLblOperatorIdLabel;    // 工号标签
    QLineEdit *m_pEditOperatorId;     // 工号输入框
    QLabel *m_pLblPasswordLabel;      // 密码标签
    QLineEdit *m_pEditPassword;       // 密码输入框
    QLabel *m_pLblStatus;             // 状态信息标签
    QLabel *m_pLblHelp;               // 帮助信息标签
    
    // 输入数据
    QString m_operatorId;             // 操作员工号
    QString m_password;               // 操作员密码
    
    // 界面状态
    bool m_bOperatorIdFocus;          // 工号输入框是否有焦点
    bool m_bProcessing;               // 是否正在处理
    bool m_bAuthorized;               // 是否授权成功
    int m_nCursorPos;                 // 当前光标位置
    int m_nRetryCount;                // 重试次数
    int m_nRetryCountdown;            // 重试倒计时
    
    // 授权结果
    COperInfo m_authOperInfo;         // 授权成功的操作员信息
    
    // 定时器
    QTimer *m_pRetryTimer;            // 重试定时器
    QTimer *m_pAuthTimer;             // 授权超时定时器
    
    // 组件实例
    AuthManager *m_pAuthManager;      // 授权管理器
    
    // 界面配置
    QFont m_fontTitle;                // 标题字体
    QFont m_fontText;                 // 普通文本字体
    QFont m_fontEdit;                 // 输入框字体
    QFont m_fontButton;               // 按钮字体
    
    // 颜色配置
    QColor m_colorBackground;         // 背景色
    QColor m_colorTitle;              // 标题色
    QColor m_colorText;               // 文本色
    QColor m_colorFocus;              // 焦点色
    QColor m_colorError;              // 错误色
    QColor m_colorSuccess;            // 成功色
    QColor m_colorWarning;            // 警告色
    
    // 常量定义
    static const int MAX_OPERATOR_ID_LEN = 8;     // 最大工号长度
    static const int MAX_PASSWORD_LEN = 16;       // 最大密码长度
    static const int MAX_RETRY_COUNT = 3;         // 最大重试次数
    static const int RETRY_DELAY_SECONDS = 5;     // 重试延迟时间(秒)
    static const int AUTH_TIMEOUT_SECONDS = 300;  // 授权超时时间(秒)
    static const int TITLE_HEIGHT = 60;           // 标题高度
    // 提示区已移除
    static const int INPUT_HEIGHT = 35;           // 输入框高度
    static const int LABEL_WIDTH = 100;            // 标签宽度
    static const int EDIT_WIDTH = 200;            // 输入框宽度
    static const int STATUS_HEIGHT = 30;          // 状态区高度
    static const int HELP_HEIGHT = 50;            // 帮助区高度
    static const int MARGIN = 20;                 // 边距
    static const int SPACING = 15;                // 间距
};

#endif // FORMAUTHORIZATION_H 
