#ifndef FORMREPAYNEW_H
#define FORMREPAYNEW_H

#include <QObject>
#include <QTimer>

#include <QDateTime>
#include "forminputvehtype.h"
#include "formrepaycomplete.h"


#include "../../MtcKey/MtcKeyDef.h"
#include "../common/repaytypes.h"
#include "../business/repaymanager.h"
#include "../../transinfo.h"
#include "FormRepaySuccess.h"
#include "FormProvinceRepayInfo.h"
#include "formpaymentselect.h"
#include "../../common/lanetype.h"
#include "../../forminputplate.h"
#include "forminputamount.h"
#include "laneinfo.h"
#include "prodebpayment.h"
#include "../business/authmanager.h"
#include "../config/repayconfig.h"

//省份的拼音选择（从forminputplate复制）
//typedef struct
//{
//    QString sPy;
//    QString sHz;
//} ProvSelect;

/**
 * @brief 新版补费流程管理类
 * 作为纯流程控制器，不再提供界面功能
 * 支持当趟补费和省内名单补费两种模式
 */
class FormRepayNew : public QObject
{
    Q_OBJECT

public:
    explicit FormRepayNew(QObject *parent = NULL);
    virtual ~FormRepayNew();

    // 初始化界面
    virtual void InitUI(int iFlag = 0);
    
    // 设置补费类型
    void SetRepayType(RepayType type);
    
    // 设置车辆信息
    void SetVehicleInfo(const QString &vehPlate, int vehPlateColor, int vehType);
    
    // 设置拦截方式（省内名单补费专用）
    void SetInterceptType(InterceptType interceptType);
    
    // 开始补费流程
    bool StartRepayProcess();

public slots:
    // 业务逻辑方法
    void OnCancel();

signals:
    // 补费完成信号（用于通知外部UI）
    void repayCompleted(bool success, const QString &message);

private slots:
    // 补费流程信号响应（原RepayManager信号，现在内部处理）
    void OnRepayStarted(RepayType type);

    void OnRepayCompleted(bool success, const QString &message);
    void OnPaymentProcessing();
    void OnPaymentCompleted(bool success, const QString &message);
    void OnDebtQueryCompleted(bool success, const RepayDebtQueryResult &result);
    void OnRepayError(RepayErrorCode errorCode, const QString &message);
    
    // 业务组件信号响应（从RepayManager移植）
    void OnAuthorizationCompleted(bool success);
    void OnDebtQueryFinished(bool success, const RepayDebtQueryResult &result);
    void OnNotifyCompleteFinished(bool success, const QString &result);
    
    // 界面操作响应（已简化，保留以兼容现有调用）
    void OnVehPlateChanged();
    void OnAmountChanged();
    void OnPaymentButtonClicked();
    
    // 注意：车牌颜色和支付方式现在通过键盘输入和单独页面处理
    
    // 定时器响应
    void OnQueryTimeout();
    
    // 延时操作
    void OnDelayedOk();
    void OnDelayedCancel();
    void OnDelayedSuccess();

private:
    // 初始化方法（已简化为空实现，保留以兼容现有调用）
    void InitUIConfig();
    void InitControls();
    void SetupControlProperties();
    void SetupButtonStyles();
    void InitLayout();
    void InitConnections();
    
    // 设置车牌颜色
    void SetVehPlateColor(int color);
    

    

    
    // 辅助函数
    QString GetVehPlateColorName(int color);
    
    // 状态控制（已简化，部分方法保留以兼容现有调用）
    void UpdateUI();

    void UpdateDebtInfo(const RepayDebtQueryResult &result);
    void SetUIEnabled(bool enabled);

    void ShowStageMessage(const QString &message);
    void ClearInputs();
    void ResetUI();
    
    // 输入验证
    bool ValidateVehPlate();
    bool ValidateAmount();
    bool ValidateInputs();
    
    // 业务处理
    void PerformAuthorization();
    void ProcessCurrentRepay();
    void ProcessProvinceRepay();
    void StartDebtQuery();
    void StartVehicleInput();              // 开始车辆输入
    void StartPaymentProcess();            // 开始支付流程
    void ProcessPayment(CTransPayType payType);
    void HandleRepayCompletion(bool success, const QString &message);
    void HandleRepayError(RepayErrorCode errorCode, const QString &message);
    
    // 状态控制（已简化，仅维护状态标志）
    void EnableInput(bool enabled);
    void EnablePaymentButtons(bool enabled);

    void UpdateProgressDisplay();
    

    
    // 错误处理
    void ShowErrorMessage(const QString &message);
    void ShowSuccessMessage(const QString &message);
    void ShowWarningMessage(const QString &message);
    
    // 车牌输入处理（使用FormInputPlate类）
    bool ShowPlateInputDialog();

    // 金额输入处理（使用FormInputAmount类）
    bool ShowAmountInputDialog();

     // 车型输入处理（简化为默认选择，避免外部依赖）
     bool ShowVehTypeInputDialog();

         // 补费成功确认（简化为直接结束）
    bool ShowRepaySuccessDialog(CTransPayType payType);
    
    // 显示补费完成界面
    void ShowRepayCompleteDialog();
    
    // 通知省中心（省内名单补费）
    void NotifyProvinceCenter();
    
    // 流水生成功能
    void FillTransInfo(CTransPayType payType);
    bool GenerateRepayRecord(CTransPayType payType);
    QString GetPayTypeName(CTransPayType payType);
    
    // 核心业务方法（从RepayManager移植）
    bool InitializeRepayComponents();           // 初始化补费组件
    bool ProcessCurrentRepayFlow();             // 处理当趟补费流程
    bool ProcessProvinceRepayFlow();            // 处理省内名单补费流程
    bool QueryDebtDetail();                     // 查询欠费明细
    bool ProcessPaymentInternal(CTransPayType payType); // 内部支付处理
    bool GenerateRepayRecordInternal(RepayType repayType, const QString &orderIds = ""); // 内部流水生成
    bool NotifyProvinceCenterInternal();        // 内部省中心通知
    
    // 支付方式处理（从RepayManager移植）
    bool ProcessCashPayment(int amount, const QString &vehPlate, int vehPlateColor, QString &errorMessage);
    bool ProcessMobilePayment(CTransPayType payType, int amount, const QString &vehPlate, int vehPlateColor, QString &errorMessage);
    bool ProcessETCPayment(int amount, const QString &vehPlate, int vehPlateColor, QString &errorMessage);
    
    // 验证方法（从RepayManager移植）
    bool ValidateRepayAmountInternal(int vehType, int amount);
    bool ValidateVehicleInfoInternal(const QString &vehPlate, int vehPlateColor);
    bool ValidateRepayConditions(RepayType type, const QString &vehPlate, int vehPlateColor);
    bool ValidateProvinceRepayResult(const RepayDebtQueryResult &result);
    
    // 辅助方法（从RepayManager移植）
    QString GenerateWasteId();                  // 生成流水ID
    void HandlePaymentSuccess(CTransPayType payType, int amount);  // 处理支付成功
    void HandlePaymentFailure(const QString &errorMsg);           // 处理支付失败
    void FillTransInfoInternal(CTransInfo &transInfo, const QString &vehPlate, int vehPlateColor, 
                              int vehType, int amount, RepayType repayType, CTransPayType payType);
    void LogRepayOperation(RepayType type, const QString &vehPlate, int vehPlateColor,
                          int amount, CTransPayType payType, bool success, 
                          const QString &errorMsg = "", const QString &orderIds = "",
                          const QString &listno = "", const QString &wasteId = "");

private:
    // 车型选项结构体
    struct VehTypeOption {
        int vehClass;
        QString name;
        QString description;
    };

    // 支付方式选择（使用单独页面）
    bool ShowPaymentSelection(CTransPayType &selectedPayType);

     // 展示省内欠费明细确认（简化界面）
     bool ShowDebtDetailDialog(const RepayDebtQueryResult &result);
     
     // 显示省内名单补费信息界面（新增）
     bool ShowProvinceRepayInfoDialog(const QString &vehPlate, int vehPlateColor, const RepayDebtQueryResult &result);
     
     // 省内名单补费专用流程（新增）
     void StartProvinceRepayFlow(const QString &vehPlate, int vehPlateColor, const RepayDebtQueryResult &result);

private:
    // FormRepayNew 已转为纯流程控制器，不再管理界面控件
    
    // 业务数据
    RepayType m_repayType;            // 补费类型

    QString m_vehPlate;               // 车牌号
    int m_vehPlateColor;              // 车牌颜色
    CVehClass m_vehType;                    // 车型
    int m_amount;                     // 补费金额
    RepayDebtQueryResult m_debtResult;     // 欠费查询结果
    
    // 交易流水相关
    CTransInfo m_curTransInfo;            // 当前交易信息
    CTransPayType m_currentPayType;       // 当前支付方式
    
    // 车牌输入组件已改为局部变量方式
    
    // 状态管理
    bool m_bProcessing;               // 是否正在处理
    bool m_bInputEnabled;             // 输入是否启用
    bool m_bPaymentEnabled;           // 支付按钮是否启用
    bool m_paymentInProgress;         // 支付是否正在进行
    QDateTime m_startTime;            // 开始时间
    
    // 业务组件（从RepayManager移植过来）
    ProDebPayment *m_pProDebPayment;     // 省中心接口
    AuthManager *m_pAuthManager;         // 授权管理器
    RepayConfig *m_pRepayConfig;         // 配置管理器
    
    // 定时器
    QTimer *m_pQueryTimer;            // 查询超时定时器
    bool m_bConnectionsInited;        // 信号是否已连接
    
    // 补费状态管理（从RepayManager移植）
    QString m_currentWasteId;          // 当前流水ID
    InterceptType m_currentInterceptType; // 当前拦截方式
    QString m_currentListno;           // 当前工单号
    bool m_bInitialized;               // 是否已初始化
    
    // 常量定义
    static const int QUERY_TIMEOUT = 30000;      // 查询超时时间(毫秒)
};

#endif // FORMREPAYNEW_H 
