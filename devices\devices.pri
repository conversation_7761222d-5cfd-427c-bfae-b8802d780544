INCLUDEPATH += $$PWD/..
INCLUDEPATH += $$PWD/../common
#INCLUDEPATH += $$quote($$PWD/../GmSSL 3.1.1/include)
DEPENDPATH += $$PWD/..

HEADERS += \
    $$PWD/abstractprinter.h \
    $$PWD/cardreader.h \
    $$PWD/basecardreader.h \
    $$PWD/abstractdev.h \
    $$PWD/printcontent.h \
    $$PWD/printer_jy.h \
    $$PWD/videocard.h \
    $$PWD/IOCard.h \
    $$PWD/NumConvertFunc.h \
    $$PWD/crcutil.h \
    $$PWD/VDMDev.h \
    $$PWD/fs_little.h \
    $$PWD/rsudev.h \
    $$PWD/rsudevtype.h \
    $$PWD/rsudev_gb.h \
    $$PWD/rsudev_gbef.h \
    $$PWD/VPRDev.h \
    $$PWD/faredisplayer_gb.h \
    $$PWD/smartnode.h \
    $$PWD/iotype.h \
    $$PWD/lanedevadapter.h \
    $$PWD/CardMachineDev.h \
    $$PWD/ccardmachinefx.h \
    $$PWD/vcrdev.h  \
    $$PWD/ccardmachine.h \
    $$PWD/cbasecardmgr.h \
    $$PWD/wtsysdev.h \
    $$PWD/autoextollscreen.h \
    $$PWD/PrinterDevice.h \
    $$PWD/ntddpar.h \
    $$PWD/cpaymentmachine.h \
    $$PWD/MobilePayNet.h \
    $$PWD/mobilepaytw.h \
    $$PWD/readerlib.h \
    $$PWD/mobilepay.h \
    $$PWD/invoiceqrcode.h \
    $$PWD/printer_tp.h \
    $$PWD/printer_sy300.h \
    $$PWD/speventdev.h \
    $$PWD/CusH264Struct.h \
    $$PWD/tool_func.h \
    $$PWD/libAVI/avilib.h \
    $$PWD/libAVI/cAviLib.h \
    $$PWD/mobilepaydev.h \
    $$PWD/vcrrequesthandler.h \
    $$PWD/mobilepayjx.h \
    $$PWD/vcrtypes.h \
    $$PWD/vdmlibSingleton.h \
    $$PWD/wtsysdev_zc.h \
    $$PWD/mobilepaydual.h \
    $$PWD/vehicleoutlinedev.h

SOURCES += \
    $$PWD/abstractprinter.cpp \
    $$PWD/cardreader.cpp \
    $$PWD/basecardreader.cpp \
    $$PWD/printer_jy.cpp \
    $$PWD/videocard.cpp \
    $$PWD/IOCard.cpp \
    $$PWD/NumConvertFunc.cpp \
    $$PWD/crcutil.cpp \
    $$PWD/VDMDev.cpp \
    $$PWD/fs_little.cpp \
    $$PWD/rsudev.cpp \
    $$PWD/rsudev_gb.cpp \
    $$PWD/rsudev_gbef.cpp \
    $$PWD/VPRDev.cpp \
    $$PWD/faredisplayer_gb.cpp \
    $$PWD/smartnode.cpp \
    $$PWD/iotype.cpp \
    $$PWD/lanedevadapter.cpp \
    $$PWD/CardMachineDev.cpp \
    $$PWD/ccardmachinefx.cpp \
    $$PWD/wtsysdev.cpp \
    $$PWD/vcrdev.cpp   \
    $$PWD/ccardmachine.cpp \
    $$PWD/cbasecardmgr.cpp \
    $$PWD/autoextollscreen.cpp \
    $$PWD/cpaymentmachine.cpp \
    $$PWD/PrinterDevice.cpp \
    $$PWD/MobilePayNet.cpp \
    $$PWD/readerlib.cpp \
    $$PWD/mobilepaytw.cpp \
    $$PWD/invoiceqrcode.cpp \
    $$PWD/printer_tp.cpp \
    $$PWD/printer_sy300.cpp \
    $$PWD/speventdev.cpp \
    $$PWD/CusH264Struct.cpp \
    $$PWD/tool_func.cpp \
    $$PWD/libAVI/cAviLib.cpp \
    $$PWD/libAVI/avilib.c \
    $$PWD/mobilepaydev.cpp \
    $$PWD/abstractdev.cpp \
    $$PWD/vcrrequesthandler.cpp \
    $$PWD/vdmlibsingleton.cpp \
    $$PWD/mobilepayjx.cpp \
    $$PWD/mobilepay.cpp \
    $$PWD/wtsysdev_zc.cpp \
    $$PWD/mobilepaydual.cpp \
    $$PWD/vehicleoutlinedev.cpp
