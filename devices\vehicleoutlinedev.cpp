#include "vehicleoutlinedev.h"
#include "ilogmsg.h"
#include "globalutils.h"
#include <QDebug>

// 静态成员变量初始化
bool CVehicleOutlineDev::m_bDriverLoaded = false;
QLibrary CVehicleOutlineDev::m_hLibModule;

CVehicleOutlineDev::CVehicleOutlineDev(QObject *parent)
    : CAbstractDev(parent)
    , m_pInitDev(NULL)
    , m_pGetVehInfo(NULL)
    , m_pCloseDev(NULL)
    , m_pDelVehInfo(NULL)
    , m_pGetQueueLength(NULL)
    , m_pFocusGetVehInfo(NULL)
    , m_pStatusTimer(NULL)
    , m_bDeviceConnected(false)
{
    m_sDevName = "车辆外轮廓尺寸检测仪";
    m_nDevId = DEV_VehicleOutline;
    
    // 创建状态检查定时器
    m_pStatusTimer = new QTimer(this);
    connect(m_pStatusTimer, SIGNAL(timeout()), this, SLOT(OnCheckDeviceStatus()));
    
    m_lastCheckTime = QDateTime::currentDateTime();
}

CVehicleOutlineDev::~CVehicleOutlineDev()
{
    CloseDev();
    ReleaseDriver();
}

bool CVehicleOutlineDev::LoadDriver()
{
    if (m_bDriverLoaded) {
        return true;
    }

    QString sPath = GetCurrentPath() + m_sDriver;
    m_hLibModule.setFileName(sPath);

    if (!m_hLibModule.load()) {
        m_lastError = QString("加载车辆外轮廓尺寸检测仪动态库[%1]失败").arg(sPath);
        ErrorLog(m_lastError);
        ChangeStatus(DEV_STATUS_LibErr);
        return false;
    }

    // 加载动态库函数
    m_pInitDev = (Func_InitDev)LoadFunction("InitDev");
    m_pGetVehInfo = (Func_GetVehInfo)LoadFunction("GetVehInfo");
    m_pCloseDev = (Func_CloseDev)LoadFunction("CloseDev");
    m_pDelVehInfo = (Func_DelVehInfo)LoadFunction("DelVehInfo");
    m_pGetQueueLength = (Func_GetQueueLength)LoadFunction("GetQueueLength");
    m_pFocusGetVehInfo = (Func_FocusGetVehInfo)LoadFunction("FocusGetVehInfo");

    if (!m_pInitDev || !m_pGetVehInfo || !m_pCloseDev || 
        !m_pDelVehInfo || !m_pGetQueueLength || !m_pFocusGetVehInfo) {
        ErrorLog(QString("获取车辆外轮廓尺寸检测仪动态库[%1]中一个或多个函数失败").arg(sPath));
        ReleaseDriver();
        ChangeStatus(DEV_STATUS_LibErr);
        return false;
    }

    m_bDriverLoaded = true;
    DebugLog(QString("车辆外轮廓尺寸检测仪动态库[%1]加载成功").arg(sPath));
    return true;
}

void CVehicleOutlineDev::ReleaseDriver()
{
    if (m_bDriverLoaded) {
        m_hLibModule.unload();
        m_bDriverLoaded = false;
    }

    m_pInitDev = NULL;
    m_pGetVehInfo = NULL;
    m_pCloseDev = NULL;
    m_pDelVehInfo = NULL;
    m_pGetQueueLength = NULL;
    m_pFocusGetVehInfo = NULL;
}

bool CVehicleOutlineDev::StartDev()
{
    if (!LoadDriver()) {
        return false;
    }

    if (!m_pInitDev) {
        SetLastError("InitDev函数指针为空");
        ChangeStatus(DEV_STATUS_LibErr);
        return false;
    }

    // 初始化设备
    int result = m_pInitDev(m_sConnStr1.toLocal8Bit().data(), m_sConnStr2.toLocal8Bit().data());
    if (result != 0) {
        SetLastError(QString("初始化车辆外轮廓尺寸检测仪失败，错误码：%1").arg(result));
        ChangeStatus(DEV_STATUS_CommErr);
        return false;
    }

    m_bDeviceConnected = true;
    ChangeStatus(DEV_STATUS_OK);
    
    // 启动状态检查定时器，每5秒检查一次
    m_pStatusTimer->start(5000);
    
    DebugLog(QString("车辆外轮廓尺寸检测仪初始化成功，连接参数：%1, %2")
             .arg(m_sConnStr1, m_sConnStr2));
    return true;
}

void CVehicleOutlineDev::CloseDev()
{
    if (m_pStatusTimer) {
        m_pStatusTimer->stop();
    }

    if (m_pCloseDev && m_bDeviceConnected) {
        m_pCloseDev();
        m_bDeviceConnected = false;
        DebugLog("车辆外轮廓尺寸检测仪设备已关闭");
    }

    ChangeStatus(DEV_STATUS_Offline);
}

bool CVehicleOutlineDev::GetVehicleOutlineInfo(int index, VehicleOutlineData &data)
{
    if (!m_pGetVehInfo || !m_bDeviceConnected) {
        SetLastError("设备未连接或函数指针为空");
        return false;
    }

    int length = 0, width = 0, height = 0;
    int result = m_pGetVehInfo(index, &length, &width, &height);
    
    if (result == 0) {
        data.length = length;
        data.width = width;
        data.height = height;
        data.timestamp = QDateTime::currentDateTime();
        
        DebugLog(QString("获取车辆外轮廓尺寸数据成功，索引：%1，长宽高：%2x%3x%4 分米")
                 .arg(index).arg(length).arg(width).arg(height));
        return true;
    } else {
        SetLastError(QString("获取车辆外轮廓尺寸数据失败，索引：%1，错误码：%2").arg(index).arg(result));
        return false;
    }
}

bool CVehicleOutlineDev::GetLatestVehicleOutlineInfo(VehicleOutlineData &data)
{
    int queueLength = GetQueueLength();
    if (queueLength <= 0) {
        return false;
    }
    
    // 获取最新的数据（队列中的第一个）
    return GetVehicleOutlineInfo(0, data);
}

bool CVehicleOutlineDev::DeleteVehicleOutlineInfo(int index)
{
    if (!m_pDelVehInfo || !m_bDeviceConnected) {
        SetLastError("设备未连接或函数指针为空");
        return false;
    }

    int result = m_pDelVehInfo(index);
    if (result == 0) {
        DebugLog(QString("删除车辆外轮廓尺寸数据成功，索引：%1").arg(index));
        return true;
    } else {
        SetLastError(QString("删除车辆外轮廓尺寸数据失败，索引：%1，错误码：%2").arg(index).arg(result));
        return false;
    }
}

int CVehicleOutlineDev::GetQueueLength()
{
    if (!m_pGetQueueLength || !m_bDeviceConnected) {
        return -1;
    }

    return m_pGetQueueLength();
}

bool CVehicleOutlineDev::ForceGetVehicleOutlineInfo(VehicleOutlineData &data)
{
    if (!m_pFocusGetVehInfo || !m_bDeviceConnected) {
        SetLastError("设备未连接或函数指针为空");
        return false;
    }

    int length = 0, width = 0, height = 0;
    int result = m_pFocusGetVehInfo(&length, &width, &height);
    
    if (result == 0) {
        data.length = length;
        data.width = width;
        data.height = height;
        data.timestamp = QDateTime::currentDateTime();
        
        DebugLog(QString("强制获取车辆外轮廓尺寸数据成功，长宽高：%1x%2x%3 分米")
                 .arg(length).arg(width).arg(height));
        return true;
    } else {
        SetLastError(QString("强制获取车辆外轮廓尺寸数据失败，错误码：%1").arg(result));
        return false;
    }
}

void CVehicleOutlineDev::ClearAllResults()
{
    int queueLength = GetQueueLength();
    for (int i = queueLength - 1; i >= 0; i--) {
        DeleteVehicleOutlineInfo(i);
    }
    
    QMutexLocker locker(&m_dataMutex);
    m_dataCache.clear();
    
    DebugLog("清空所有车辆外轮廓尺寸识别结果");
}

bool CVehicleOutlineDev::CheckDeviceStatus()
{
    // 简单的设备状态检查，可以根据实际需要扩展
    if (!m_bDeviceConnected) {
        return false;
    }
    
    // 尝试获取队列长度来检查设备是否正常响应
    int queueLength = GetQueueLength();
    return queueLength >= 0;
}

void CVehicleOutlineDev::OnCheckDeviceStatus()
{
    bool currentStatus = CheckDeviceStatus();
    
    if (currentStatus != m_bDeviceConnected) {
        m_bDeviceConnected = currentStatus;
        
        if (currentStatus) {
            ChangeStatus(DEV_STATUS_OK);
            DebugLog("车辆外轮廓尺寸检测仪设备连接恢复正常");
        } else {
            ChangeStatus(DEV_STATUS_CommErr);
            ErrorLog("车辆外轮廓尺寸检测仪设备连接异常");
        }
    }
    
    // 检查是否有新数据
    CheckAndEmitNewData();
    
    m_lastCheckTime = QDateTime::currentDateTime();
}

void* CVehicleOutlineDev::LoadFunction(const char* funcName)
{
    void *pFunc = m_hLibModule.resolve(funcName);
    if (!pFunc) {
        ErrorLog(QString("动态库缺少函数[%1]").arg(funcName));
    }
    return pFunc;
}

void CVehicleOutlineDev::UpdateDataCache()
{
    QMutexLocker locker(&m_dataMutex);
    
    int queueLength = GetQueueLength();
    if (queueLength <= 0) {
        return;
    }
    
    // 更新缓存，只保留最新的数据
    m_dataCache.clear();
    for (int i = 0; i < queueLength; i++) {
        VehicleOutlineData data;
        if (GetVehicleOutlineInfo(i, data)) {
            m_dataCache.append(data);
        }
    }
}

void CVehicleOutlineDev::CheckAndEmitNewData()
{
    int queueLength = GetQueueLength();
    if (queueLength > 0) {
        VehicleOutlineData data;
        if (GetLatestVehicleOutlineInfo(data) && data.isValid()) {
            emit OnVehicleOutlineData(data);
        }
    }
}
