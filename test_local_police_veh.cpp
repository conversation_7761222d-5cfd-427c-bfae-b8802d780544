/**
 * @file test_local_police_veh.cpp
 * @brief 本地ETC警车判断函数测试示例
 * <AUTHOR> Assistant
 * @date 2025-08-20
 */

#include <QCoreApplication>
#include <QDebug>
#include <QString>
#include "vehplatefunc.h"
#include "transinfo.h"

/**
 * @brief 测试通用的本地ETC警车判断函数
 */
void testIsLocalETCPoliceVeh()
{
    qDebug() << "=== 测试通用本地ETC警车判断函数 ===";
    
    // 测试用例1：包含"赣O"的车牌
    QString testPlate1 = "赣O12345";
    bool result1 = IsLocalETCPoliceVeh(testPlate1);
    qDebug() << QString("车牌: %1, 是否本地警车: %2").arg(testPlate1).arg(result1 ? "是" : "否");
    
    // 测试用例2：包含"赣O"的车牌（不同位置）
    QString testPlate2 = "赣O警1234";
    bool result2 = IsLocalETCPoliceVeh(testPlate2);
    qDebug() << QString("车牌: %1, 是否本地警车: %2").arg(testPlate2).arg(result2 ? "是" : "否");
    
    // 测试用例3：不包含"赣O"的车牌
    QString testPlate3 = "赣A12345";
    bool result3 = IsLocalETCPoliceVeh(testPlate3);
    qDebug() << QString("车牌: %1, 是否本地警车: %2").arg(testPlate3).arg(result3 ? "是" : "否");
    
    // 测试用例4：其他省份的警车
    QString testPlate4 = "京A警1234";
    bool result4 = IsLocalETCPoliceVeh(testPlate4);
    qDebug() << QString("车牌: %1, 是否本地警车: %2").arg(testPlate4).arg(result4 ? "是" : "否");
    
    // 测试用例5：空车牌
    QString testPlate5 = "";
    bool result5 = IsLocalETCPoliceVeh(testPlate5);
    qDebug() << QString("车牌: %1, 是否本地警车: %2").arg(testPlate5.isEmpty() ? "空" : testPlate5).arg(result5 ? "是" : "否");
    
    qDebug() << "";
}

/**
 * @brief 测试CTransInfo类的本地ETC警车判断函数
 */
void testCTransInfoIsLocalETCPoliceVeh()
{
    qDebug() << "=== 测试CTransInfo类本地ETC警车判断函数 ===";
    
    CTransInfo transInfo;
    
    // 模拟设置OBU车牌信息
    QString testOBUPlate = "赣O12345";
    QByteArray obuPlateData = testOBUPlate.toLocal8Bit();
    memset(transInfo.VehInfo.szVehPlate, 0, sizeof(transInfo.VehInfo.szVehPlate));
    memcpy(transInfo.VehInfo.szVehPlate, obuPlateData.constData(), 
           qMin(obuPlateData.size(), (int)sizeof(transInfo.VehInfo.szVehPlate) - 1));
    
    // 模拟设置ETC卡内车牌信息
    QString testETCPlate = "赣O警6789";
    QByteArray etcPlateData = testETCPlate.toLocal8Bit();
    memset(transInfo.OBUVehInfo.szVehPlate, 0, sizeof(transInfo.OBUVehInfo.szVehPlate));
    memcpy(transInfo.OBUVehInfo.szVehPlate, etcPlateData.constData(), 
           qMin(etcPlateData.size(), (int)sizeof(transInfo.OBUVehInfo.szVehPlate) - 1));
    
    QString resultPlate;
    bool isLocalPolice = transInfo.IsLocalETCPoliceVeh(resultPlate);
    
    qDebug() << QString("OBU车牌: %1").arg(testOBUPlate);
    qDebug() << QString("ETC卡内车牌: %1").arg(testETCPlate);
    qDebug() << QString("判断结果: %1").arg(isLocalPolice ? "是本地警车" : "不是本地警车");
    qDebug() << QString("匹配的车牌: %1").arg(resultPlate);
    
    qDebug() << "";
}

/**
 * @brief 主函数
 */
int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "本地ETC警车判断函数测试";
    qDebug() << "判断条件：OBU车牌或ETC卡内车牌(B3帧信息)包含\"赣O\"字符串";
    qDebug() << "";
    
    // 测试通用函数
    testIsLocalETCPoliceVeh();
    
    // 测试CTransInfo类函数
    testCTransInfoIsLocalETCPoliceVeh();
    
    qDebug() << "测试完成";
    
    return 0;
}
