#include "formrepaytypeselect.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QPainter>

#include "../../globalui.h"
#include "../../dlgmain.h"
#include "../../log4qt/ilogmsg.h"

FormRepayTypeSelect::FormRepayTypeSelect(QWidget *parent)
    : CBaseOpWidget(parent)
    , m_lblTitle(0)
    , m_lblOpt1(0)
    , m_lblOpt2(0)
    , m_lblHelp(0)
    , m_selectedType(RepayType_None)
    , m_hasSelection(false)
    , m_currentIndex(0)
{
    setObjectName(QString("FormRepayTypeSelect"));
}

FormRepayTypeSelect::~FormRepayTypeSelect()
{
}

void FormRepayTypeSelect::InitUI(int iFlag)
{
    Q_UNUSED(iFlag);
    CBaseOpWidget::InitUI();
    SetTitle("");
    // 初始化字体与颜色
    m_fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize, QFont::Bold);
    m_fontOption = QFont(g_GlobalUI.m_FontName, 18, QFont::Normal);
    m_fontHelp = QFont(g_GlobalUI.m_FontName, 12);
    m_colorBackground = g_GlobalUI.m_ColorBackground;
    m_colorNormal = QColor(0, 0, 0);
    m_colorSelected = QColor(0, 120, 215);
    BuildUI();
    UpdateHint();
}

bool FormRepayTypeSelect::ShowSelect(RepayType &selectedType)
{
    // 进入模态显示
    int r = doModalShow(false, 0);
    if (r == Rlt_OK && m_hasSelection) {
        selectedType = m_selectedType;
        return true;
    }
    return false;
}

void FormRepayTypeSelect::OnModalShowed()
{
    CBaseOpWidget::OnModalShowed();
    UpdateHint();
    SetCurrentSelection(0);
}

int FormRepayTypeSelect::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (!mtcKeyEvent) return 0;
    if (!mtcKeyEvent->isFuncKey()) return CBaseOpWidget::mtcKeyPressed(mtcKeyEvent);

    // 使用 func() 判断功能键
    int keyCode = mtcKeyEvent->func();
    switch (keyCode) {
    case KeyEsc:
        OnCancelClicked();
        return 1;
    case Key1:  // 数字键1：只改变选中项的高亮，不立即确认
        SetCurrentSelection(0);
        return 1;
    case Key2:  // 数字键2：只改变选中项的高亮，不立即确认
        SetCurrentSelection(1);
        return 1;
    case KeyConfirm:  // 确认当前高亮项
        if (m_currentIndex == 0) OnSelectCurrent(); else OnSelectProvince();
        return 1;
    default:
        break;
    }
    return CBaseOpWidget::mtcKeyPressed(mtcKeyEvent);
}

void FormRepayTypeSelect::OnSelectCurrent()
{
    m_selectedType = RepayType_Current;
    m_hasSelection = true;
    //SetMessage(QString::fromUtf8("已选择：当趟补费"));
    OnOk();
}

void FormRepayTypeSelect::OnSelectProvince()
{
    m_selectedType = RepayType_Province;
    m_hasSelection = true;
    //SetMessage(QString::fromUtf8("已选择：省内名单补费"));
    OnOk();
}

void FormRepayTypeSelect::OnCancelClicked()
{
    m_hasSelection = false;
    OnCancel();
}

void FormRepayTypeSelect::BuildUI()
{
    // 使用标签文本还原图片样式
    QVBoxLayout *vLayout = new QVBoxLayout();
    vLayout->setContentsMargins(40, 40, 40, 40);
    vLayout->setSpacing(40);

    m_lblTitle = new QLabel(QString::fromUtf8("补费方式"), this);
    m_lblTitle->setAlignment(Qt::AlignHCenter | Qt::AlignVCenter);
    m_lblTitle->setFont(m_fontTitle);

    m_lblOpt1 = new QLabel(QString::fromUtf8("        1、 当趟补费"), this);
    m_lblOpt1->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    m_lblOpt1->setFont(m_fontOption);

    m_lblOpt2 = new QLabel(QString::fromUtf8("        2、 省内名单补费"), this);
    m_lblOpt2->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    m_lblOpt2->setFont(m_fontOption);

    m_lblHelp = new QLabel(this);
    m_lblHelp->setAlignment(Qt::AlignHCenter | Qt::AlignVCenter);
    m_lblHelp->setFont(m_fontHelp);
    m_lblHelp->setStyleSheet("color: rgb(100, 100, 100);");

    vLayout->addStretch(1);
    vLayout->addWidget(m_lblTitle);
    vLayout->addStretch(1);
    vLayout->addWidget(m_lblOpt1);
    vLayout->addWidget(m_lblOpt2);
    vLayout->addStretch(2);
    vLayout->addWidget(m_lblHelp);
    vLayout->addStretch(1);

    setLayout(vLayout);
}

void FormRepayTypeSelect::UpdateHint()
{
    if (m_lblHelp) {
        m_lblHelp->setText(QString::fromUtf8("请按数字键选择  按【确认】键继续  按【ESC】键取消"));
    }
}

void FormRepayTypeSelect::SetCurrentSelection(int index)
{
    m_currentIndex = index;
    if (m_lblOpt1 && m_lblOpt2) {
        // 普通/高亮样式：通过背景色区分，高亮为蓝底白字
        QString normal   = "color: rgb(0,0,0); background-color: transparent; padding: 6px 12px; border-radius: 6px;";
        QString selected = "color: rgb(255,255,255); background-color: rgb(0,120,215); padding: 6px 12px; border-radius: 6px;";
        if (index == 0) {
            m_lblOpt1->setStyleSheet(selected);
            m_lblOpt2->setStyleSheet(normal);
        } else {
            m_lblOpt1->setStyleSheet(normal);
            m_lblOpt2->setStyleSheet(selected);
        }
    }
}


