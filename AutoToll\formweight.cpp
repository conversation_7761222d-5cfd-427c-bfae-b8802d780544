#include "formweight.h"
#include "globalui.h"
#include "lanetype.h"
#include <QPainter>
#include <QHeaderView>
#include "laneinfo.h"
//计重限速
#define alarmSpeed 30

FormWeight::FormWeight(QWidget *parent) : QWidget(parent)
{
    //显示当前车辆计重信息
    m_pTblWtInfo =  new QTableWidget(this);
    //显示当前车辆轴信息
    m_pTblAxleInfo = new QTableWidget(this);
    //计重状态
    m_bWeightDevNormal = true;
    //车辆外轮廓设备状态：0-无设备，1-设备正常，2-设备异常
    m_nVehicleOutlineDevStatus = 0;
}

FormWeight::~FormWeight()
{
    //显示当前车辆计重信息
    delete m_pTblWtInfo;
    //显示当前车辆轴信息
    delete m_pTblAxleInfo;
}

void FormWeight::InitWtInfoTable()
{
    /****************以下为表格的设置*********************************/
    QFont fontText(g_GlobalUI.m_FontName, g_GlobalUI.m_FontSize);
    m_pTblWtInfo->setFont(fontText);
//    //设置背景
//    QPalette pal  = m_pTblWtInfo->palette();
//    pal.setBrush(QPalette::Base, g_GlobalUI.m_ColorBackground);
//    m_pTblWtInfo->setPalette(pal);
    //自动列宽
//    m_pTblAxleInfo->resizeColumnsToContents();
//    m_pTblParam->resizeRowsToContents();
    //设置平分
    m_pTblWtInfo->horizontalHeader()->setResizeMode(QHeaderView::Stretch);
    //拉伸最后一行
    m_pTblWtInfo->horizontalHeader()->setStretchLastSection(true);
    //不显示表头
    m_pTblWtInfo->horizontalHeader()->hide();
    m_pTblWtInfo->verticalHeader()->hide();
    //隐藏滚动条
    m_pTblWtInfo->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    //内容自动换行
    m_pTblWtInfo->setWordWrap(true);
    //整行选择
    m_pTblWtInfo->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pTblWtInfo->setSelectionMode(QAbstractItemView::NoSelection);
    m_pTblWtInfo->setEditTriggers(QAbstractItemView::NoEditTriggers);
    //不处理按键
    m_pTblWtInfo->setFocusPolicy(Qt::NoFocus);
    //隐藏网络线
//    m_pTblEntryInfo->setShowGrid(false);

    //设置行列数
    m_pTblWtInfo->setColumnCount(8);
    m_pTblWtInfo->setRowCount(2);

    //计算行高
    int rowHeight = m_pTblWtInfo->height() / 2 -1;
    m_pTblWtInfo->setRowHeight(0, rowHeight);
    m_pTblWtInfo->setRowHeight(1, rowHeight);


    //设置固定显示内容
    m_pTblWtInfo->setItem(0,0, new QTableWidgetItem("轴组"));
    m_pTblWtInfo->item(0,0)->setTextAlignment(Qt::AlignCenter);
    m_pTblWtInfo->setItem(0,2, new QTableWidgetItem("总重"));
    m_pTblWtInfo->item(0,2)->setTextAlignment(Qt::AlignCenter);
    m_pTblWtInfo->setItem(0,4, new QTableWidgetItem("超限"));
    m_pTblWtInfo->item(0,4)->setTextAlignment(Qt::AlignCenter);
    m_pTblWtInfo->setItem(0,6, new QTableWidgetItem("车速"));
    m_pTblWtInfo->item(0,6)->setTextAlignment(Qt::AlignCenter);
    m_pTblWtInfo->setItem(1,0, new QTableWidgetItem("限载"));
    m_pTblWtInfo->item(1,0)->setTextAlignment(Qt::AlignCenter);
    m_pTblWtInfo->setItem(1,2, new QTableWidgetItem("长度"));
    m_pTblWtInfo->item(1,2)->setTextAlignment(Qt::AlignCenter);
    m_pTblWtInfo->setItem(1,4, new QTableWidgetItem("宽度"));
    m_pTblWtInfo->item(1,4)->setTextAlignment(Qt::AlignCenter);
    m_pTblWtInfo->setItem(1,6, new QTableWidgetItem("高度"));
    m_pTblWtInfo->item(1,6)->setTextAlignment(Qt::AlignCenter);
}

void FormWeight::InitAxleInfoTable()
{
    /****************以下为表格的设置*********************************/
    QFont fontText(g_GlobalUI.m_FontName, g_GlobalUI.m_FontSize);
    m_pTblAxleInfo->setFont(fontText);
//    //设置背景
    QPalette pal  = m_pTblAxleInfo->palette();
    pal.setBrush(QPalette::Base, g_GlobalUI.m_ColorBackground);
    m_pTblAxleInfo->setPalette(pal);
    //自动列宽
//    m_pTblAxleInfo->resizeColumnsToContents();
//    m_pTblParam->resizeRowsToContents();
    //设置平分
    m_pTblAxleInfo->horizontalHeader()->setResizeMode(QHeaderView::Stretch);
    //拉伸最后一行
    m_pTblAxleInfo->horizontalHeader()->setStretchLastSection(true);
    //不显示表头
    m_pTblAxleInfo->horizontalHeader()->hide();
    m_pTblAxleInfo->verticalHeader()->hide();
    //隐藏滚动条
    m_pTblAxleInfo->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    //内容自动换行
    m_pTblAxleInfo->setWordWrap(true);
    //整行选择
    m_pTblAxleInfo->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pTblAxleInfo->setSelectionMode(QAbstractItemView::NoSelection);
    m_pTblAxleInfo->setEditTriggers(QAbstractItemView::NoEditTriggers);
    //不处理按键
    m_pTblAxleInfo->setFocusPolicy(Qt::NoFocus);
    //隐藏网络线
//    m_pTblEntryInfo->setShowGrid(false);

    //设置行列数
    m_pTblAxleInfo->setColumnCount(8);
    m_pTblAxleInfo->setRowCount(2);

    //计算行高
    int rowHeight = m_pTblAxleInfo->height() / 2 -1;
    m_pTblAxleInfo->setRowHeight(0, rowHeight);
    m_pTblAxleInfo->setRowHeight(1, rowHeight);


    //设置固定显示内容
    m_pTblAxleInfo->setItem(0,0, new QTableWidgetItem("首车轴型"));
    m_pTblAxleInfo->setItem(1,0, new QTableWidgetItem("首车轴重"));
    m_pTblAxleInfo->item(0,0)->setTextAlignment(Qt::AlignCenter);
    m_pTblAxleInfo->item(1,0)->setTextAlignment(Qt::AlignCenter);

    //设置单元格
//    m_pTblTradeInfo->setStyleSheet("QTableWidget::item{padding-left:2px;padding-right:2px;");

    //计算列宽
//    m_pTblEntryInfo->setColumnWidth(0, g_GlobalUI.vehif_LabelRowWidth);
//    m_pTblEntryInfo->setColumnWidth(2, g_GlobalUI.vehif_LabelRowWidth);
//    int colWidth = (m_pTblEntryInfo->rect().width()- m_pTblEntryInfo->columnWidth(0) - m_pTblEntryInfo->columnWidth(2) - 2)/2;
//    m_pTblEntryInfo->setColumnWidth(1, colWidth);
//    m_pTblEntryInfo->setColumnWidth(3, colWidth);


}

void FormWeight::paintEvent(QPaintEvent *)
{
    if (!g_GlobalUI.m_bWndWeightShow){
        return;
    }
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);
    //加载图标
    QSize iconSize =  g_GlobalUI.wt_iconSize;
    QPixmap pmIcon(iconSize);
    pmIcon.load(":/images/others/carwt.png");
    //绘制背景色
    QColor clrBackground = g_GlobalUI.m_ColorBackground;
    painter.setBrush(clrBackground);
    painter.setPen(Qt::black);
    painter.drawRect(rectClient.x(), rectClient.y(), rectClient.width()-1, rectClient.height()-1);

    //边距
    int margin = 2;
    //车重文字区高度（每辆车头上带重量文字描述)
    int textHeight = g_GlobalUI.wt_labelHeight;

    //绘制路的高度为图标大小的1/4
    int roadHeight = iconSize.height() / 4;


    //状态文字高度
    int stateHeight = margin + textHeight + iconSize.height() - roadHeight / 2;
    int textTop = g_GlobalUI.wt_tableHeight * 2 + margin;
    //左右各空出显示宽度，用来显示车辆数 和 设备状态
    int countWidth = 40;
    QRect rctTextCount(margin, textTop, countWidth, stateHeight -1);
    painter.drawRect(rctTextCount);

    //加载文字字体
    QFont fontLabel(g_GlobalUI.m_FontName, g_GlobalUI.m_FontSize);
    painter.setPen(Qt::black);
    //绘制“车辆数”三个字
    QRect rctLableCount(margin, textTop, countWidth, textHeight);
    painter.setBrush(QColor(195, 195, 195));
    painter.drawRect(rctLableCount);
    painter.setFont(fontLabel);
    painter.drawText(rctLableCount, Qt::AlignCenter, QString("车辆数"));

    rctTextCount.adjust(0, rctLableCount.height(), 0, 0);
    fontLabel.setBold(true);
    painter.setFont(fontLabel);
    QString sCarCount = QString("%1").arg(m_lstVeh.size());
    painter.drawText(rctTextCount, Qt::AlignCenter, sCarCount);

    //设备状态
    QString sDevState = "计重\n正常";
    QColor clDevState = Qt::black;
    fontLabel.setBold(false);
    if (!m_bWeightDevNormal){
        sDevState = "计重\n异常";
        clDevState = Qt::red;
        fontLabel.setBold(true);
    }
    painter.setPen(clDevState);
    painter.setFont(fontLabel);

    QRect rctTextState = QRect(rect().width() - margin - countWidth, textTop, countWidth-1, stateHeight -1);
    painter.drawRect(rctTextState);
    painter.drawText(rctTextState, Qt::AlignCenter, sDevState);

    //车辆外轮廓设备状态
    QString sOutlineDevState;
    QColor clOutlineDevState = Qt::black;
    fontLabel.setBold(false);

    switch (m_nVehicleOutlineDevStatus) {
        case 0:  // 无设备
            sOutlineDevState = "无外廓";
            clOutlineDevState = Qt::gray;
            break;
        case 1:  // 设备正常
            sOutlineDevState = "外廓正常";
            clOutlineDevState = Qt::black;
            break;
        case 2:  // 设备异常
            sOutlineDevState = "外廓异常";
            clOutlineDevState = Qt::red;
            fontLabel.setBold(true);
            break;
        default:
            sOutlineDevState = "无外廓";
            clOutlineDevState = Qt::gray;
            break;
    }

    painter.setPen(clOutlineDevState);
    painter.setFont(fontLabel);

    // 在称重状态下方显示车辆外轮廓设备状态
    QRect rctOutlineState = QRect(rect().width() - margin - countWidth, textTop + stateHeight, countWidth-1, stateHeight -1);
    painter.drawRect(rctOutlineState);
    painter.drawText(rctOutlineState, Qt::AlignCenter, sOutlineDevState);

    //图标显示起始高度
    int iconTop = textTop + textHeight;

    //路显示的起始高度
    int roadTop = iconTop + iconSize.height() - roadHeight / 2;
    QRect rctRoad = QRect(margin, roadTop, rect().width()-2*margin, roadHeight);
    //绘制背景色
    QColor clrRoad(127, 127, 127);
    painter.setBrush(clrRoad);
    painter.setPen(Qt::black);
    painter.drawRect(rctRoad);
    //绘制中线
    painter.setBrush(Qt::white);
    painter.setPen(Qt::NoPen);
    QRect rctSmall(rctRoad.left(), 0, 5, 2);
    int x = rctRoad.left() + 1;
    for(int i=0; i<= rctRoad.width()/10; i++){
        rctSmall.moveTo(x, rctRoad.top() + rctRoad.height()/2-1);
        painter.drawRect(rctSmall);
        x += 10;
    }

    //车辆区总高度
    int carZoneHeight = margin + textHeight + roadHeight / 2 + iconSize.height();
    //车辆区总宽度
    int carZoneWidth = rect().width()- 2*margin - 2*countWidth;
    int iconLeft = margin + countWidth;

    //图标间距
    int iconSpace = 10;
    //可显示文字的宽度
    int textWidth = iconSize.width() + 2*iconSpace;

    //绘制车辆图标及文字
    for(int i=0; i<m_lstVeh.size(); i++){
        VehWeightItem item = m_lstVeh.at(i);
        int iconX = iconLeft + i*textWidth;
        if (iconX + textWidth > carZoneWidth + iconLeft){
            //超出了显示范围
            break;
        }
        //绘制文字
        QString sText =  QString("%1T(%2)").arg(QString::number(item.totalWeight/1000.0,'f',1)).arg(item.axleCount);
        QRect rctLable(iconX, textTop, textWidth, textHeight);
        painter.setFont(fontLabel);
        painter.setPen(Qt::black);
        painter.drawText(rctLable, Qt::AlignCenter, sText);
//        //绘制小车图标
        painter.drawPixmap(iconX + iconSpace, iconTop,
                           iconSize.width(), iconSize.height(),
                           pmIcon);

    }
    painter.end();
    QPainter ptThis(this);
    ptThis.drawPixmap(rectClient, pixmap);
}



void FormWeight::InitUI()
{
    //初始化界面
    setGeometry(g_GlobalUI.m_RectWndWeight);

    int margin=1;
    //计重信息表格
    QRect rctTable(margin, margin, rect().width()-2*margin, g_GlobalUI.wt_tableHeight);
    m_pTblWtInfo->setGeometry(rctTable);
    //轴组信息表格
    rctTable.moveTo(margin, margin+g_GlobalUI.wt_tableHeight);
    m_pTblAxleInfo->setGeometry(rctTable);

    InitAxleInfoTable();
    InitWtInfoTable();

}

void FormWeight::ClearWeightInfoShow()
{
    QString sEmpty = "";
    //轴组
    m_pTblWtInfo->setItem(0,1, new QTableWidgetItem(sEmpty));
    //总重
    m_pTblWtInfo->setItem(0,3, new QTableWidgetItem(sEmpty));
    //超限
    m_pTblWtInfo->setItem(0,5, new QTableWidgetItem(sEmpty));
    //时速
    m_pTblWtInfo->setItem(0,7, new QTableWidgetItem(sEmpty));
    //轴限
    m_pTblWtInfo->setItem(1,1, new QTableWidgetItem(sEmpty));
    //最轻轴
    m_pTblWtInfo->setItem(1,3, new QTableWidgetItem(sEmpty));

    //先填充轴组显示的小横线
    for(int i=1; i<=MAX_INPUT_AXIS_COUNT-3/*原来是7*/; i++){
        //轴组类型
        m_pTblAxleInfo->setItem(0,i, new QTableWidgetItem(sEmpty));
        //轴组重量
        m_pTblAxleInfo->setItem(1,i, new QTableWidgetItem(sEmpty));
    }
    m_lstVeh.clear();
    update();

}

void FormWeight::AddVehWeightInfo(const VehWeightItem item)
{
    m_lstVeh.append(item);
    update();
}

void FormWeight::ShowVehWeight(const CVehAxisInfo *pWt, bool bSpecial)
{
    //根据信息显示
    //轴组
    QString sAxisType = QString::number(pWt->GetConfirmedAxisGroup());
    m_pTblWtInfo->setItem(0,1, new QTableWidgetItem(sAxisType));
    m_pTblWtInfo->item(0,1)->setTextAlignment(Qt::AlignCenter);
    //总重
    QString sWeight = QString("%1T").arg(QString::number(pWt->GetConfirmedTotalRawWeight()/1000.0,'f',2));
    m_pTblWtInfo->setItem(0,3, new QTableWidgetItem(sWeight));
    m_pTblWtInfo->item(0,3)->setTextAlignment(Qt::AlignCenter);
    //超限
    int nOverRate = pWt->GetOverWeightRate();
    if (bSpecial){
        //取超限作业车
        nOverRate = pWt->GetOverWeightRateForSpecial();
    }
    QString sOverRate = QString("%1%").arg(QString::number(nOverRate/10.0,'f',1));
    m_pTblWtInfo->setItem(0,5, new QTableWidgetItem(sOverRate));
    m_pTblWtInfo->item(0,5)->setTextAlignment(Qt::AlignCenter);
    if (nOverRate>50){
        m_pTblWtInfo->item(0,5)->setForeground(QBrush(Qt::red));
    } else {
        m_pTblWtInfo->item(0,5)->setForeground(QBrush(Qt::black));
    }
    //时速
    QString sSpeed = QString("%1km/h").arg(QString::number(pWt->GetSpeed(),'f',1));
    m_pTblWtInfo->setItem(0,7, new QTableWidgetItem(sSpeed));
    m_pTblWtInfo->item(0,7)->setTextAlignment(Qt::AlignCenter);
    //轴限
    quint32 nLimit = pWt->GetWeightStandard();
    if (bSpecial){
        //取超限作业车
        nLimit = pWt->GetWeightStandardForSpecial();
    }
    QString sLimit = QString("%1T").arg(QString::number(nLimit/1000.0,'f',2));
    m_pTblWtInfo->setItem(1,1, new QTableWidgetItem(sLimit));
    m_pTblWtInfo->item(1,1)->setTextAlignment(Qt::AlignCenter);

    //车辆外轮廓尺寸信息（单位：米）
    QString sLength = "-";
    QString sWidth = "-";
    QString sHeight = "-";

    if (pWt->GetVehLength() > 0) {
        sLength = QString("%1m").arg(QString::number(pWt->GetVehLengthInMeter(), 'f', 1));
    }
    if (pWt->GetVehWidth() > 0) {
        sWidth = QString("%1m").arg(QString::number(pWt->GetVehWidthInMeter(), 'f', 1));
    }
    if (pWt->GetVehHeight() > 0) {
        sHeight = QString("%1m").arg(QString::number(pWt->GetVehHeightInMeter(), 'f', 1));
    }

    m_pTblWtInfo->setItem(1,3, new QTableWidgetItem(sLength));
    m_pTblWtInfo->item(1,3)->setTextAlignment(Qt::AlignCenter);
    m_pTblWtInfo->setItem(1,5, new QTableWidgetItem(sWidth));
    m_pTblWtInfo->item(1,5)->setTextAlignment(Qt::AlignCenter);
    m_pTblWtInfo->setItem(1,7, new QTableWidgetItem(sHeight));
    m_pTblWtInfo->item(1,7)->setTextAlignment(Qt::AlignCenter);

    //先填充轴组显示的小横线
    for(int i=1; i<=MAX_INPUT_AXIS_COUNT-3/*原来是7*/; i++){
        //轴组类型
        m_pTblAxleInfo->setItem(0,i, new QTableWidgetItem("-"));
        m_pTblAxleInfo->item(0,i)->setTextAlignment(Qt::AlignCenter);
        //轴组重量
        m_pTblAxleInfo->setItem(1,i, new QTableWidgetItem("-"));
        m_pTblAxleInfo->item(1,i)->setTextAlignment(Qt::AlignCenter);
    }

    //显示轴组
    QList<CAxisData> *pAgList = new QList<CAxisData>();
    // 获取确认后的轴重信息
    pWt->GetConfirmedAxisList(pAgList);
    //如果为空，不显示
    if (pAgList== 0 || pAgList->size() == 0){
        return;
    }
    int nCol = 1;
    for(int i=0; i<pAgList->size(); i++){
        //轴组类型
        QString sAgName = GetAxisNumForName(pAgList->at(i).m_nAxisType);
        m_pTblAxleInfo->setItem(0,nCol, new QTableWidgetItem(sAgName));
        m_pTblAxleInfo->item(0,nCol)->setTextAlignment(Qt::AlignCenter);
        //轴组重量
        QString sAgWeight = QString("%1T").arg(QString::number(pAgList->at(i).m_nAxisWeight/1000.0,'f',1)); ;
        m_pTblAxleInfo->setItem(1,nCol, new QTableWidgetItem(sAgWeight));
        m_pTblAxleInfo->item(1,nCol)->setTextAlignment(Qt::AlignCenter);
        nCol++;
        if (nCol > MAX_INPUT_AXIS_COUNT-3/*原来是7*/){
            //最多显示到7个轴组
            break;
        }
    }


}

//显示当前计重状态为正常
void FormWeight::SetDevStatusNormal(bool bNormal)
{
    m_bWeightDevNormal = bNormal;
    update();
}

//设置车辆外轮廓设备状态
void FormWeight::SetVehicleOutlineDevStatus(int nStatus)
{
    m_nVehicleOutlineDevStatus = nStatus;
    update();
}
