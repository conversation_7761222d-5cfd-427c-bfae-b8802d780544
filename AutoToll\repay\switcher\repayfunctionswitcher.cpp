#include "repayfunctionswitcher.h"
#include "../../log4qt/ilogmsg.h"
#include "../../formrepay.h"
#include "../ui/formrepaynew.h"
#include "../ui/formrepaytypeselect.h"
#include "../../funcmenu.h"
#include "../../dlgmain.h"
#include <QCoreApplication>
#include <QDir>
#include <QTimer>
#include <QMutexLocker>

RepayFunctionSwitcher* RepayFunctionSwitcher::m_pInstance = 0;
QMutex RepayFunctionSwitcher::m_mutex;

RepayFunctionSwitcher::RepayFunctionSwitcher(QObject *parent)
    : QObject(parent)
    , m_bUseNewFunction(true)  // 默认使用新版功能
    , m_bAllowRuntimeSwitch(true)
    , m_bSwitchNotificationEnabled(true)
    , m_bInitialized(false)
    , m_pConfigWatcher(0)
    , m_pRepayConfig(0)
    , m_pRepayForm(0)
{
}

RepayFunctionSwitcher::~RepayFunctionSwitcher()
{
    // 清理当前补费流程控制器
    if (m_pRepayForm) {
        delete m_pRepayForm;
        m_pRepayForm = 0;
    }
}

RepayFunctionSwitcher* RepayFunctionSwitcher::GetInstance()
{
    if (m_pInstance == 0) {
        QMutexLocker locker(&m_mutex);
        if (m_pInstance == 0) {
            m_pInstance = new RepayFunctionSwitcher();
        }
    }
    return m_pInstance;
}

bool RepayFunctionSwitcher::Initialize()
{
    if (m_bInitialized) {
        return true;
    }
    
    try {
        // 获取配置管理器
        m_pRepayConfig = RepayConfig::GetInstance();
        if (!m_pRepayConfig) {
            ErrorLog("获取补费配置管理器失败");
            return false;
        }
        
        // 读取配置
        ReloadConfig();
        
        // 设置配置变更监听
        connect(m_pRepayConfig, SIGNAL(ConfigChanged(QString,QString,QVariant)),
                this, SLOT(OnConfigChanged(QString,QString,QVariant)));
        
        // 监控配置文件变化
        m_pConfigWatcher = new QFileSystemWatcher(this);
        QString configPath = QCoreApplication::applicationDirPath() + "/lane.ini";
        if (QFile::exists(configPath)) {
            m_pConfigWatcher->addPath(configPath);
                    connect(m_pConfigWatcher, SIGNAL(fileChanged(QString)),
                this, SLOT(OnConfigFileChanged(QString)));
        }
        
        m_bInitialized = true;
        InfoLog("功能切换控制器初始化成功");
        
        return true;
    }
    catch (...) {
        ErrorLog("功能切换控制器初始化异常");
        return false;
    }
}

bool RepayFunctionSwitcher::IsUseNewRepayFunction() const
{
    return m_bUseNewFunction;
}

bool RepayFunctionSwitcher::SetUseNewRepayFunction(bool useNew)
{
    if (!m_bAllowRuntimeSwitch) {
        emit SwitchNotification("运行时切换已禁用，请重启系统后生效");
        WarnLog("运行时切换功能已禁用");
        return false;
    }
    
    if (!ValidateSwitch(useNew)) {
        emit SwitchNotification("功能切换验证失败");
        ErrorLog("功能切换验证失败");
        return false;
    }
    
    bool oldValue = m_bUseNewFunction;
    m_bUseNewFunction = useNew;
    
    // 保存配置
    if (m_pRepayConfig) {
        m_pRepayConfig->SetUseNewRepayFunction(useNew);
    }
    
    // 显示切换通知
    if (m_bSwitchNotificationEnabled) {
        ShowSwitchNotification(useNew);
    }
    
    // 发送信号
    if (oldValue != useNew) {
        emit RepayFunctionChanged(useNew);
        emit SwitchCompleted(true, QString("功能切换成功"));
        InfoLog(QString("补费功能已切换：%1 → %2")
                .arg(oldValue ? "新版" : "旧版")
                .arg(useNew ? "新版" : "旧版"));
    }
    
    return true;
}

void RepayFunctionSwitcher::ReloadConfig()
{
    if (!m_pRepayConfig) {
        return;
    }
    
    m_bUseNewFunction = m_pRepayConfig->IsUseNewRepayFunction();
    m_bAllowRuntimeSwitch = m_pRepayConfig->IsAllowRuntimeSwitch();
    m_bSwitchNotificationEnabled = m_pRepayConfig->IsSwitchNotificationEnabled();
    
    DebugLog(QString("功能切换配置已重新加载：使用新版=%1，允许运行时切换=%2")
             .arg(m_bUseNewFunction ? "是" : "否")
             .arg(m_bAllowRuntimeSwitch ? "是" : "否"));
}

void RepayFunctionSwitcher::HandleRepayKeyEvent(const CVehInfo &vehInfo)
{
    if (!m_bInitialized) {
        ErrorLog("功能切换控制器未初始化");
        return;
    }
    
    // 检查车辆信息
//    if (vehInfo.IsVLPEmpty()) {
//        if (GetMainDlg()) {
//            GetMainDlg()->ShowPromptMsg(QString("请输入补费车辆信息"));
//        }
//        WarnLog("补费操作：车辆信息为空");
//        return;
//    }
    
    DebugLog(QString("处理补费按键事件：车牌=%1，使用新版功能=%2")
             .arg(QString(vehInfo.szVehPlate))
             .arg(m_bUseNewFunction ? "是" : "否"));
    
    // 根据配置选择功能版本
    if (m_bUseNewFunction) {
        CallNewRepayFunction(vehInfo);
    } else {
        CallLegacyRepayFunction(vehInfo);
    }
}

QString RepayFunctionSwitcher::GetCurrentFunctionName() const
{
    return m_bUseNewFunction ? "新版补费功能" : "现有补费功能";
}

bool RepayFunctionSwitcher::ValidateSwitch(bool useNewFunction) const
{
    // 基本验证：检查配置是否有效
    if (!m_pRepayConfig) {
        return false;
    }
    
    // 可以在这里添加更多的验证逻辑
    // 比如检查新功能的依赖模块是否可用等
    
    return true;
}

void RepayFunctionSwitcher::OnConfigFileChanged()
{
    InfoLog("检测到配置文件变化，重新加载补费功能配置");
    
    // 延时重新加载，避免文件正在写入时读取
    QTimer::singleShot(500, this, SLOT(OnDelayedReload()));
}

void RepayFunctionSwitcher::OnConfigChanged(const QString &section, const QString &key, const QVariant &value)
{
    if (section == "RepaySystem") {
        if (key == "UseNewRepayFunction") {
            bool useNew = value.toBool();
            if (useNew != m_bUseNewFunction) {
                m_bUseNewFunction = useNew;
                emit RepayFunctionChanged(useNew);
                InfoLog(QString("补费功能版本配置已变更：%1").arg(useNew ? "新版" : "旧版"));
            }
        }
        else if (key == "AllowRuntimeSwitch") {
            m_bAllowRuntimeSwitch = value.toBool();
            DebugLog(QString("运行时切换配置已变更：%1").arg(m_bAllowRuntimeSwitch ? "允许" : "禁用"));
        }
        else if (key == "SwitchNotificationEnabled") {
            m_bSwitchNotificationEnabled = value.toBool();
            DebugLog(QString("切换通知配置已变更：%1").arg(m_bSwitchNotificationEnabled ? "启用" : "禁用"));
        }
    }
}

void RepayFunctionSwitcher::ShowSwitchNotification(bool useNewFunction)
{
    QString message = QString("补费功能已切换到：%1").arg(useNewFunction ? "新版" : "现有版本");
    
    if (GetMainDlg()) {
        GetMainDlg()->ShowPromptMsg(message);
    }
    
    emit SwitchNotification(message);
    InfoLog(message);
}

void RepayFunctionSwitcher::CallLegacyRepayFunction(const CVehInfo &vehInfo)
{
    try {
        InfoLog("调用现有补费功能");
        
        // 调用现有补费功能
        FormRepay frmRepay;  // 现有补费界面
        int nRlt = CFuncMenu::DoRepayMenu();  // 现有菜单
        if (0 == nRlt) {
            DebugLog("用户取消补费操作");
            return;
        }
        
        DebugLog(QString("现有补费菜单选择：%1").arg(nRlt));
        frmRepay.RepayMoney(vehInfo, nRlt);
    }
    catch (...) {
        ErrorLog("调用现有补费功能时发生异常");
    }
}

void RepayFunctionSwitcher::CallNewRepayFunction(const CVehInfo &vehInfo)
{
    try {
        InfoLog("调用新版补费功能");
        
        // 先进行班长授权验证（在选择补费类型之前）
        InfoLog("开始班长授权验证");
        
        FormAuthorization authForm(GetMainDlg());
        QString operatorId, operatorName;
        bool authResult = authForm.DoAuthorization(operatorId, operatorName);
        
        if (!authResult) {
            InfoLog("班长授权验证失败或用户取消");
            if (GetMainDlg()) {
                GetMainDlg()->ShowPromptMsg(QString("授权验证失败，操作取消"));
            }
            return;
        }
        
        InfoLog(QString("班长授权验证成功 - 操作员:%1(%2)").arg(operatorName).arg(operatorId));
        
        // 授权成功后，使用独立页面选择补费类型（替代旧的菜单方式）
        RepayType selectedType = RepayType_None;
        InterceptType selectedInterceptType = Intercept_Exit; // 默认出口拦截
        
        {
            FormRepayTypeSelect typeSelect(GetMainDlg());
            typeSelect.InitUI();
            bool bSel = typeSelect.ShowSelect(selectedType);
            if (!bSel || selectedType == RepayType_None) {
                DebugLog("用户取消补费操作");
                return;
            }
        }
        
        // 如果选择省内名单补费，立即显示拦截方式选择界面
        if (selectedType == RepayType_Province) {
            FormInterceptSelect interceptForm(GetMainDlg());
            interceptForm.InitUI();
            // 提示信息控件已移除，无需设置
            interceptForm.SetDefaultSelection(Intercept_Exit);
            
            if (!interceptForm.ShowInterceptSelect(selectedInterceptType)) {
                InfoLog("用户取消拦截方式选择");
                return;
            }
            
            InfoLog(QString("用户选择拦截方式 - 车牌:%1, 拦截类型:%2")
                    .arg(QString(vehInfo.szVehPlate)).arg(static_cast<int>(selectedInterceptType)));
        }
        
        int nRepayType = static_cast<int>(selectedType);
        DebugLog(QString("新版补费类型选择：%1").arg(nRepayType));
        
        // 清理之前的补费流程控制器（如果存在）
        if (m_pRepayForm) {
            delete m_pRepayForm;
            m_pRepayForm = 0;
        }
        
        // 创建新版补费流程控制器（成员变量管理生命周期）
        m_pRepayForm = new FormRepayNew(GetMainDlg());
        m_pRepayForm->InitUI();
        
        // 设置补费类型
        m_pRepayForm->SetRepayType(static_cast<RepayType>(nRepayType));
        
        // 设置车辆信息
        m_pRepayForm->SetVehicleInfo(QString(vehInfo.szVehPlate), vehInfo.nVehPlateColor, vehInfo.VehClass);
        
        // 如果是省内名单补费，设置拦截方式
        if (selectedType == RepayType_Province) {
            m_pRepayForm->SetInterceptType(selectedInterceptType);
        }
        
        // 启动补费流程（不显示界面，纯流程控制器）
        if (!m_pRepayForm->StartRepayProcess()) {
            ErrorLog("启动新版补费流程失败");
            if (GetMainDlg()) {
                GetMainDlg()->ShowPromptMsg(QString("补费流程启动失败"),false,true);
            }
            // 失败时清理
            delete m_pRepayForm;
            m_pRepayForm = 0;
        }
        // 注意：FormRepayNew作为纯流程控制器，会自动管理各个步骤的界面显示
        // FormRepaySuccess等界面会在适当时机自动弹出
    }
    catch (...) {
        ErrorLog("调用新版补费功能时发生异常");
        if (GetMainDlg()) {
            GetMainDlg()->ShowPromptMsg(QString("补费功能异常"),false,true);
        }
    }
}

void RepayFunctionSwitcher::OnDelayedReload()
{
    // 重新加载RepayConfig配置
    if (m_pRepayConfig) {
        m_pRepayConfig->ReloadConfig();
    }
    
    // 重新加载本地配置
    ReloadConfig();
} 
