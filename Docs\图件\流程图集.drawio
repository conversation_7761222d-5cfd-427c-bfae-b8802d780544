<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="28.0.9" pages="4">
  <diagram name="ETC最小费额总体流程" id="ppr2lVGI6lqeU4_qDw4P">
    <mxGraphModel dx="1701" dy="932" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="PIpVFty-j-eXB5_ukM9O-10" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-1" target="PIpVFty-j-eXB5_ukM9O-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-1" value="开始计费" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="220" y="220" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-11" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-2" target="PIpVFty-j-eXB5_ukM9O-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-2" value="计算累计金额-OBU内金额累加出口承载门架金额" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="210" y="300" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-3" target="PIpVFty-j-eXB5_ukM9O-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-14" value="大于" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="PIpVFty-j-eXB5_ukM9O-13">
          <mxGeometry x="-0.0545" y="1" relative="1" as="geometry">
            <mxPoint x="-1" y="14" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-3" target="PIpVFty-j-eXB5_ukM9O-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-17" value="等于" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="PIpVFty-j-eXB5_ukM9O-16">
          <mxGeometry x="0.0756" y="-1" relative="1" as="geometry">
            <mxPoint x="1" y="9" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-3" value="通行省份个数与1比较" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="190" y="400" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-4" target="PIpVFty-j-eXB5_ukM9O-8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-4" target="PIpVFty-j-eXB5_ukM9O-7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-25" value="范围内" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="PIpVFty-j-eXB5_ukM9O-24">
          <mxGeometry x="-0.5685" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-4" target="PIpVFty-j-eXB5_ukM9O-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-27" value="低于下限" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="PIpVFty-j-eXB5_ukM9O-26">
          <mxGeometry x="-0.521" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-4" value="多省最小费额比较" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="50" y="515" width="170" height="80" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-5" target="PIpVFty-j-eXB5_ukM9O-9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-19" value="低于下限" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="PIpVFty-j-eXB5_ukM9O-18">
          <mxGeometry x="-0.2638" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-5" target="PIpVFty-j-eXB5_ukM9O-8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="BXqFqZHXqs7Eu7j1vd-T-1" value="&lt;span style=&quot;font-size: 14px;&quot;&gt;大于上限&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="PIpVFty-j-eXB5_ukM9O-29">
          <mxGeometry x="0.3072" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-5" target="PIpVFty-j-eXB5_ukM9O-7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-31" value="范围内" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="PIpVFty-j-eXB5_ukM9O-30">
          <mxGeometry x="-0.3945" relative="1" as="geometry">
            <mxPoint y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-5" value="本省最小费额比较" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="334" y="515" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-6" value="最小费额计费" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="236" y="790" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-7" value="通行介质计费" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="236" y="720" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-8" value="人工处理" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="236" y="660" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-9" target="PIpVFty-j-eXB5_ukM9O-7">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="530" y="735" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-33" value="U行且小于等于半小时" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="PIpVFty-j-eXB5_ukM9O-32">
          <mxGeometry x="-0.6311" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-9" target="PIpVFty-j-eXB5_ukM9O-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-35" value="U形且大于半小时" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="PIpVFty-j-eXB5_ukM9O-34">
          <mxGeometry x="-0.6802" y="-2" relative="1" as="geometry">
            <mxPoint y="37" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-9" value="U行车判断" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="530" y="580" width="100" height="80" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="p6qy0nVzEr5RFLcJAGII" name="ETC省内最小费额判断流程">
    <mxGraphModel dx="1701" dy="932" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="ms3-y0-yjPpRuq_poWer-1" target="h6s7oNqBzTRFwPy9OA7W-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ms3-y0-yjPpRuq_poWer-1" value="开始" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="330" y="50" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="ms3-y0-yjPpRuq_poWer-2">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="380" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ms3-y0-yjPpRuq_poWer-2" value="获取特殊参数省内最小费额上下限" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="210" y="250" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="h6s7oNqBzTRFwPy9OA7W-1" target="ms3-y0-yjPpRuq_poWer-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-3" value="存在" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="h6s7oNqBzTRFwPy9OA7W-2">
          <mxGeometry x="0.26" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="h6s7oNqBzTRFwPy9OA7W-1" target="h6s7oNqBzTRFwPy9OA7W-7">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="490" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-6" value="不存在" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="h6s7oNqBzTRFwPy9OA7W-5">
          <mxGeometry x="0.22" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-1" value="特殊参数110是否存在" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="300" y="110" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="h6s7oNqBzTRFwPy9OA7W-4" target="h6s7oNqBzTRFwPy9OA7W-13">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-4" value="采用默认值（2倍作为上限，1倍作为下限）" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="354" y="250" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="h6s7oNqBzTRFwPy9OA7W-7" target="h6s7oNqBzTRFwPy9OA7W-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-9" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="h6s7oNqBzTRFwPy9OA7W-8">
          <mxGeometry x="-0.542" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="h6s7oNqBzTRFwPy9OA7W-7" target="h6s7oNqBzTRFwPy9OA7W-11">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="640" y="260" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-12" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="h6s7oNqBzTRFwPy9OA7W-10">
          <mxGeometry x="-0.13" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-7" value="是否为本省警车" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="510" y="110" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="h6s7oNqBzTRFwPy9OA7W-11" target="h6s7oNqBzTRFwPy9OA7W-13">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-11" value="采用车型最大值作为上限，1倍作为下限" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="520" y="250" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="h6s7oNqBzTRFwPy9OA7W-13" target="h6s7oNqBzTRFwPy9OA7W-14">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-24" value="低于下限" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="h6s7oNqBzTRFwPy9OA7W-23">
          <mxGeometry x="0.0964" y="1" relative="1" as="geometry">
            <mxPoint x="-4" y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="h6s7oNqBzTRFwPy9OA7W-13" target="h6s7oNqBzTRFwPy9OA7W-15">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-26" value="范围内" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="h6s7oNqBzTRFwPy9OA7W-25">
          <mxGeometry x="-0.0743" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="h6s7oNqBzTRFwPy9OA7W-13" target="h6s7oNqBzTRFwPy9OA7W-16">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-28" value="超过范围" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="h6s7oNqBzTRFwPy9OA7W-27">
          <mxGeometry x="-0.28" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-13" value="最小费额比较" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="300" y="350" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-14" value="返回低于下限" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="540" y="375" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-15" value="返回正常" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="130" y="375" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="h6s7oNqBzTRFwPy9OA7W-16" target="h6s7oNqBzTRFwPy9OA7W-17">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-30" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="h6s7oNqBzTRFwPy9OA7W-29">
          <mxGeometry x="-0.1649" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-31" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="h6s7oNqBzTRFwPy9OA7W-16" target="h6s7oNqBzTRFwPy9OA7W-18">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-32" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="h6s7oNqBzTRFwPy9OA7W-31">
          <mxGeometry relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-16" value="是否在路径识别参数内" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="300" y="480" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-33" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="h6s7oNqBzTRFwPy9OA7W-17" target="h6s7oNqBzTRFwPy9OA7W-18">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-34" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="h6s7oNqBzTRFwPy9OA7W-33">
          <mxGeometry x="0.2857" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-38" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.25;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="h6s7oNqBzTRFwPy9OA7W-17" target="h6s7oNqBzTRFwPy9OA7W-35">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-39" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="h6s7oNqBzTRFwPy9OA7W-38">
          <mxGeometry x="-0.1849" y="-1" relative="1" as="geometry">
            <mxPoint x="-58" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-17" value="调用在线接口查询&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;是&lt;/span&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;否在路径识别参数内&lt;/span&gt;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="510" y="595" width="190" height="90" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="h6s7oNqBzTRFwPy9OA7W-18" target="h6s7oNqBzTRFwPy9OA7W-35">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-37" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="h6s7oNqBzTRFwPy9OA7W-36">
          <mxGeometry x="0.32" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-40" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="h6s7oNqBzTRFwPy9OA7W-18" target="h6s7oNqBzTRFwPy9OA7W-15">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-41" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="h6s7oNqBzTRFwPy9OA7W-40">
          <mxGeometry x="-0.7126" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-18" value="是否超过车型最大收费额" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="280" y="600" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="h6s7oNqBzTRFwPy9OA7W-35" value="返回超过上限" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="330" y="720" width="100" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="_HaTU4GgGWrAflIRQlR2" name="CPC最小费额总体流程">
    <mxGraphModel dx="1701" dy="932" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="MBZoG-Q29aWumDogZxLu-1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=14;" edge="1" parent="1" source="MBZoG-Q29aWumDogZxLu-31" target="MBZoG-Q29aWumDogZxLu-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-33" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="MBZoG-Q29aWumDogZxLu-1">
          <mxGeometry x="-0.64" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-2" value="开始计费" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="270" y="70" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=14;" edge="1" parent="1" source="MBZoG-Q29aWumDogZxLu-4" target="MBZoG-Q29aWumDogZxLu-9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-4" value="计算累计金额=CPC内金额累加出口承载门架金额" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="260" y="250" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="MBZoG-Q29aWumDogZxLu-9" target="MBZoG-Q29aWumDogZxLu-16">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-6" value="大于" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="MBZoG-Q29aWumDogZxLu-5">
          <mxGeometry x="-0.0545" y="1" relative="1" as="geometry">
            <mxPoint x="-1" y="14" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="MBZoG-Q29aWumDogZxLu-9" target="MBZoG-Q29aWumDogZxLu-22">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-8" value="等于" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="MBZoG-Q29aWumDogZxLu-7">
          <mxGeometry x="0.0756" y="-1" relative="1" as="geometry">
            <mxPoint x="1" y="9" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-9" value="通行省份个数与1比较" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="240" y="350" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="MBZoG-Q29aWumDogZxLu-16" target="MBZoG-Q29aWumDogZxLu-25">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-11" value="大于上限" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="MBZoG-Q29aWumDogZxLu-10">
          <mxGeometry x="0.3099" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="MBZoG-Q29aWumDogZxLu-16" target="MBZoG-Q29aWumDogZxLu-24">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-13" value="范围内" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="MBZoG-Q29aWumDogZxLu-12">
          <mxGeometry x="-0.5685" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="MBZoG-Q29aWumDogZxLu-16" target="MBZoG-Q29aWumDogZxLu-23">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-15" value="低于下限" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="MBZoG-Q29aWumDogZxLu-14">
          <mxGeometry x="-0.521" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-16" value="多省最小费额比较" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="100" y="465" width="170" height="80" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="MBZoG-Q29aWumDogZxLu-22" target="MBZoG-Q29aWumDogZxLu-30">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-18" value="低于下限" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="MBZoG-Q29aWumDogZxLu-17">
          <mxGeometry x="-0.2638" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="MBZoG-Q29aWumDogZxLu-22" target="MBZoG-Q29aWumDogZxLu-25">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="MBZoG-Q29aWumDogZxLu-22" target="MBZoG-Q29aWumDogZxLu-24">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-21" value="范围内" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="MBZoG-Q29aWumDogZxLu-20">
          <mxGeometry x="-0.3945" relative="1" as="geometry">
            <mxPoint y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-22" value="本省最小费额比较" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="384" y="465" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-23" value="在线计费" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="286" y="740" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-24" value="通行介质计费" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="286" y="670" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-25" value="在线计费" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="286" y="610" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="MBZoG-Q29aWumDogZxLu-30" target="MBZoG-Q29aWumDogZxLu-24">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="580" y="685" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-27" value="U行且小于等于半小时" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="MBZoG-Q29aWumDogZxLu-26">
          <mxGeometry x="-0.6311" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="MBZoG-Q29aWumDogZxLu-30" target="MBZoG-Q29aWumDogZxLu-23">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-29" value="U形且大于半小时" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="MBZoG-Q29aWumDogZxLu-28">
          <mxGeometry x="-0.6802" y="-2" relative="1" as="geometry">
            <mxPoint y="37" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-30" value="U行车判断" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="580" y="530" width="100" height="80" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-32" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=14;" edge="1" parent="1" source="MBZoG-Q29aWumDogZxLu-2" target="MBZoG-Q29aWumDogZxLu-31">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="320" y="100" as="sourcePoint" />
            <mxPoint x="320" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-35" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="MBZoG-Q29aWumDogZxLu-31" target="MBZoG-Q29aWumDogZxLu-34">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-36" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="MBZoG-Q29aWumDogZxLu-35">
          <mxGeometry x="0.0816" y="-3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-31" value="门架交易成功&lt;div&gt;次数&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;是否为1&lt;/span&gt;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="240" y="130" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="MBZoG-Q29aWumDogZxLu-34" value="在线计费" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="470" y="155" width="100" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="mJMdHLrdl-ge_AUKa5oH" name="CPC省内最小费额判断流程">
    <mxGraphModel dx="1701" dy="932" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="yiKJ6_qHLGaUKiI-dime-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="yiKJ6_qHLGaUKiI-dime-2" target="yiKJ6_qHLGaUKiI-dime-9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-2" value="开始" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="300" y="100" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="yiKJ6_qHLGaUKiI-dime-4">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="350" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-4" value="获取特殊参数省内最小费额上下限" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="180" y="300" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="yiKJ6_qHLGaUKiI-dime-9" target="yiKJ6_qHLGaUKiI-dime-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-6" value="存在" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="yiKJ6_qHLGaUKiI-dime-5">
          <mxGeometry x="0.26" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="yiKJ6_qHLGaUKiI-dime-9" target="yiKJ6_qHLGaUKiI-dime-16">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="460" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-8" value="不存在" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="yiKJ6_qHLGaUKiI-dime-7">
          <mxGeometry x="0.22" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-9" value="特殊参数112是否存在" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="270" y="160" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="yiKJ6_qHLGaUKiI-dime-11" target="yiKJ6_qHLGaUKiI-dime-25">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-11" value="采用默认值（1.5倍作为上限，1倍作为下限）" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="324" y="300" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="yiKJ6_qHLGaUKiI-dime-16" target="yiKJ6_qHLGaUKiI-dime-11">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-13" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="yiKJ6_qHLGaUKiI-dime-12">
          <mxGeometry x="-0.542" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="yiKJ6_qHLGaUKiI-dime-16" target="yiKJ6_qHLGaUKiI-dime-18">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="610" y="310" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-15" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="yiKJ6_qHLGaUKiI-dime-14">
          <mxGeometry x="-0.13" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-16" value="是否为本省警车" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="480" y="160" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="yiKJ6_qHLGaUKiI-dime-18" target="yiKJ6_qHLGaUKiI-dime-25">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-18" value="采用车型最大值作为上限，1倍作为下限" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="490" y="300" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="yiKJ6_qHLGaUKiI-dime-25" target="yiKJ6_qHLGaUKiI-dime-26">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-20" value="低于下限" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="yiKJ6_qHLGaUKiI-dime-19">
          <mxGeometry x="0.0964" y="1" relative="1" as="geometry">
            <mxPoint x="-4" y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="yiKJ6_qHLGaUKiI-dime-25" target="yiKJ6_qHLGaUKiI-dime-27">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-22" value="范围内" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="yiKJ6_qHLGaUKiI-dime-21">
          <mxGeometry x="-0.0743" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="yiKJ6_qHLGaUKiI-dime-25" target="yiKJ6_qHLGaUKiI-dime-32">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-24" value="超过范围" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="yiKJ6_qHLGaUKiI-dime-23">
          <mxGeometry x="-0.28" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-25" value="最小费额比较" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="270" y="400" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-26" value="返回低于下限" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="510" y="425" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-27" value="返回正常" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="100" y="425" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="yiKJ6_qHLGaUKiI-dime-32" target="yiKJ6_qHLGaUKiI-dime-43">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="575" y="645" as="targetPoint" />
            <Array as="points">
              <mxPoint x="520" y="570" />
              <mxPoint x="520" y="785" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-29" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="yiKJ6_qHLGaUKiI-dime-28">
          <mxGeometry x="-0.1649" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="yiKJ6_qHLGaUKiI-dime-32" target="yiKJ6_qHLGaUKiI-dime-42">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-31" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="yiKJ6_qHLGaUKiI-dime-30">
          <mxGeometry relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-32" value="是否在路径识别参数内" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="270" y="530" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-38" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="yiKJ6_qHLGaUKiI-dime-42" target="yiKJ6_qHLGaUKiI-dime-43">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-39" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="yiKJ6_qHLGaUKiI-dime-38">
          <mxGeometry x="0.32" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-40" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="yiKJ6_qHLGaUKiI-dime-42" target="yiKJ6_qHLGaUKiI-dime-27">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-41" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="yiKJ6_qHLGaUKiI-dime-40">
          <mxGeometry x="-0.7126" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-42" value="是否超过车型最大收费额" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="250" y="650" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="yiKJ6_qHLGaUKiI-dime-43" value="返回超过上限" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="300" y="770" width="100" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
