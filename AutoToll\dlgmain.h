#ifndef DLGMAIN_H
#define DLGMAIN_H

#include <QDate>
#include <QDialog>
#include <QEvent>
#include <QList>
#include <QMutexLocker>

#include "baseopwidget.h"
#include "cpsamdlg.h"
#include "devices/IOCard.h"
#include "devices/videocard.h"
#include "formdevstatus.h"
#include "formetctradeinfo.h"
#include "formextrainfo.h"
#include "formfaredisplayer.h"
#include "formhistory.h"
#include "formlaneinfo.h"
#include "formloading.h"
#include "formlog.h"
#include "formparamversion.h"
#include "formpromptmsg.h"
#include "formqrcode.h"
#include "formunlogin.h"
#include "formvehimage.h"
#include "formvehinfo.h"
#include "formvehmoney.h"
#include "formvehqueue.h"
#include "formvideocard.h"
#include "formweight.h"
#include "laneconfigdlg.h"
#include "laneserver.h"
#include "messagebox.h"
#include "motorcadefm.h"
#include "ntpclient.h"
#include "paraminfodlg.h"
#include "remoteserver.h"
#include "rsudataprocessor.h"
#include "sernum.h"
#include "sernumfm.h"
#include "shiftmgr.h"
#include "totalfm.h"
#include "transinfo.h"
#include "vehplate.h"
#include "repay/switcher/repayfunctionswitcher.h"

#ifndef Q_WS_WIN
#define WM_USER 1024
#endif
//车辆识别消息
#define WM_VPR_DETECT (WM_USER + 2)

class QDlgMain : public QDialog
{
    Q_OBJECT
public:
    explicit QDlgMain(QWidget *parent = 0);
    ~QDlgMain();

private:
    /*************各子窗口定义******************/
    //设备状态窗口
    FormDevStatus *m_pFrmDevStatus;
    //最上面程序名称，收费站信息窗口
    FormLaneInfo *m_pFrmLaneInfo;
    //视频采集卡窗口
    FormVideoCard *m_pFrmVideoCard;
    //参数状态窗口
    FormParamVersion *m_pFrmParamVer;
    //车辆队列示意窗口
    FormVehQueue *m_pFrmVehQueue;
    //抓拍图像窗口
    FormVehImage *m_pFrmVehImage;
    // ETC交易记录
    FormETCTradeInfo *m_pFrmEtcInfo[2];
    //日志窗口
    FormLog *m_pFrmLog;
    //交易流水窗口
    FormHistory *m_pFrmHistory;
    //费显窗口
    FormFareDisplayer *m_pFrmFareDisp;
    //提示窗口
    FormPromptMsg *m_pFrmPromptMsg;
    //计重窗口
    FormWeight *m_pFrmWeight;
    //票号窗口
    FormExtraInfo *m_pFrmExtraInfo;
    //二维码窗口
    FormQRCode *m_pFrmQRCode;

    //  qint32 m_TimerShowID;
    /*状态机窗口*/
    //车辆信息输入窗口
    FormVehInfo *m_pFrmVehInfo;
    CMotorCadeFm *motorCadeFm;
    //下班状态窗口
    FormUnLogin *m_pFrmUnlogin;
    //显示收费金额窗口
    FormVehMoney *m_pFrmVehMoney;

    //远程控制关闭按钮
    QPushButton *m_pBtnCloseRemoteControl;

    /********************************/
    //正在加载窗口
    FormLoading *m_pFrmLoading;

    CPSAMDlg *m_pPsamDlg;
    // SerNumFm *Sernumfm;
    QStackedWidget *m_FrmsMgr;
    Cmessagelabel *m_pMsgLbl;

    bool m_bAppInitOk;
    int m_nExitAppTimerId;
    CLaneServer m_LaneServer;
    CParamInfoDlg *m_pParamInfoDlg;
    CLaneConfigDlg *m_pLaneConfigDlg;

    //参数定时下载定时器
    qint32 m_TimerReqParams;
    //参数下载定时器
    qint32 m_TimerReqParamsLf;
    //界面时钟ID
    qint32 m_TimerOutUseTimeParam;
    // 30s检查一次参数是否到期
    qint32 m_nLoadParamNum;
    //校时时钟
    qint32 m_TimerAskTimeID;
    //心跳报文时钟
    qint32 m_TimeHeadID;

    //打印纸纸状态定时器
    qint32 m_TimerPrintPaper;
    //前次打印纸状态
    int m_nLastPaperStatus;

    qint32 m_TimerShowLog;
    SerNum *ser;
    QString m_curtime;
    QTimer *m_pFlashQueTimer;
    QTimer m_tLoginTimer;
    QTimer m_activeTimer;
    QTimer m_CheckShiftTimer;  //班次检测定时器
    QTimer m_HeadTimer;
    qint32 m_nKeyBTimes;  // B键连按次数
    QTimer m_KeyBClearTimer;
    QTimer m_UploadLogTimer;  //日志上传定时器，已停用
    QTimer m_ExitTimer;       //系统关闭定时器
    int m_nExitCode;
    //显示前队列车辆定时器
    QTimer m_UpdateFronVehQueTimer;
    //常规10秒定时器，用来检测内存，硬盘等信息
    QTimer m_Routine10SecTimer;

    QMutex m_waitVehListTex;

    QMutex m_LogMutex;
    QStringList m_sLogList;

    NTPClient *ntpClient;

    QTimer m_PSAMSignTimer;  //这个定时器没启用？
    QTimer m_PrintLogTimer;

    QMutex m_VLPRNumMutex;

    QString m_sVLPRHourBatchNo;
    quint32 m_VehicleDataCount;
    quint32 m_VehiclePicCount;

    QTimer m_VLPRSumTimer;
    QMutex m_StayOutMt;
    CMessageBox *m_pStayOutTimeDlg;
    bool m_bStayOut;
    qint64 m_lAppStartTime;
    QThread *m_pProcessorThreads[2];

    /**记录车牌识别产生的图像名称*/
    QStringList m_lstShowImgs;

    QrCodeInfo_Lane m_qrCodeInfo;

public:
    enum
    {
        ETCDisplayType_Clear,
        ETCDisplayType_ShowMoney,
        ETCDisplayType_ShowOBU,
        ETCDisplayType_ShowSpEvent,
        ETCDisplayType_SetVehInfo,
        ETCDisplayType_ShowEntryInfo,
        ETCDisplayType_ShowCardInfo,
        ETCDisplayType_ShowPromptInfo,
        ETCDisplayType_ShowTransInfo,
    };
signals:
    void NotifyETCDisplayEvent(int nIndex, int nType, QList<QVariant> ParamList);
    //通知显示提示信息（使用信号，保证线程安全）
    void NotifyShowPromptEvent(const QString &promptMsg, bool bWarnFlag, bool bAutoClear = false);
    void NotifyClearVehFrmEvent(bool bClearTrans);
    void NotifyShowQrCode();
private slots:
    void OnLaneStatusChanedEvent();
    void OnWorkShiftChangedEvent(const CShiftSumInfo OldShiftSumInfo,
                                 const CShiftSumInfo newShiftSumInfo, bool bAutoLogin);
    void NotifyShiftEnd(CShiftSumInfo ShiftSumInfo, bool bAutoLogin);
    void SetWindowActivity();
    void OnChangeToUnloginState();
    void OnKeyBClear();
    void OnClearVehFrmEvent(bool bClearTrans);
    void OnShowQrCode();

public slots:
    // 票号变化
    void OnInvoiceChange(quint64 llCode, quint64 llStartNo, quint32 dwCount);
    void OnPaperNoChange(quint64 lCurNo, int nCnt, QString sTime, QString sEndTime);

    void OnDevStatusChange(qint32 nDevId, qint32 nDevStatus);
    void UpdateVehState();
    void OnIOInputChanged(quint32 dwOldInput, quint32 dwNewInput, quint8 bLoopStatus,
                          QString sLoopStatus);
    void OnDIStatusChanged(int nDI, bool bStatus, quint8 bLoopStatus, QString sLoopStatus);
    void OnDOStatusChanged(int nDO, int bStatus);
    void OnLockBar(bool bLockBar);
    //网络状态变化
    void OnNetWorkChanged(qint32 nStatus);

    //车牌识别
    void OnVPRDevEvent(CVPRResult *pRlt);

    void OnCaptureEvent(QString sFileName);
    void OnSaveDataFailedEvent(int nMsgType, quint32 occurTime, QString sError);

    //交易结束提示

    void OnTransCompleteETC(bool bRedo, int nViolateId, CTransInfo *pLastTranInfo);
    void OnRsuState(int nIndex, bool isOK);
    void OnShiftTimer();
    //定时打印系统信息日志
    void OnPrintLog();

    void OnRsuError(int nIndex, quint8 bFrameId, quint8 bErrorCode);
    //过车超时处理
    void OnStayOutTimeEvent(QString sPlate, quint8 bType);
    //处理闯关
    void OnVehInvoiceEvent();

    void OnUploadLog();
    void InitRsuProcessor();
    //初始化移动支付
    bool InitMobilePay(QString &sError);

    void OnLoadNewParamFileMsg(qint32 nCfgFile, CCfgFileHead cfgFileHead, bool bPreLoad);
    //卡机槽函数
    void OnCardMachineEvent(int nEvent, int nPos, int nIndex);

    void OnHelpBtnEvent(int nPos);

    //显示操作员上班时间
    void OnShowOnDutyTime(const QDateTime &dtOnDuty);
    //常规定时任务
    void OnRoutine10SecTime();
    //费显通知显示文字
    void OnFareDisplayerShowText(QString sText, int nRow);
    void OnFlashQue();
    //更新车辆交易队列
    void OnUpdateTradeVehQueue();
    //校时返回
    void OnSetTime(QDateTime dateTime);
    // psam签到
    void OnPSAMSign();
    void OnPsamAuthEvent(int nIndex, int nEvent, int nDevType);
    void SendVlprSumWaste();
    //刷新交易区车辆队列信息（交易完成，未过光栅）
    void RefreshTradeVehQueue();

    //状态机通知更新对应界面
    void OnETCDisplayMsgEvent(int nIndex, int nType, QList<QVariant> ParamList);
    //当收到显示提示信息时，显示提示信息（使用信号，保证线程安全）
    void OnShowPromptMsgEvent(const QString &promptMsg, bool bWarnFlag, bool bAutoClear = false);
    //计重数据更新
    void NotifyWeightDataChange();
    //接收到车型识别数据时
    void OnVcrResultData(unsigned long dwCarID, int nColor, QString sPlate, qint32 vehClass,
                         int nAxisCount);

    void DisplayCurShiftSumInfo();

    void OnExitTimer();

    //中心连接状态
    void OnReomteClientStateChanged(int nState);

    //坐席远控关闭按钮
    void OnBtnCloseRemoteControlClicked(bool checked = false);
    //坐席连接状态
    void OnRemoteConnected(const QString &sIP);
    void OnRemoteDisConnected(const QString &sIP);
    void OnRemoteControlCmd(int nCmd, QString sMsg);

    bool LoadVPRResult();
    bool LoadVCRResult();
    //平板远控进行车辆信息输入
    void OnInputVehInfoByRemote(qint32 vehClass, int nPlateColor, QString sVehPlate, qint32 vehType,
                                QString axleType);
    //平板远控进行支付方式的输入
    void OnSelectPayTypeByRemote(int keyValue);
    //平板远控要求回退到输入状态
    void OnBackInputStateByRemote();

    //远程模拟键落杆
    void OnRemoteSimulateDownBar();

private:
    //初始化界面
    void InitUI(int nLaneType);
    //安装子窗口键盘消息过滤器
    void InstallChildWndKeyEventFilter();

    //根据车道类型初始化界面控件
    void InitUIControls(qint32 nLaneType);
    //初始化车道信息区的显示
    void InitLaneInfoArea(qint32 nLaneType);
    //初始化车道状态机
    void InitAllLaneState();

    //显示站名称
    void ShowStationName(const QString &stationname);
    //显示车道名称
    void ShowLaneName(int nLaneType, const QString &lanename);
    //显示收费员编码
    void ShowOperatorId(const QString &operId);
    //显示车道状态
    // TODO 参数转换成编码
    void ShowLaneState(qint32 nLaneStatus);
    //显示费率版本
    void ShowFeeVersion(QString sVersion);
    //显示最小费率版本
    void ShowMinFeeVersion(QString sVersion, QString sNewVersion);
    //显示黑名单版本
    void ShowBlackListVersion(QString sVersion);
    //显示未上传数据数量
    void Shownodatacount(qint32 ncount);
    //显示告警信息
    void ShowWarning(QString sWarning);

    void ShowGantryName(const QString &sGantryName);

    void GetInvoiceInfo(QString &sStartNo, QString &sCount);
    void ShowInvoiceInfo(const QString &sStartNo, const QString &sCount);
    void ConnectAllDevSignal();
    void DisConnectDevSignal();
    bool CheckSer(int nOrgID, int nLaneID);

    void RefreshParamVerInfo();
    bool InitDevice(QString &sError);
    void InitVehicleOutlineDevStatus();
    void StopAllTimer();
    void StartAskTimeTimer();
    // void StartHeartMsg();
    bool LoadMsgFromFile(CJKMsgInfo &jkMsgInfo);
    void SetWindowStaysOnTopHint(bool bWindowStaysOnTopHint);
    //检查打印纸状态
    void CheckPrintPaperStatus();

protected:
    void timerEvent(QTimerEvent *);
    void customEvent(QEvent *event);
    virtual void mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
    //事件过滤器，用来捕获键盘操作
    bool eventFilter(QObject *obj, QEvent *event);
    void closeEvent(QCloseEvent *event);

    // windows事件过滤器
    bool winEvent(MSG *message, long *result);

protected:
    //主窗口中通过定时器更新日志到界面
    void RefreshLog();
    bool OnOpenCardEvent(int nReaderId, int nCardType, bool &bContinueReadCard);
    void OnQrCodeEvent(int nPos, const QString &sQrCode);

public:
    //程序启动
    bool AppStart(QString &sError);
    //一定在InitOrgBasicInfo之后调用
    bool InitStationDoorInfo();

    bool InitOrgBasicInfo();
    bool GetLocalStationinfo(QString &sError);
    void AppDestroy();
    void ExitApp(int nExitCode);

    void ShowAuthDlg();

    //一定在InitStationDoorInfo之后调用
    bool InitOpenStationInfo();
    bool InitOpenStationInfo_New();
    qint32 Init();
    void ShowWasteInfo(CWasteInfo *wasteInfo);

    void StartExitAppTimer();
    //启动班次定时检查定时器，下班时调用，上班时停止
    void StartShiftCheckTimer();
    void StopShiftCheckTimer();
    CParamInfoDlg *GetParamInfoDlg()
    {
        if (!m_pParamInfoDlg) {
            m_pParamInfoDlg = new CParamInfoDlg();
        }
        return m_pParamInfoDlg;
    }

    void ReqParamFun();
    //增加日志
    void ShowLog(const QString sInfo);
    //刷新车辆队列示意图显示
    //刷新通过区车辆队列（过了光栅的待通过）
    void RefreshPassVehQueue();

    void ShowETCVPRPic(QString &spic);
    //显示班次信息
    void ShowShiftInfo(const QDate workdate, const QString &shift);
    void SetMotor(bool bMotor);
    void CheckAndDownLoadParam();
    void SetLastShiftWasteInfo(const QString &shiftName, int entry_exit_waste_num, int gantry_num);
    void SetShiftWasteInfo(int entry_exit_waste_num, const QString &entry_exit_waste_time,
                           int vip_waste_num, const QString &vip_waste_time, int gantry_waste_num,
                           const QString &gantry_waste_time);
    void DisplayLastShiftSumInfo();

    //显示帮助提示
    void ShowPromptMsg(const QString &promptMsg, bool bWarnFlag = false, bool bAutoClear = false);

    //发送更新ETC交易的相关消息（多线程更新界面需要）
    void Emit_ETCShowOBUInfo(int nIndex, const QString &sOBUSn);
    void Emit_ETCShowVehInfo(int nIndex, const CVehInfo &vehInfo);
    void Emit_ETCShowCardInfo(int nIndex, int nCardType, const QString &sCardId, quint32 dwBalance);
    void Emit_ETCShowEntryInfo(int nIndex, const QDateTime &EnTime, const QString &sEnStationName,
                               const QString &sVLP, const QString &sHexId);
    void Emit_ETCShowMoney(int nIndex, quint32 dwMoney);
    //在ETC交易窗口显示提示信息
    void Emit_ETCShowPrompt(int nIndex, const QString &promptMsg);
    // ETC线程清除车辆信息窗口
    void Emit_ClearVehInfoFrm(bool bClearTrans);
    // 发送交易信息
    void Emit_ETCShowTransInfo(int nIndex, const CWasteInfo &wasteInfo);

    //显示抓拍的图像
    void ShowImages(int nVlpColor, const QString &sPlate);

    //显示二维码
    void SetQrCode(QrCodeInfo_Lane *pInfo);
    void ShowQRCode(QrCodeInfo_Lane *pInfo);
    //显示历史二维码图像
    void ShowHistoryQrCodeImage(const QString &vehicleId, QDateTime exTime);
    void ShowHistoryQrCodeImage(const QString &sRecord);
    //更新设备状态显示
    void RefreshDevStatus();
    //设置车道自助或人工判断
    void RefreshLaneInfo();
    // 重新初始化天线
    bool ReinitializeRsu(int nRsuIndex = -1, QString * sError = NULL);
    // 新版本重新初始化天线，保持线程状态
    bool ReinitializeRsuNew(int nRsuIndex = -1, QString * sError = NULL);
    void ShowRsuSelectionDialog();
    bool ReinitializeRsuNew2(int nRsuIndex, QString *sError);
};

QDlgMain *GetMainDlg();

#endif  // DLGMAIN_H
